{"chunk": 1, "numChunks": 3, "fileHash": "7tlNFY9QuuTsIOMeR/sMN3VB67YYk3Cgo2yYtVL/J9U=", "filePath": "pages/wuxing/wuxing.wxss", "content": "  font-size: 28rpx;\n  color: #666;\n  margin-bottom: 20rpx;\n  text-align: center;\n}\n\n.distribution-grid {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.distribution-item {\n  display: flex;\n  align-items: center;\n  gap: 20rpx;\n}\n\n.element-name {\n  width: 80rpx;\n  font-size: 28rpx;\n  color: #333;\n}\n\n.element-strength {\n  width: 80rpx;\n  font-size: 28rpx;\n  color: #666;\n  text-align: right;\n}\n\n.strength-bar {\n  flex: 1;\n  height: 30rpx;\n  background-color: #eee;\n  border-radius: 15rpx;\n  overflow: hidden;\n}\n\n.bar-fill {\n  height: 100%;\n  background-color: #4CAF50;\n  transition: width 0.3s ease;\n}\n\n/* 分析区域样式 */\n.analysis-section {\n  margin-top: 30rpx;\n}\n\n.analysis-item {\n  margin-bottom: 20rpx;\n  padding: 20rpx;\n  background-color: #f9f9f9;\n  border-radius: 8rpx;\n}\n\n.item-title {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: bold;\n  margin-bottom: 10rpx;\n}\n\n.item-content {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n/* 五行关系样式 */\n.relationships-section {\n  margin-top: 30rpx;\n}\n\n.relationships-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 20rpx;\n}\n\n.relationship-item {\n"}