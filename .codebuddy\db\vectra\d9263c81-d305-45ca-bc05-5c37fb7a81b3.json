{"chunk": 9, "numChunks": 10, "fileHash": "lg9EuzAhq20Uogzg9r07DedW1yISmqoEvsYKCcdN8GI=", "filePath": "utils/fengshui/calculator.js", "content": "// 八卦方位数据\nclass FengshuiCalculator {\n  isCompatibleDirection(mainDirection, direction) {\n    // 判断方位是否相容\n    const compatibleMap = {\n      '南': ['东', '西'],\n      '北': ['东', '西'],\n      '东': ['南', '北'],\n      '西': ['南', '北'],\n      '东南': ['东北', '西南'],\n      '西北': ['东北', '西南'],\n      '东北': ['东南', '西北'],\n      '西南': ['东南', '西北']\n    };\n\n    return compatibleMap[mainDirection]?.includes(direction) || mainDirection === direction;\n  }\n  generateLayoutAdvice(auspiciousPositions, area) {\n    const layout = [];\n    const roomTypes = {\n      small: ['卧室', '书房', '卫生间'],\n      medium: ['客厅', '餐厅', '厨房'],\n      large: ['主卧', '活动室']\n    };\n\n    // 根据面积分配房间\n    if (area < 60) {\n      this.addRoomAdvice(layout, roomTypes.small, auspiciousPositions);\n    } else if (area < 100) {\n      this.addRoomAdvice(layout, [...roomTypes.small, ...roomTypes.medium], auspiciousPositions);\n    } else {\n      this.addRoomAdvice(layout, [...roomTypes.small, ...roomTypes.medium, ...roomTypes.large], auspiciousPositions);\n    }\n\n    return layout;\n  }"}