{"chunk": 0, "numChunks": 4, "fileHash": "jubZTzyunGc3ITYrDRZliCEjMyZub9rUjJcBcvHhj28=", "filePath": "pages/name-test/name-test.wxml", "content": "<!--姓名测试页面-->\n<view class=\"page-container\">\n  <view class=\"header\">\n    <view class=\"title\">姓名测试</view>\n    <view class=\"subtitle\">通过五行数理分析姓名</view>\n  </view>\n\n  <!-- 姓名输入区域 -->\n  <view class=\"input-section\">\n    <view class=\"input-group\">\n      <view class=\"label\">姓氏</view>\n      <input \n        class=\"name-input\" \n        type=\"text\" \n        placeholder=\"请输入姓氏\"\n        value=\"{{surname}}\"\n        bindinput=\"onSurnameInput\"\n        maxlength=\"10\"\n      />\n    </view>\n    <view class=\"input-group\">\n      <view class=\"label\">名字</view>\n      <input \n        class=\"name-input\" \n        type=\"text\" \n        placeholder=\"请输入名字\"\n        value=\"{{givenName}}\"\n        bindinput=\"onGivenNameInput\"\n        maxlength=\"10\"\n      />\n    </view>\n    <view class=\"input-group\">\n      <view class=\"label\">性别</view>\n      <radio-group class=\"gender-group\" bindchange=\"onGenderChange\">\n        <label class=\"radio-item\">\n          <radio value=\"male\" checked=\"{{gender === 'male'}}\"/>\n          <text>男</text>\n        </label>\n        <label class=\"radio-item\">\n          <radio value=\"female\" checked=\"{{gender === 'female'}}\"/>\n          <text>女</text>\n        </label>\n      </radio-group>\n    </view>\n    <button class=\"test-btn\" bindtap=\"startNameTest\" disabled=\"{{!canTest}}\">\n      开始测试\n    </button>\n  </view>\n\n  <!-- 加载状态 -->\n  <view class=\"loading-section\" wx:if=\"{{isLoading}}\">\n    <view class=\"loading-icon\">\n      <view class=\"spinner\"></view>\n"}