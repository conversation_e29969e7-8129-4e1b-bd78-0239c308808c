{"chunk": 4, "numChunks": 6, "fileHash": "dNwun7ERRMYazRQ1UOKQO3ggF1ZH6El+O2/hG+Rh3/o=", "filePath": "pages/chat-list/chat-list.wxml", "content": "          <view class=\"action-icon\">📌</view>\n          <text>{{selectedChat.isPinned ? '取消置顶' : '置顶对话'}}</text>\n        </view>\n        <view class=\"action-item\" bindtap=\"toggleFavoriteChat\">\n          <view class=\"action-icon\">⭐</view>\n          <text>{{selectedChat.isFavorite ? '取消收藏' : '收藏对话'}}</text>\n        </view>\n        <view class=\"action-item\" bindtap=\"renameChat\">\n          <view class=\"action-icon\">✏️</view>\n          <text>重命名</text>\n        </view>\n        <view class=\"action-item\" bindtap=\"exportSingleChat\">\n          <view class=\"action-icon\">📋</view>\n          <text>导出记录</text>\n        </view>\n        <view class=\"action-item danger\" bindtap=\"confirmDeleteChat\">\n          <view class=\"action-icon\">🗑️</view>\n          <text>删除对话</text>\n        </view>\n      </view>\n    </view>\n  </view>\n\n  <!-- 重命名弹窗 -->\n  <view class=\"rename-modal\" wx:if=\"{{showRenameModal}}\">\n    <view class=\"modal-mask\" bindtap=\"hideRenameModal\"></view>\n    <view class=\"modal-content\">\n      <view class=\"modal-header\">\n        <text>重命名对话</text>\n        <view class=\"close-btn\" bindtap=\"hideRenameModal\">×</view>\n      </view>\n      <view class=\"modal-body\">\n        <input \n          class=\"rename-input\"\n          placeholder=\"请输入新的对话名称\"\n          value=\"{{newChatName}}\"\n          bindinput=\"onRenameInput\"\n          maxlength=\"20\"\n        />\n      </view>\n      <view class=\"modal-footer\">\n"}