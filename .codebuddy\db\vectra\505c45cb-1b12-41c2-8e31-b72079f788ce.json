{"chunk": 4, "numChunks": 6, "fileHash": "NZjhfJONEn33fTaL5WUzkjtuxbwRJIB1E7qQ/zXp6ww=", "filePath": "pages/profile/profile.wxml", "content": "          <text class=\"menu-text\">充值记录</text>\n        <text class=\"menu-arrow\">›</text>\n    </view>\n  </view>\n\n    <!-- 设置相关 -->\n    <view class=\"menu-group\">\n      <view class=\"group-title\">设置</view>\n      <view class=\"menu-item\" bindtap=\"switchTheme\">\n        <text class=\"menu-icon\">🎨</text>\n          <text class=\"menu-text\">主题模式</text>\n        <text class=\"menu-value\">{{currentTheme}}</text>\n        <text class=\"menu-arrow\">›</text>\n      </view>\n      <view class=\"menu-item\" bindtap=\"navigateTo\" data-url=\"/pages/settings/settings\">\n        <text class=\"menu-icon\">⚙️</text>\n          <text class=\"menu-text\">更多设置</text>\n        <text class=\"menu-arrow\">›</text>\n    </view>\n  </view>\n\n    <!-- 帮助反馈 -->\n    <view class=\"menu-group\">\n      <view class=\"group-title\">帮助与支持</view>\n      <view class=\"menu-item\" bindtap=\"navigateTo\" data-url=\"/pages/customer-service/customer-service\">\n        <text class=\"menu-icon\">🎧</text>\n          <text class=\"menu-text\">联系客服</text>\n        <text class=\"menu-arrow\">›</text>\n      </view>\n      <view class=\"menu-item\" bindtap=\"navigateTo\" data-url=\"/pages/feedback/feedback\">\n        <text class=\"menu-icon\">💌</text>\n          <text class=\"menu-text\">意见反馈</text>\n        <text class=\"menu-arrow\">›</text>\n      </view>\n      <view class=\"menu-item\" bindtap=\"navigateTo\" data-url=\"/pages/about/about\">\n"}