{"chunk": 19, "numChunks": 22, "fileHash": "jr30LZOMc1P5VIpMptyaGnan9xv44m/c09rHA5aMeTY=", "filePath": "pages/index/index.js", "content": "// index.js\nPage({\n  getSolarTermsData(year, month, day) {\n    const solarTerms = {\n      '2-4': '立春', '2-19': '雨水',\n      '3-5': '惊蛰', '3-20': '春分',\n      '4-4': '清明', '4-20': '谷雨',\n      '5-5': '立夏', '5-21': '小满',\n      '6-5': '芒种', '6-21': '夏至',\n      '7-7': '小暑', '7-22': '大暑',\n      '8-7': '立秋', '8-23': '处暑',\n      '9-7': '白露', '9-23': '秋分',\n      '10-8': '寒露', '10-23': '霜降',\n      '11-7': '立冬', '11-22': '小雪',\n      '12-7': '大雪', '12-22': '冬至',\n      '1-5': '小寒', '1-20': '大寒'\n    }\n    \n    const key = `${month}-${day}`\n    const name = solarTerms[key]\n    \n    return {\n      name: name || '',\n      isToday: !!name\n    }\n  },\n  // 检查签到状态\n  checkSignIn() {\n    const today = new Date().toLocaleDateString()\n    const lastSignIn = wx.getStorageSync('lastSignIn')\n    \n    this.setData({\n      signInStatus: today === lastSignIn\n    })\n  },\n  // 签到功能"}