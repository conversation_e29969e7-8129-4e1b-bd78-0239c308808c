{"chunk": 7, "numChunks": 10, "fileHash": "lg9EuzAhq20Uogzg9r07DedW1yISmqoEvsYKCcdN8GI=", "filePath": "utils/fengshui/calculator.js", "content": "// 八卦方位数据\nclass FengshuiCalculator {\n  calculateShopPositions(mainDirection, positions) {\n    // 商铺吉凶方位计算\n    const auspiciousDirections = {\n      '南': ['收银台', '展示区'],\n      '东': ['主要商品区', '品牌展示'],\n      '北': ['仓储区', '办公区'],\n      '西': ['休息区', '试衣间']\n    };\n\n    const inauspiciousDirections = {\n      '东南': ['卫生间', '垃圾间'],\n      '西北': ['仓库', '杂物间'],\n      '西南': ['员工通道', '后门'],\n      '东北': ['设备间', '管道间']\n    };\n\n    for (const [direction, usage] of Object.entries(auspiciousDirections)) {\n      if (this.isCompatibleDirection(mainDirection, direction)) {\n        positions.auspicious.push({\n          direction,\n          recommendedUsage: usage.join('、'),\n          description: `此方位适合设置${usage.join('、')}，有利于招财进宝。`\n        });\n      }\n    }\n\n    for (const [direction, usage] of Object.entries(inauspiciousDirections)) {\n      if (!this.isCompatibleDirection(mainDirection, direction)) {\n        positions.inauspicious.push({\n          direction,\n          avoidance: `避免设置${usage.join('、')}`,\n          description: `此方位不宜设置${usage.join('、')}，以免影响生意。`\n        });\n      }\n    }\n  }"}