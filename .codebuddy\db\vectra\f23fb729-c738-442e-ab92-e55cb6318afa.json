{"chunk": 3, "numChunks": 13, "fileHash": "UUsAqDc9MY1pjBTLokkGc1GjjJmTwiOLz2lDZFa/4nc=", "filePath": "pages/index/index.wxss", "content": "  font-size: 24rpx;\n  padding: 12rpx 0;\n}\n\n.yi-items, .ji-items {\n  display: flex;\n  align-items: flex-start;\n}\n\n.yiji-label {\n  font-weight: 500;\n  margin-right: 12rpx;\n  min-width: 40rpx;\n}\n\n.yi-items .yiji-label {\n  color: #4caf50;\n}\n\n.ji-items .yiji-label {\n  color: #f44336;\n}\n\n.yiji-content {\n  flex: 1;\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8rpx;\n}\n\n.yiji-tag {\n  background: rgba(149, 117, 205, 0.1);\n  border-radius: 12rpx;\n  padding: 4rpx 12rpx;\n  display: inline-flex;\n  align-items: center;\n  transition: all 0.3s ease;\n}\n\n.yi-items .yiji-tag {\n  background: rgba(76, 175, 80, 0.1);\n}\n\n.ji-items .yiji-tag {\n  background: rgba(244, 67, 54, 0.1);\n}\n\n.yiji-tag:active {\n  transform: scale(0.95);\n  opacity: 0.8;\n}\n\n.tag-text {\n  color: #666;\n  font-size: 24rpx;\n}\n\n.yi-items .tag-text {\n  color: #4caf50;\n}\n\n.ji-items .tag-text {\n  color: #f44336;\n}\n\n/* 轮播图 - 美化 */\n.banner {\n  height: 350rpx;\n  margin: 20rpx 0;\n  border-radius: 25rpx;\n  overflow: hidden;\n}\n\n.banner-item {\n  position: relative;\n  height: 100%;\n}\n\n.banner-image {\n"}