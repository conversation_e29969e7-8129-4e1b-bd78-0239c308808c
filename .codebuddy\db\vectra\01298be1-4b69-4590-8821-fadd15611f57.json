{"chunk": 9, "numChunks": 18, "fileHash": "yi4gCVr62AiqkHDrZGxGnPy2D7On+5yxIebQ7IGvQ04=", "filePath": "pages/community/community.js", "content": "// pages/community/community.js\nPage({\n  },\n  getCategoryName(value) {\n    const category = this.data.categories.find(item => item.value === value)\n    return category ? category.name : '其他'\n  },\n  formatTime(timestamp) {\n    if (!timestamp) return ''\n    const date = new Date(timestamp)\n    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`\n  },\n  /**\n   * 搜索帖子\n   */\n  onSearchInput(e) {\n    this.setData({\n      searchKeyword: e.detail.value,\n      posts: [],\n      pageNum: 1,\n      hasMore: true\n    }, () => {\n      this.loadPosts()\n    })\n  },\n  /**\n   * 切换分类\n   */"}