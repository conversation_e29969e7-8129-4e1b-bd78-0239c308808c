{"chunk": 5, "numChunks": 8, "fileHash": "2gwHiceF78ljJhRnHSk/QhbjXumu/H9/2egtjP1psSk=", "filePath": "pages/fortune/fortune.js", "content": "const app = getApp()\nPage({\n  /**\n   * 绘制运势曲线图\n   */\n  drawFortuneChart() {\n    const query = wx.createSelectorQuery()\n    query.select('#fortuneChart')\n      .fields({ node: true, size: true })\n      .exec((res) => {\n        if (!res || !res[0] || !res[0].node) {\n          console.error('获取canvas节点失败')\n          return\n        }\n        \n        const canvas = res[0].node\n        const ctx = canvas.getContext('2d')\n        \n        // 设置canvas大小\n        const dpr = wx.getSystemInfoSync().pixelRatio\n        canvas.width = res[0].width * dpr\n        canvas.height = res[0].height * dpr\n        ctx.scale(dpr, dpr)\n\n        // 绘制曲线\n        this.drawCurve(ctx, res[0].width, res[0].height)\n      })\n  },\n  /**\n   * 绘制曲线\n   */"}