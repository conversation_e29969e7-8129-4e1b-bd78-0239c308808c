{"chunk": 4, "numChunks": 38, "fileHash": "DpyinnhZJO6jrs0/VY0BXwrNjORlEQ1BeRywBJybhxQ=", "filePath": "pages/ai-chat/ai-chat.js", "content": "const app = getApp()\nPage({\n  onShow() {\n    try {\n      if (typeof this.getTabBar === 'function' && this.getTabBar()) {\n        // 调用自定义 tabBar 的 setSelected 方法\n        this.getTabBar().setSelected()\n      }\n    } catch (error) {\n      console.error('设置 TabBar 选中状态失败:', error)\n    }\n    this.refreshGlobalState()\n    this.checkLoginStatus()\n  },\n  /**\n   * 初始化状态监听器\n   */\n  initStateListeners() {\n    // 监听用户信息变化\n    this.userInfoListener = globalState.addListener('userInfo', (newUserInfo) => {\n      this.setData({\n        userInfo: newUserInfo,\n        isLoggedIn: !!newUserInfo\n      })\n    })\n\n    // 监听出生信息变化\n    this.birthInfoListener = globalState.addListener('birthInfo', (newBirthInfo) => {\n      this.setData({ birthInfo: newBirthInfo })\n    })\n  },\n  /**\n   * 刷新全局状态\n   */\n  refreshGlobalState() {\n    const state = globalState.getState()\n    this.setData({\n      userInfo: state.userInfo,\n      isLoggedIn: !!state.userInfo,\n      birthInfo: state.birthInfo\n    })\n  },\n  /**\n   * 页面卸载时清理监听器\n   */\n  onUnload() {\n    if (this.userInfoListener) this.userInfoListener()\n    if (this.birthInfoListener) this.birthInfoListener()\n  },\n  /**\n   * 初始化聊天界面\n   */"}