{"chunk": 1, "numChunks": 13, "fileHash": "UUsAqDc9MY1pjBTLokkGc1GjjJmTwiOLz2lDZFa/4nc=", "filePath": "pages/index/index.wxss", "content": "  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n/* 主要内容 */\n.content {\n  padding: 20rpx;\n}\n\n/* 时间问候头部 - 增强样式 */\n.time-greeting-header {\n  background: linear-gradient(135deg, #fff 0%, #f8f5ff 100%);\n  padding: 30rpx;\n  border-radius: 25rpx;\n  margin-bottom: 25rpx;\n  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.08);\n}\n\n.greeting-section {\n  display: flex;\n  align-items: baseline;\n  margin-bottom: 20rpx;\n}\n\n.greeting-text {\n  font-size: 40rpx;\n  font-weight: bold;\n  background: linear-gradient(135deg, #9575cd 0%, #7e57c2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  margin-right: 10rpx;\n}\n\n.user-name {\n  font-size: 36rpx;\n  color: #666;\n  font-weight: 500;\n}\n\n/* 日历部分新样式 */\n.calendar-section {\n  background: linear-gradient(135deg, #f5f0ff 0%, #ece7f5 100%);\n  border-radius: 20rpx;\n  padding: 20rpx;\n  display: flex;\n  flex-direction: column;\n  gap: 16rpx;\n}\n\n.solar-date {\n  display: flex;\n  align-items: center;\n  padding-bottom: 16rpx;\n  border-bottom: 2rpx solid rgba(149, 117, 205, 0.1);\n}\n\n.date-number {\n"}