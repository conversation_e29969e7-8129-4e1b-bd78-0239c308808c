{"chunk": 0, "numChunks": 1, "fileHash": "IaOHLZBX7TvlZYjC1QfDemV1gkHcAttShvwHZ/OhiiU=", "filePath": "subpages/admin/admin/admin.wxss", "content": "/* subpages/admin/admin/admin.wxss */\r\n/* 此文件是为了解决编译器路径错误而创建的 */\r\n\r\n.admin-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  padding: 20rpx;\r\n}\r\n\r\n.admin-header {\r\n  background: white;\r\n  border-radius: 16rpx;\r\n  padding: 40rpx;\r\n  text-align: center;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.admin-title {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #8a2be2;\r\n}\r\n\r\n.admin-content {\r\n  background: white;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.admin-menu {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20rpx;\r\n}\r\n\r\n.menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 30rpx;\r\n  background: #f8f9fa;\r\n  border-radius: 12rpx;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.menu-item:active {\r\n  background: #e9ecef;\r\n  transform: scale(0.98);\r\n}\r\n\r\n.menu-icon {\r\n  font-size: 32rpx;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.menu-text {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  font-weight: 500;\r\n} "}