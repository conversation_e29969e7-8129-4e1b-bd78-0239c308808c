{"chunk": 11, "numChunks": 22, "fileHash": "jr30LZOMc1P5VIpMptyaGnan9xv44m/c09rHA5aMeTY=", "filePath": "pages/index/index.js", "content": "// index.js\nPage({\n  updateTimeAndCalendar() {\n    // 更新问候语\n    this.updateGreeting()\n    \n    // 获取今日信息\n    const todayInfo = getTodayInfo()\n    \n    // 获取详细的宜忌信息\n    const now = new Date()\n    const year = now.getFullYear()\n    const month = now.getMonth() + 1\n    const day = now.getDate()\n    \n    // 使用 lunar 实例获取详细的宜忌信息\n    const lunar = require('../../utils/lunar').lunar\n    const yijiInfo = lunar.getYiJiWithExplanations(year, month, day)\n    \n    // 更新 todayInfo 中的宜忌信息\n    todayInfo.lunar.yiji = yijiInfo\n    \n    this.setData({ todayInfo })\n  },\n  updateGreeting() {\n    const hour = new Date().getHours()\n    let greeting = ''\n    \n    if (hour >= 0 && hour < 6) {\n      greeting = '夜深了，'\n    } else if (hour >= 6 && hour < 9) {\n      greeting = '早安，'\n    } else if (hour >= 9 && hour < 12) {\n      greeting = '上午好，'\n    } else if (hour >= 12 && hour < 14) {\n      greeting = '午安，'\n    } else if (hour >= 14 && hour < 18) {\n      greeting = '下午好，'\n    } else if (hour >= 18 && hour < 22) {\n      greeting = '晚上好，'\n    } else {\n      greeting = '夜深了，'\n    }\n    \n    this.setData({ greeting })\n  },"}