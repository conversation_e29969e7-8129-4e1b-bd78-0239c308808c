{"chunk": 6, "numChunks": 14, "fileHash": "3g+f4UKfyPQOeFOGstuRr+TvNVx6NqSI5ZfRnXdVunU=", "filePath": "pages/profile/profile.js", "content": "// pages/profile/profile.js\nPage({\n  /**\n   * 加载初始数据\n   */\n  loadInitialData() {\n    const state = globalState.getState()\n    this.setData({\n      userInfo: state.userInfo,\n      hasUserInfo: !!state.userInfo,\n      points: state.points,\n      level: this.calculateLevel(state.points),\n      birthInfo: state.birthInfo,\n      signInStatus: state.signInStatus,\n      isWxWork: state.isWxWork\n    })\n  },\n  /**\n   * 刷新全局状态\n   */\n  refreshGlobalState() {\n    globalState.loadFromStorage()\n    this.loadInitialData()\n  },\n  /**\n   * 生命周期函数--监听页面隐藏\n   */\n  onHide() {\n\n  },\n  /**\n   * 生命周期函数--监听页面卸载\n   */\n  onUnload() {\n\n  },\n  /**\n   * 页面相关事件处理函数--监听用户下拉动作\n   */\n  onPullDownRefresh() {\n    if (this.data.userInfo.userId) {\n      this.loadUserStats()\n    }\n    wx.stopPullDownRefresh()\n  },\n  /**\n   * 页面上拉触底事件的处理函数\n   */\n  onReachBottom() {\n\n  },\n  /**\n   * 用户点击右上角分享\n   */\n  onShareAppMessage() {\n    return {\n      title: '玄学社区',\n      path: '/pages/profile/profile'\n    }\n  },\n  // 检查登录状态\n  checkLoginStatus() {\n    const userInfo = wx.getStorageSync('userInfo')\n    if (userInfo) {\n      this.setData({ userInfo })\n    }\n  },\n  // 登录"}