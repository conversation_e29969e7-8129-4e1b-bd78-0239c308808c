{"chunk": 4, "numChunks": 6, "fileHash": "LfPZZEUjbDaiOVOLCtwxAuZgKgXnDAGyjWMO0X626Ik=", "filePath": "pages/ai-chat/ai-chat.wxml", "content": "          <view class=\"input-counter\">{{inputValue.length}}/1000</view>\n        </view>\n      </view>\n\n      <!-- 按钮区域 -->\n      <view class=\"button-section\">\n        <view class=\"action-buttons\">\n          <button class=\"cancel-btn\" bindtap=\"onCancelInput\">\n            <text class=\"btn-icon\">✕</text>\n            <text class=\"btn-text\">取消</text>\n          </button>\n          <button\n            class=\"send-btn {{inputValue.trim() ? 'active' : 'disabled'}}\"\n            bindtap=\"sendMessage\"\n            disabled=\"{{!inputValue.trim() || isTyping}}\"\n          >\n            <text class=\"btn-icon\">{{isTyping ? '⏳' : '🚀'}}</text>\n            <text class=\"btn-text\">{{isTyping ? '发送中' : '发送'}}</text>\n          </button>\n        </view>\n      </view>\n\n      <!-- 快捷回复 -->\n      <view class=\"quick-replies\" wx:if=\"{{quickReplies.length > 0}}\">\n        <view class=\"quick-reply-item\"\n              wx:for=\"{{quickReplies}}\"\n              wx:key=\"*this\"\n              data-text=\"{{item}}\"\n              bindtap=\"selectQuickReply\">\n          {{item}}\n        </view>\n      </view>\n    </view>\n\n    <!-- 收起状态 - 悬浮球 -->\n    <view class=\"floating-ball\" wx:else bindtap=\"onFabClick\">\n      <view class=\"ball-glow\"></view>\n      <view class=\"ball-content\">\n        <text class=\"ball-icon\">{{fabIcon}}</text>\n      </view>\n      <view class=\"ball-pulse\"></view>\n    </view>\n  </view>\n\n  <!-- 遮罩层 -->\n"}