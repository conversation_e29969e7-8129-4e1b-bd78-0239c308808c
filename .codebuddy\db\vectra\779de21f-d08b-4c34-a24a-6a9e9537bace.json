{"chunk": 5, "numChunks": 12, "fileHash": "5X5f8yfnxquoBxYzBGhnUPxPUBZeZKQywMAReox6cBk=", "filePath": "pages/bazi/index.js", "content": "const app = getApp()\nPage({\n  async onAnalyze() {\n    if (!this.data.canSubmit) return\n\n    wx.showLoading({ title: '正在分析...' })\n\n    try {\n      // 调用八字计算器\n      const calculator = new BaziCalculator({\n        name: this.data.name,\n        gender: this.data.gender,\n        birthDate: this.data.birthDate,\n        birthTime: this.data.birthTime,\n        isLunar: this.data.isLunar\n      })\n\n      // 获取分析结果\n      const result = await calculator.calculate()\n\n      // 更新五行分析\n      this.setData({\n        wuxingAnalysis: result.wuxing.map(item => ({\n          ...item,\n          percent: item.value\n        }))\n      })\n\n      // 更新命格分析\n      this.setData({\n        minggeAnalysis: result.mingge\n      })\n\n      // 更新运势分析\n      this.updateYunshiAnalysis(result.yunshi)\n\n      // 更新吉凶分析\n      this.setData({\n        jixiongAnalysis: result.jixiong\n      })\n\n      // 更新建议指导\n      this.setData({\n        adviceList: result.advice\n      })\n\n      // 显示结果\n      this.setData({ showResult: true })\n\n      // 绘制图表\n      this.drawCharts()\n\n      wx.hideLoading()\n    } catch (error) {\n      console.error('分析失败:', error)\n      wx.hideLoading()\n      wx.showToast({\n        title: '分析失败，请重试',\n        icon: 'none'\n      })\n    }\n  },\n  // 切换运势标签\n  switchTab(e) {\n    const tab = e.currentTarget.dataset.tab\n    this.setData({ currentTab: tab })\n    this.updateYunshiChart()\n  },\n  // 更新运势分析"}