{"chunk": 3, "numChunks": 6, "fileHash": "YXf85xx1jxmB9as0Va5OH90PaK79Xnt82sfEgjKj2bE=", "filePath": "pages/post-detail/post-detail.wxss", "content": "  padding-left: 15rpx;\n  position: relative;\n}\n\n.section-title::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 6rpx;\n  height: 70%;\n  background: #9575cd;\n  border-radius: 3rpx;\n}\n\n.related-list {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.related-item {\n  display: flex;\n  background: white;\n  padding: 20rpx;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  transition: all 0.3s ease;\n}\n\n.related-item:active {\n  transform: translateY(2rpx);\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);\n}\n\n.related-image {\n  width: 140rpx;\n  height: 100rpx;\n  border-radius: 15rpx;\n  margin-right: 20rpx;\n  object-fit: cover;\n}\n\n.related-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.related-title {\n  font-size: 30rpx;\n  color: #333;\n  line-height: 1.5;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n}\n\n.related-meta {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 评论区域 */\n.comment-section {\n  margin: 40rpx 30rpx;\n}\n\n.comment-list {\n  display: flex;\n"}