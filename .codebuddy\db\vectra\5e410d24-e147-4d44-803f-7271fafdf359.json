{"chunk": 3, "numChunks": 18, "fileHash": "yi4gCVr62AiqkHDrZGxGnPy2D7On+5yxIebQ7IGvQ04=", "filePath": "pages/community/community.js", "content": "// pages/community/community.js\nPage({\n  /**\n   * 页面的初始数据\n   */\n  data: {\n    searchKeyword: '',\n    currentCategory: 'all',\n    categories: [\n      { name: '全部', value: 'all' },\n      { name: '紫微斗数', value: 'ziwei' },\n      { name: '易经', value: 'yijing' },\n      { name: '风水', value: 'fengshui' },\n      { name: '八字', value: 'bazi' },\n      { name: '运势', value: 'fortune' }\n    ],\n    publishCategories: [\n      { name: '紫微斗数', value: 'ziwei' },\n      { name: '易经', value: 'yijing' },\n      { name: '风水', value: 'fengshui' },\n      { name: '八字', value: 'bazi' },\n      { name: '运势', value: 'fortune' }\n    ],\n    posts: [],\n    showPublishModal: false,\n    newPost: {\n      title: '',\n      content: '',\n      images: [],\n      categoryIndex: 0,\n      tag: 'discussion'\n    },\n    isLoading: false,\n    hasMore: true,\n    pageNum: 1,\n    pageSize: 10,\n    isCloudInited: false,\n    showReplyModal: false,\n    currentComment: null,\n    sortType: 'latest', // latest, hot, recommended\n    userFavorites: []\n  },\n  /**\n   * 生命周期函数--监听页面加载\n   */\n  onLoad(options) {\n    this.initCloud()\n    this.loadUserFavorites()\n  },"}