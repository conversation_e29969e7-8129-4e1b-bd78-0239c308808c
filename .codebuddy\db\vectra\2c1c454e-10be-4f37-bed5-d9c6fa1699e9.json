{"chunk": 11, "numChunks": 19, "fileHash": "PEPeuJm140RAD7VBd85WlnsfcWUdoorqVSQj8ISByl0=", "filePath": "pages/ai-chat/ai-chat.wxss", "content": "  height: calc(env(safe-area-inset-bottom) + 20rpx);\n}\n\n/* 动画效果 */\n@keyframes glow-pulse {\n  0%, 100% {\n    opacity: 0.6;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(1.1);\n  }\n}\n\n@keyframes pulse-dot {\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.7;\n    transform: scale(1.2);\n  }\n}\n\n@keyframes pulse-typing {\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.5;\n    transform: scale(0.8);\n  }\n}\n\n@keyframes pulse-ring {\n  0% {\n    transform: scale(1);\n    opacity: 1;\n  }\n  100% {\n    transform: scale(1.5);\n    opacity: 0;\n  }\n}\n\n@keyframes panel-expand {\n  0% {\n    opacity: 0;\n    transform: scale(0.8);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n@keyframes message-fade-in {\n  0% {\n    opacity: 0;\n    transform: translateY(20rpx);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* AI思考状态 */\n.typing-indicator {\n  margin-top: 32rpx;\n}\n\n.typing-indicator .ai-message {\n  opacity: 0.8;\n}\n\n.typing-content {\n  display: flex;\n  align-items: center;\n"}