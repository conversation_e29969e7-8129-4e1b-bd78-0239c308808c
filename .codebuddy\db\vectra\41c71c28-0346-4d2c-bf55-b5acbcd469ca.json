{"chunk": 13, "numChunks": 15, "fileHash": "O/nJs1XsxYT9PDo4XFlMEnsiqZRG/Zu4Jl5KoBF5Ci8=", "filePath": "pages/index/index.wxml", "content": "        <!-- 测算类目 -->\n        <view class=\"category-section\">\n          <view class=\"section-title\">测算项目</view>\n          <view class=\"category-grid\">\n            <view class=\"category-item\" wx:for=\"{{categories}}\" wx:key=\"id\" bindtap=\"onCategoryTap\" data-id=\"{{item.id}}\" data-url=\"{{item.url}}\">\n              <image class=\"category-icon\" src=\"{{item.icon}}\" mode=\"aspectFit\" />\n              <text class=\"category-name\">{{item.name}}</text>\n          </view>\n        </view>\n      </view>\n\n        <!-- 热门测算 -->\n        <view class=\"hot-section\">\n          <view class=\"section-title\">\n            <text>热门推荐</text>\n            <text class=\"section-more\" bindtap=\"goToMore\">查看更多</text>\n          </view>\n          <view class=\"hot-list\">\n            <view class=\"hot-item\" wx:for=\"{{hotItems}}\" wx:key=\"id\" bindtap=\"onHotItemTap\" data-id=\"{{item.id}}\" data-url=\"{{item.url}}\">\n              <image class=\"hot-image\" src=\"{{item.imageUrl}}\" mode=\"aspectFill\" />\n              <view class=\"hot-info\">\n                <text class=\"hot-title\">{{item.title}}</text>\n                <text class=\"hot-desc\">{{item.description}}</text>\n                <view class=\"hot-meta\">\n                  <text class=\"hot-price\">￥{{item.price}}</text>\n                  <text class=\"hot-count\">{{item.orderCount}}人测算</text>\n          </view>\n              </view>\n          </view>\n        </view>\n      </view>\n\n        <!-- 热门文章 -->\n        <view class=\"article-section\">\n          <view class=\"section-title\">\n            <text>命理知识</text>\n"}