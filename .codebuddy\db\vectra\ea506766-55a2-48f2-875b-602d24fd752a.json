{"chunk": 2, "numChunks": 7, "fileHash": "Cqx4nGobpVxA67G0CO//E9qs4sv6sw0mA2CIPhYqZ4g=", "filePath": "subpages/divination/bazi/bazi.wxss", "content": "  width: 100%;\n  height: 96rpx;\n  background: linear-gradient(135deg, #8a2be2, #9932cc);\n  color: white;\n  border-radius: 48rpx;\n  font-size: 32rpx;\n  font-weight: bold;\n  margin-top: 40rpx;\n  border: none;\n  box-shadow: 0 8rpx 20rpx rgba(138, 43, 226, 0.3);\n}\n\n.analyze-btn:active {\n  opacity: 0.8;\n  transform: scale(0.98);\n}\n\n.analyze-btn[disabled] {\n  opacity: 0.6;\n}\n\n/* 分析结果 */\n.bazi-result {\n  padding: 0 30rpx;\n  margin-top: 30rpx;\n}\n\n.result-card {\n  background: white;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);\n}\n\n.result-header {\n  text-align: center;\n  margin-bottom: 40rpx;\n}\n\n.result-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.result-name {\n  font-size: 28rpx;\n  color: #666;\n}\n\n/* 八字展示 */\n.bazi-display {\n  background: #f8f9fa;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 40rpx;\n}\n\n.bazi-row {\n  display: flex;\n  justify-content: space-around;\n  margin-bottom: 20rpx;\n}\n\n.bazi-row:last-child {\n  margin-bottom: 0;\n}\n\n"}