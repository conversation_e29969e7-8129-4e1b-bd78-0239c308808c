{"chunk": 1, "numChunks": 6, "fileHash": "u49Rqom2MlWUCSY5d2kW1CfpdWY95ku7je8yGKj9+oc=", "filePath": "pages/name-test/name-test.wxss", "content": "  font-size: 30rpx;\n}\n\n.test-btn {\n  width: 100%;\n  height: 88rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 44rpx;\n  font-size: 32rpx;\n  font-weight: bold;\n}\n\n.test-btn[disabled] {\n  background: #ccc;\n}\n\n/* 加载状态 */\n.loading-section {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 80rpx 40rpx;\n  color: white;\n}\n\n.loading-icon {\n  margin-bottom: 40rpx;\n}\n\n.spinner {\n  width: 80rpx;\n  height: 80rpx;\n  border: 6rpx solid rgba(255,255,255,0.3);\n  border-radius: 50%;\n  border-top-color: white;\n  animation: spin 1s ease-in-out infinite;\n}\n\n@keyframes spin {\n  to { transform: rotate(360deg); }\n}\n\n.loading-text {\n  font-size: 32rpx;\n  opacity: 0.9;\n}\n\n/* 结果区域 */\n.result-section {\n  margin: 40rpx 30rpx;\n  background: white;\n  border-radius: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.1);\n}\n\n.result-header {\n  padding: 40rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  text-align: center;\n}\n\n.test-name {\n  font-size: 48rpx;\n  font-weight: bold;\n  margin-bottom: 20rpx;\n}\n\n"}