{"chunk": 10, "numChunks": 22, "fileHash": "jr30LZOMc1P5VIpMptyaGnan9xv44m/c09rHA5aMeTY=", "filePath": "pages/index/index.js", "content": "// index.js\nPage({\n  },\n  onLoad() {\n    this.initTimeAndCalendar()\n    this.getBanners()\n    this.getDailyFortune()\n    this.loadRecentUsed()\n    this.checkSolarTerms()\n    this.checkSignIn()\n    this.getHotArticles()\n    \n    // 设置加载状态\n    setTimeout(() => {\n      this.setData({ isLoading: false })\n    }, 1000)\n\n    // 检查用户是否已登录\n    if (app.globalData.userInfo) {\n      this.setData({\n        userInfo: app.globalData.userInfo,\n        hasUserInfo: true\n      })\n    }\n  },\n  onShow() {\n    try {\n      if (typeof this.getTabBar === 'function' && this.getTabBar()) {\n        // 调用自定义 tabBar 的 setSelected 方法\n        this.getTabBar().setSelected()\n      }\n      this.loadRecentUsed() // 刷新最近使用\n    } catch (error) {\n      console.error('设置 TabBar 选中状态失败:', error)\n    }\n  },\n  initTimeAndCalendar() {\n    // 设置初始数据\n    this.updateTimeAndCalendar()\n    \n    // 每分钟更新一次\n    setInterval(() => {\n      this.updateTimeAndCalendar()\n    }, 60000)\n  },"}