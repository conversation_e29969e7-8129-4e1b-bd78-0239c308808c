{"chunk": 60, "numChunks": 74, "fileHash": "UWh+hTmku0OFKrrOI8FY7Ob/w0LwUBFFaP/YfXcJBpg=", "filePath": "utils/xingming/calculator.js", "content": "// 汉字笔画数据库\nclass XingmingCalculator {\n  constructor(options) {\n    this.options = options\n    this.surname = options.surname\n    this.givenName = options.givenName\n    this.gender = options.gender\n    this.bazi = options.bazi\n  }\n  async calculate() {\n    try {\n      // 1. 计算五格数理\n      const wugeResult = this.calculateWuge()\n      \n      // 2. 计算三才配置\n      const sancaiResult = this.calculateSancai()\n      \n      // 3. 计算八字配合\n      const baziResult = this.calculateBaziMatch()\n      \n      // 4. 计算总分\n      const totalScore = Math.round((wugeResult.score + sancaiResult.score + baziResult.score) / 3)\n      \n      // 5. 生成分析结果\n      return {\n        score: totalScore,\n        description: this.getScoreDescription(totalScore),\n        wugeList: wugeResult.list,\n        sancaiAnalysis: sancaiResult.analysis,\n        sancaiData: sancaiResult.data,\n        baziAnalysis: baziResult.analysis,\n        jixiongList: this.generateJixiongList(),\n        adviceList: this.generateAdviceList()\n      }\n    } catch (error) {\n      console.error('姓名测算错误:', error)\n      throw error\n    }\n  }\n  // 计算五格数理"}