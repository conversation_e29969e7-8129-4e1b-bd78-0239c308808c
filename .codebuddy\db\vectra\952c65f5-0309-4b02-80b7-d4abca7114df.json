{"chunk": 4, "numChunks": 15, "fileHash": "ydfcj+4tZs2HvARUdw6TErn3fg1suYaeqU0s+F1D6wI=", "filePath": "pages/hehun/index.js", "content": "const app = getApp()\nPage({\n  onFemaleDateChange(e) {\n    this.setData({\n      'female.birthDate': e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  onFemaleTimeChange(e) {\n    this.setData({\n      'female.birthTime': e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 检查是否可以提交\n  checkCanSubmit() {\n    const { male, female } = this.data\n    const canSubmit = male.name && male.birthDate && male.birthTime &&\n                     female.name && female.birthDate && female.birthTime\n    this.setData({ canSubmit })\n  },\n  // 开始测算"}