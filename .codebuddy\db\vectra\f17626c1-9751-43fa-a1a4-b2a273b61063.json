{"chunk": 10, "numChunks": 15, "fileHash": "An9WOmGw20FYVB4+SrCnlnnpOBHN6cohdD4SvY7rXL8=", "filePath": "pages/ziwei/ziwei.js", "content": "// ziwei.js\nPage({\n  generateCareerDetails(mingGong, stars) {\n    const careerStars = stars.filter(s => s.position === mingGong)\n    const mainStars = careerStars.filter(s => s.type === '主星')\n    const details = []\n    // 根据主星生成详细预测\n    if (mainStars.some(s => s.name === '紫微')) {\n      details.push({\n        title: '领导能力',\n        content: '您具有出色的领导才能，适合担任管理职务，能够统筹全局，决策果断。'\n      })\n    }\n    if (mainStars.some(s => s.name === '天机')) {\n      details.push({\n        title: '创新能力',\n        content: '您思维敏捷，创新能力强，适合从事策划、研究或技术开发工作。'\n      })\n    }\n    if (mainStars.some(s => s.name === '武曲')) {\n      details.push({\n        title: '理财能力',\n        content: '您理财能力强，适合从事金融、财务或商业管理工作。'\n      })\n    }\n    if (mainStars.some(s => s.name === '天府')) {\n      details.push({\n        title: '管理能力',\n        content: '您具有出色的管理能力，适合担任企业高管或自主创业。'\n      })\n    }\n    // 如果没有特定主星，添加通用预测"}