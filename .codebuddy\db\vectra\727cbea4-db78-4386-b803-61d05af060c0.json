{"chunk": 2, "numChunks": 7, "fileHash": "YmBtAc19/HOz7eUBhWxGsON9KNrEblPnxcHiHsXvXv8=", "filePath": "pages/community/community.wxss", "content": ".category-tag.yijing {\n  background: #4b0082;\n}\n\n.category-tag.fengshui {\n  background: #483d8b;\n}\n\n.category-tag.bazi {\n  background: #9370db;\n}\n\n.category-tag.fortune {\n  background: #7b68ee;\n}\n\n.post-content {\n  margin-bottom: 15px;\n}\n\n.post-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8px;\n}\n\n.post-text {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.5;\n  margin-bottom: 10px;\n}\n\n.post-images {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 8px;\n}\n\n.post-images image {\n  width: 100%;\n  height: 100px;\n  border-radius: 8px;\n}\n\n.post-footer {\n  display: flex;\n  justify-content: space-around;\n  border-top: 1px solid #f0f0f0;\n  padding-top: 12px;\n}\n\n.action-item {\n  display: flex;\n  align-items: center;\n}\n\n.action-icon {\n  width: 16px;\n  height: 16px;\n  margin-right: 4px;\n}\n\n.action-item text {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 加载更多样式 */\n.loading-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 15px 0;\n}\n\n.loading-icon {\n  width: 20px;\n  height: 20px;\n  margin-right: 8px;\n  border: 2px solid #f3f3f3;\n  border-top: 2px solid #8a2be2;\n  border-radius: 50%;\n"}