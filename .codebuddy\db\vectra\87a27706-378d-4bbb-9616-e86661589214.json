{"chunk": 2, "numChunks": 5, "fileHash": "rXA9I8zblvy793sn1s5fHDcAnMr2WCJwMU+H52clkck=", "filePath": "pages/birth-info/birth-info.wxss", "content": "  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);\n}\n\n.input-placeholder {\n  color: #bbb;\n}\n\n.input-icon {\n  position: absolute;\n  right: 20rpx;\n  font-size: 32rpx;\n  opacity: 0.5;\n}\n\n/* 性别选择器 */\n.gender-selector {\n  display: flex;\n  gap: 20rpx;\n}\n\n.gender-option {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 24rpx;\n  border: 2rpx solid #e8e8e8;\n  border-radius: 16rpx;\n  background: #fafafa;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.gender-option.active {\n  border-color: #667eea;\n  background: rgba(102, 126, 234, 0.1);\n  transform: translateY(-2rpx);\n}\n\n.gender-icon {\n  font-size: 48rpx;\n  margin-bottom: 8rpx;\n}\n\n.gender-text {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n/* 选择器 */\n.picker-wrapper {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n  border: 2rpx solid #e8e8e8;\n  border-radius: 16rpx;\n  padding: 0 20rpx;\n  background: #fafafa;\n  transition: all 0.3s ease;\n}\n\n.picker-wrapper:active {\n  border-color: #667eea;\n  background: white;\n}\n\n.picker-text {\n  font-size: 32rpx;\n  flex: 1;\n}\n\n"}