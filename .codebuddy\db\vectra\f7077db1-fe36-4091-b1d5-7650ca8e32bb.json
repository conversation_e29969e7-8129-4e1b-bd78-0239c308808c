{"chunk": 5, "numChunks": 10, "fileHash": "lg9EuzAhq20Uogzg9r07DedW1yISmqoEvsYKCcdN8GI=", "filePath": "utils/fengshui/calculator.js", "content": "// 八卦方位数据\nclass FengshuiCalculator {\n  calculateResidentialPositions(mainDirection, positions) {\n    // 住宅吉凶方位计算\n    const auspiciousDirections = {\n      '南': ['客厅', '餐厅'],\n      '东': ['书房', '儿童房'],\n      '北': ['主卧', '次卧'],\n      '西': ['厨房', '储藏室']\n    };\n\n    const inauspiciousDirections = {\n      '东南': ['厕所', '垃圾房'],\n      '西北': ['厨房', '明火位置'],\n      '西南': ['病房', '暗房'],\n      '东北': ['厕所', '污水管道']\n    };\n\n    for (const [direction, usage] of Object.entries(auspiciousDirections)) {\n      if (this.isCompatibleDirection(mainDirection, direction)) {\n        positions.auspicious.push({\n          direction,\n          recommendedUsage: usage.join('、'),\n          description: `此方位利于${usage.join('、')}的设置，可增进家人运势。`\n        });\n      }\n    }\n\n    for (const [direction, usage] of Object.entries(inauspiciousDirections)) {\n      if (!this.isCompatibleDirection(mainDirection, direction)) {\n        positions.inauspicious.push({\n          direction,\n          avoidance: `避免设置${usage.join('、')}`,\n          description: `此方位不宜设置${usage.join('、')}，以免影响家运。`\n        });\n      }\n    }\n  }"}