{"chunk": 2, "numChunks": 6, "fileHash": "NZjhfJONEn33fTaL5WUzkjtuxbwRJIB1E7qQ/zXp6ww=", "filePath": "pages/profile/profile.wxml", "content": "      </view>\n      <view class=\"quick-item\" bindtap=\"navigateTo\" data-url=\"/pages/my-favorites/my-favorites\">\n        <view class=\"quick-icon\" style=\"background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\">\n          ⭐\n        </view>\n        <text class=\"quick-text\">我的收藏</text>\n        <text class=\"quick-badge\" wx:if=\"{{userStats.collections}}\">{{userStats.collections}}</text>\n      </view>\n      <view class=\"quick-item\" bindtap=\"navigateTo\" data-url=\"/pages/my-history/my-history\">\n        <view class=\"quick-icon\" style=\"background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\">\n          📊\n        </view>\n        <text class=\"quick-text\">测算记录</text>\n        <text class=\"quick-badge\" wx:if=\"{{userStats.history}}\">{{userStats.history}}</text>\n      </view>\n      <view class=\"quick-item\" bindtap=\"navigateTo\" data-url=\"/pages/sign-in/sign-in\">\n        <view class=\"quick-icon\" style=\"background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);\">\n          📅\n        </view>\n        <text class=\"quick-text\">签到中心</text>\n      </view>\n    </view>\n  </view>\n\n  <!-- 功能列表 -->\n  <view class=\"menu-list\">\n    <!-- 个人管理 -->\n    <view class=\"menu-group\">\n      <view class=\"group-title\">个人管理</view>\n      <view class=\"menu-item\" bindtap=\"editBirthInfo\">\n        <text class=\"menu-icon\">🎂</text>\n          <text class=\"menu-text\">出生信息</text>\n"}