{"chunk": 0, "numChunks": 3, "fileHash": "RttDx+iB5hfM6jraH4ey2N9z0fr1jgnCDw08WhBbAn8=", "filePath": "pages/fortune/fortune.wxss", "content": ".container {\n  padding: 20rpx;\n  background-color: #f8f8f8;\n  min-height: 100vh;\n}\n\n.result-section {\n  margin-top: 30rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n  padding: 20rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n  padding-left: 20rpx;\n  border-left: 8rpx solid #8a2be2;\n}\n\n.overview-section {\n  margin-bottom: 30rpx;\n}\n\n.overview-item, .detail-item, .advice-item {\n  background-color: #f9f9f9;\n  border-radius: 12rpx;\n  padding: 20rpx;\n  margin-bottom: 20rpx;\n}\n\n.item-title {\n  font-size: 28rpx;\n  color: #666;\n  margin-bottom: 10rpx;\n}\n\n.item-content {\n  font-size: 30rpx;\n  color: #333;\n  line-height: 1.6;\n}\n\n.detail-section {\n  margin-bottom: 30rpx;\n}\n\n.advice-section {\n  margin-top: 30rpx;\n}\n\n.birth-info-display {\n  background-color: #fff;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n}\n\n.info-content {\n  margin: 20rpx 0;\n}\n\n.info-item {\n  display: flex;\n"}