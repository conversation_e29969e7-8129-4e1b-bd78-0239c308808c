{"chunk": 3, "numChunks": 6, "fileHash": "IwGBwJ25Blubhn4hCt83o/e9MDZEM6l3SvRIm1A0vrE=", "filePath": "pages/fengshui/fengshui.wxml", "content": "        <text class=\"card-title\">环境信息</text>\n        <text class=\"card-icon\">🌳</text>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">周边环境</text>\n          <text class=\"optional\">选填</text>\n        </view>\n        <view class=\"checkbox-group\">\n          <view class=\"checkbox-item {{item.checked ? 'active' : ''}}\" \n                wx:for=\"{{surroundings}}\" \n                wx:key=\"value\"\n                bindtap=\"toggleSurrounding\"\n                data-index=\"{{index}}\">\n            <text class=\"checkbox-icon\">{{item.icon}}</text>\n            <text class=\"checkbox-text\">{{item.label}}</text>\n          </view>\n        </view>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">特殊位置</text>\n          <text class=\"optional\">选填</text>\n        </view>\n        <view class=\"checkbox-group\">\n          <view class=\"checkbox-item {{item.checked ? 'active' : ''}}\" \n                wx:for=\"{{specialLocations}}\" \n                wx:key=\"value\"\n                bindtap=\"toggleSpecialLocation\"\n                data-index=\"{{index}}\">\n            <text class=\"checkbox-icon\">{{item.icon}}</text>\n            <text class=\"checkbox-text\">{{item.label}}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 提示信息 -->\n    <view class=\"tips-card\">\n      <view class=\"tips-header\">\n        <text class=\"tips-icon\">💡</text>\n        <text class=\"tips-title\">温馨提示</text>\n      </view>\n      <view class=\"tips-content\">\n"}