{"chunk": 3, "numChunks": 5, "fileHash": "rXA9I8zblvy793sn1s5fHDcAnMr2WCJwMU+H52clkck=", "filePath": "pages/birth-info/birth-info.wxss", "content": ".picker-text.placeholder {\n  color: #bbb;\n}\n\n.picker-text.selected {\n  color: #333;\n}\n\n.picker-icon {\n  font-size: 32rpx;\n  opacity: 0.5;\n}\n\n.picker-tip {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 8rpx;\n  line-height: 1.4;\n}\n\n/* 提示卡片 */\n.tips-card {\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border-radius: 20rpx;\n  padding: 24rpx;\n  margin-bottom: 32rpx;\n  border: 1rpx solid #f39c12;\n}\n\n.tips-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.tips-icon {\n  font-size: 32rpx;\n  margin-right: 12rpx;\n}\n\n.tips-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #d68910;\n}\n\n.tips-content {\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.tip-item {\n  font-size: 26rpx;\n  color: #b7950b;\n  line-height: 1.4;\n}\n\n/* 提交按钮 */\n.submit-section {\n  margin-top: 40rpx;\n}\n\n.submit-btn {\n  width: 100%;\n  height: 96rpx;\n  border-radius: 24rpx;\n  font-size: 36rpx;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12rpx;\n  transition: all 0.3s ease;\n  border: none;\n}\n\n.submit-btn.active {\n"}