{"chunk": 3, "numChunks": 7, "fileHash": "YmBtAc19/HOz7eUBhWxGsON9KNrEblPnxcHiHsXvXv8=", "filePath": "pages/community/community.wxss", "content": "  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-more text {\n  font-size: 14px;\n  color: #999;\n}\n\n/* 没有更多数据样式 */\n.no-more {\n  text-align: center;\n  padding: 15px 0;\n}\n\n.no-more text {\n  font-size: 14px;\n  color: #999;\n}\n\n/* 空状态样式 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40px 0;\n}\n\n.empty-state image {\n  width: 120px;\n  height: 120px;\n  margin-bottom: 15px;\n}\n\n.empty-state text {\n  font-size: 14px;\n  color: #999;\n}\n\n/* 发布按钮样式 */\n.publish-btn {\n  position: fixed;\n  right: 20px;\n  bottom: 20px;\n  width: 50px;\n  height: 50px;\n  background: #8a2be2;\n  border-radius: 25px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4px 12px rgba(138,43,226,0.3);\n}\n\n.publish-btn image {\n  width: 24px;\n  height: 24px;\n}\n\n/* 发布弹窗样式 */\n.modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0,0,0,0.5);\n  z-index: 1000;\n  display: flex;\n  align-items: center;\n"}