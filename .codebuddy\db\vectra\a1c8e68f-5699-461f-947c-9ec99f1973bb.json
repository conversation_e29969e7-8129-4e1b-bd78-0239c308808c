{"chunk": 33, "numChunks": 38, "fileHash": "DpyinnhZJO6jrs0/VY0BXwrNjORlEQ1BeRywBJybhxQ=", "filePath": "pages/ai-chat/ai-chat.js", "content": "const app = getApp()\nPage({\n  scrollToBottom() {\n    // 使用 nextTick 确保DOM更新后再滚动\n    wx.nextTick(() => {\n      // 创建查询对象\n      const query = wx.createSelectorQuery().in(this);\n      \n      // 查询scroll-view的高度\n      query.select('.chat-messages').node();\n      query.select('.chat-messages').scrollOffset();\n      \n      query.exec((res) => {\n        if (res[0] && res[1]) {\n          // 获取scroll-view的实际内容高度\n          const scrollHeight = res[0].scrollHeight || 0;\n          const height = res[1].height || 0;\n          \n          // 计算需要滚动的距离\n          const scrollTop = scrollHeight - height;\n          \n          if (scrollTop > 0) {\n          this.setData({\n              scrollTop: scrollTop + 1000 // 加一个额外的值确保滚动到底\n            });\n          }\n        }\n      });\n    });\n  },\n  /**\n   * 格式化时间\n   */\n  formatTime(date) {\n    const hour = date.getHours()\n    const minute = date.getMinutes()\n    return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`\n  },\n  /**\n   * 保存消息历史\n   */\n  saveMessageHistory() {\n    try {\n      const messages = this.data.messages.slice(-config.features.maxMessageHistory)\n      wx.setStorageSync('chat_history', messages)\n    } catch (error) {\n      console.warn('保存聊天记录失败:', error)\n    }\n  },\n  /**\n   * 加载消息历史\n   */"}