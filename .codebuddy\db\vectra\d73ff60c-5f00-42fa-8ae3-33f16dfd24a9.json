{"chunk": 2, "numChunks": 4, "fileHash": "AzClV7baUsatVdHoLC/V2zxViVlpnMYViBcaI7+/JbU=", "filePath": "pages/customer-service/index.wxss", "content": "    transform: translateY(0);\n  }\n  30% {\n    transform: translateY(-10rpx);\n  }\n}\n\n/* 输入区域 */\n.input-section {\n  background-color: #fff;\n  border-top: 2rpx solid #f0f0f0;\n  padding: 20rpx;\n}\n\n/* 工具栏 */\n.toolbar {\n  display: flex;\n  gap: 30rpx;\n  padding: 10rpx 0;\n}\n\n.tool-item {\n  padding: 10rpx;\n}\n\n.tool-icon {\n  width: 48rpx;\n  height: 48rpx;\n}\n\n/* 输入框 */\n.input-box {\n  display: flex;\n  align-items: center;\n  gap: 20rpx;\n  margin-top: 20rpx;\n}\n\n.input {\n  flex: 1;\n  background-color: #f5f5f5;\n  border-radius: 36rpx;\n  padding: 20rpx 30rpx;\n  font-size: 28rpx;\n}\n\n.send-btn {\n  padding: 16rpx 40rpx;\n  border-radius: 36rpx;\n  background-color: #e0e0e0;\n  color: #fff;\n  font-size: 28rpx;\n}\n\n.send-btn.active {\n  background-color: #07c160;\n}\n\n/* 表情面板 */\n.emoji-panel {\n  height: 400rpx;\n  background-color: #fff;\n  padding: 20rpx;\n}\n\n/* 更多面板 */\n.more-panel {\n  height: 400rpx;\n  background-color: #fff;\n  padding: 40rpx;\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 40rpx;\n}\n\n.more-item {\n  display: flex;\n  flex-direction: column;\n"}