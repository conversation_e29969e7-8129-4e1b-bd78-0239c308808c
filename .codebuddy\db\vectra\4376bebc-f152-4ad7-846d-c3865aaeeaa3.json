{"chunk": 65, "numChunks": 74, "fileHash": "UWh+hTmku0OFKrrOI8FY7Ob/w0LwUBFFaP/YfXcJBpg=", "filePath": "utils/xingming/calculator.js", "content": "// 汉字笔画数据库\nclass XingmingCalculator {\n  generateJixiongList() {\n    const wuge = this.calculateWuge()\n    const sancai = this.calculateSancai()\n    \n    return [\n      {\n        type: '姓名吉凶',\n        description: this.getNameJixiong()\n      },\n      {\n        type: '数理吉凶',\n        description: this.getWugeJixiong(wuge.list)\n      },\n      {\n        type: '三才吉凶',\n        description: this.getSancaiJixiong(sancai)\n      },\n      {\n        type: '八字吉凶',\n        description: this.getBaziJixiong()\n      }\n    ]\n  }\n  // 生成建议列表\n  generateAdviceList() {\n    return [\n      {\n        title: '姓名用字',\n        content: this.getNameAdvice()\n      },\n      {\n        title: '五行调节',\n        content: this.getWuxingAdvice()\n      },\n      {\n        title: '事业发展',\n        content: this.getCareerAdvice()\n      },\n      {\n        title: '健康运势',\n        content: this.getHealthAdvice()\n      }\n    ]\n  }\n  // 工具方法\n  calculateTotalStrokes() {\n    let total = 0\n    for (const char of this.surname) {\n      total += STROKE_DATA[char] || 0\n    }\n    for (const char of this.givenName) {\n      total += STROKE_DATA[char] || 0\n    }\n    return total\n  }\n  calculateTianGe() {\n    return this.surname.length === 1 ? \n      STROKE_DATA[this.surname] + 1 :\n      STROKE_DATA[this.surname[0]] + STROKE_DATA[this.surname[1]]\n  }"}