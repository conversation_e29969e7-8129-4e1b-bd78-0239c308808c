{"chunk": 6, "numChunks": 10, "fileHash": "lg9EuzAhq20Uogzg9r07DedW1yISmqoEvsYKCcdN8GI=", "filePath": "utils/fengshui/calculator.js", "content": "// 八卦方位数据\nclass FengshuiCalculator {\n  calculateOfficePositions(mainDirection, positions) {\n    // 办公场所吉凶方位计算\n    const auspiciousDirections = {\n      '南': ['会议室', '接待区'],\n      '东': ['主管办公室', '创意区'],\n      '北': ['财务室', '档案室'],\n      '西': ['员工工位', '休息区']\n    };\n\n    const inauspiciousDirections = {\n      '东南': ['卫生间', '杂物间'],\n      '西北': ['打印室', '设备间'],\n      '西南': ['走廊', '通道'],\n      '东北': ['储藏室', '管道间']\n    };\n\n    for (const [direction, usage] of Object.entries(auspiciousDirections)) {\n      if (this.isCompatibleDirection(mainDirection, direction)) {\n        positions.auspicious.push({\n          direction,\n          recommendedUsage: usage.join('、'),\n          description: `此方位适合设置${usage.join('、')}，有助于提升工作效率。`\n        });\n      }\n    }\n\n    for (const [direction, usage] of Object.entries(inauspiciousDirections)) {\n      if (!this.isCompatibleDirection(mainDirection, direction)) {\n        positions.inauspicious.push({\n          direction,\n          avoidance: `避免设置${usage.join('、')}`,\n          description: `此方位不宜设置${usage.join('、')}，以免影响办公环境。`\n        });\n      }\n    }\n  }"}