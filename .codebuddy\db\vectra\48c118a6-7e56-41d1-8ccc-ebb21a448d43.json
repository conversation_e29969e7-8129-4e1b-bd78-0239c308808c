{"chunk": 10, "numChunks": 11, "fileHash": "iGKX2KeXQCBpUHYCjVhl7Obd6FTaj6FiHotsH10wGkE=", "filePath": "frontend-implementation/pages/analysis/index.js", "content": "// 命理分析页面\nPage({\n  prepareAnalysisData() {\n    const { formData, birthInfo, analysisType } = this.data\n    \n    const baseData = {\n      analysis_type: analysisType,\n      question: formData.question,\n      description: formData.description,\n      focus_area: formData.focus_area\n    }\n    \n    // 如果需要出生信息\n    if (this.data.analysisConfig.requiresBirthInfo && birthInfo) {\n      baseData.birth_info = birthInfo\n    }\n    \n    // 合婚分析需要伴侣信息\n    if (analysisType === 'marriage' && formData.partner_info) {\n      baseData.partner_info = formData.partner_info\n    }\n    \n    return baseData\n  },\n  /**\n   * 表单验证\n   */\n  validateForm() {\n    const { formData, analysisConfig, hasBirthInfo } = this.data\n    \n    // 检查出生信息\n    if (analysisConfig.requiresBirthInfo && !hasBirthInfo) {\n      showToast({\n        title: '请先完善出生信息',\n        icon: 'none'\n      })\n      return false\n    }\n    \n    // 检查问题描述\n    if (!formData.question.trim()) {\n      showToast({\n        title: '请输入您的问题',\n        icon: 'none'\n      })\n      return false\n    }\n    \n    // 合婚分析需要伴侣信息\n    if (this.data.analysisType === 'marriage' && !formData.partner_info) {\n      showToast({\n        title: '请添加伴侣信息',\n        icon: 'none'\n      })\n      return false\n    }\n    \n    return true\n  },\n  /**\n   * 添加伴侣信息\n   */"}