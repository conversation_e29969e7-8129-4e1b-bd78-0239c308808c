{"chunk": 10, "numChunks": 15, "fileHash": "ydfcj+4tZs2HvARUdw6TErn3fg1suYaeqU0s+F1D6wI=", "filePath": "pages/hehun/index.js", "content": "const app = getApp()\nPage({\n  async analyzeMarriage() {\n    try {\n      const baziCalculator = new BaziCalculator();\n      const marriageCalculator = new MarriageCalculator();\n      // 计算男方八字\n      const maleResult = await baziCalculator.calculate({\n        birthDate: this.data.maleBirthDate,\n        birthTime: this.data.maleBirthTime,\n        gender: 'male'\n      });\n      // 计算女方八字\n      const femaleResult = await baziCalculator.calculate({\n        birthDate: this.data.femaleBirthDate,\n        birthTime: this.data.femaleBirthTime,\n        gender: 'female'\n      });\n      // 计算合婚结果\n      const marriageResult = await marriageCalculator.calculate({\n        male: maleResult,\n        female: femaleResult\n      });"}