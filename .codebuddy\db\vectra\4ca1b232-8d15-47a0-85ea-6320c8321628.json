{"chunk": 13, "numChunks": 19, "fileHash": "PEPeuJm140RAD7VBd85WlnsfcWUdoorqVSQj8ISByl0=", "filePath": "pages/ai-chat/ai-chat.wxss", "content": "  animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(20rpx) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n/* 欢迎区域 */\n.welcome-section {\n  margin-bottom: 50rpx;\n  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.welcome-card {\n  background: var(--gradient-card);\n  border-radius: 28rpx;\n  padding: 48rpx;\n  text-align: center;\n  box-shadow: 0 12rpx 40rpx var(--shadow-color);\n  margin-bottom: 36rpx;\n  border: 1rpx solid rgba(232, 229, 255, 0.5);\n  position: relative;\n  overflow: hidden;\n}\n\n.welcome-card::after {\n  content: '';\n  position: absolute;\n  top: -50%;\n  right: -50%;\n  width: 200%;\n  height: 200%;\n  background: radial-gradient(circle, rgba(121, 40, 202, 0.05) 0%, transparent 70%);\n  animation: pulse 4s ease-in-out infinite;\n}\n\n.welcome-avatar {\n  font-size: 80rpx;\n  margin-bottom: 24rpx;\n  display: inline-block;\n  animation: gentle-float 3s ease-in-out infinite;\n}\n\n.welcome-title {\n  display: block;\n  font-size: 40rpx;\n  font-weight: 600;\n  background: var(--gradient-primary);\n"}