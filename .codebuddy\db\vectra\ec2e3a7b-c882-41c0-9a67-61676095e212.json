{"chunk": 11, "numChunks": 18, "fileHash": "/DUygIsVyFRM4gmsdi8uiMJN8RFlyElKDKTgRRrPFCI=", "filePath": "utils/hehun/calculator.js", "content": "// 五行生克关系\nclass HehunCalculator {\n  isZhiHe(zhi1, zhi2) {\n    const zhiHe = {\n      '子丑': true, '寅亥': true, '卯戌': true,\n      '辰酉': true, '巳申': true, '午未': true\n    }\n    return zhiHe[zhi1 + zhi2] || zhiHe[zhi2 + zhi1]\n  }\n  formatBaziString(bazi) {\n    return `${bazi.year.gan}${bazi.year.zhi} ${bazi.month.gan}${bazi.month.zhi} ${bazi.day.gan}${bazi.day.zhi} ${bazi.time.gan}${bazi.time.zhi}`\n  }\n  getScoreDescription(score) {\n    if (score >= 90) return '天作之合，百年好合'\n    if (score >= 80) return '相配良缘，幸福美满'\n    if (score >= 70) return '般配和谐，可结良缘'\n    if (score >= 60) return '缘分尚可，需互相包容'\n    if (score >= 50) return '缘分一般，需要磨合'\n    return '八字相冲，婚姻易有波折'\n  }"}