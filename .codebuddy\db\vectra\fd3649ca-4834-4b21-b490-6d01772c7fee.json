{"chunk": 7, "numChunks": 76, "fileHash": "lPYWZE8QItgwSyEsMXHtl6y/HYBCI8BdGaV+1l8ICMA=", "filePath": "docs/md/API_INTERFACE_DOCUMENTATION.md", "content": "// 意图配置表 (intent_configs)\n{\n  \"id\": \"string\",                   // 配置ID\n  \"intent_name\": \"string\",          // 意图名称\n  \"keywords\": [\"string\"],           // 关键词列表\n  \"patterns\": [\"string\"],           // 正则模式\n  \"confidence_threshold\": \"number\", // 置信度阈值\n  \"response_templates\": [\"string\"], // 回复模板\n  \"actions\": [\"object\"],            // 关联操作\n  \"requires_birth_info\": \"boolean\", // 是否需要出生信息\n  \"points_cost\": \"number\",          // 积分消费\n  \"is_active\": \"boolean\",           // 是否启用\n  \"priority\": \"number\",             // 优先级\n  \"created_at\": \"datetime\",\n  \"updated_at\": \"datetime\"\n}\n\n// AI配置表 (ai_configs)\n{\n  \"id\": \"string\",                   // 配置ID\n  \"config_type\": \"string\",          // 配置类型 (model/chat/intent)\n  \"config_name\": \"string\",          // 配置名称\n  \"config_data\": \"json\",            // 配置数据\n  \"version\": \"string\",              // 版本\n  \"is_active\": \"boolean\",           // 是否启用\n  \"effective_time\": \"datetime\",     // 生效时间\n  \"created_by\": \"string\",           // 创建者\n  \"created_at\": \"datetime\",\n  \"updated_at\": \"datetime\"\n}\n\n// 知识库文档表 (knowledge_documents)\n{\n  \"id\": \"string\",                   // 文档ID\n  \"title\": \"string\",                // 文档标题\n  \"content\": \"text\",                // 文档内容\n  \"summary\": \"text\",                // 文档摘要\n"}