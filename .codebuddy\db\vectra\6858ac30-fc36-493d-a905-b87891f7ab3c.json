{"chunk": 12, "numChunks": 13, "fileHash": "RdYUonhiW47SLv0/ZKSqV2JJEqqe4g6tc2PZPdqonRM=", "filePath": "pages/post-detail/post-detail.js", "content": "const app = getApp()\nPage({\n  async submitComment() {\n    const content = this.data.commentText.trim()\n    if (!content) {\n      wx.showToast({\n        title: '请输入评论内容',\n        icon: 'none'\n      })\n      return\n    }\n    wx.showLoading({\n      title: '发送中...',\n      mask: true\n    })"}