{"chunk": 5, "numChunks": 13, "fileHash": "UUsAqDc9MY1pjBTLokkGc1GjjJmTwiOLz2lDZFa/4nc=", "filePath": "pages/index/index.wxss", "content": ".category-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 30rpx 15rpx;\n  background: white;\n  border-radius: 25rpx;\n  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.1);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.category-item::before {\n  content: '';\n  position: absolute;\n  top: -50%;\n  left: -50%;\n  width: 200%;\n  height: 200%;\n  background: linear-gradient(45deg, transparent, rgba(149, 117, 205, 0.1), transparent);\n  transform: rotate(45deg);\n  transition: all 0.6s ease;\n  opacity: 0;\n}\n\n.category-item:active {\n  transform: scale(0.95);\n  box-shadow: 0 4rpx 12rpx rgba(149, 117, 205, 0.2);\n}\n\n.category-item:active::before {\n  opacity: 1;\n  animation: shimmer 0.6s ease;\n}\n\n@keyframes shimmer {\n  0% { transform: rotate(45deg) translateX(-100%); }\n  100% { transform: rotate(45deg) translateX(100%); }\n}\n\n.category-icon {\n  width: 80rpx;\n  height: 80rpx;\n  margin-bottom: 15rpx;\n  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));\n}\n\n.category-name {\n  font-size: 26rpx;\n  color: #666;\n  text-align: center;\n  font-weight: 500;\n}\n\n"}