{"chunk": 3, "numChunks": 19, "fileHash": "PEPeuJm140RAD7VBd85WlnsfcWUdoorqVSQj8ISByl0=", "filePath": "pages/ai-chat/ai-chat.wxss", "content": "  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);\n}\n\n.card-icon {\n  font-size: 64rpx;\n  margin-bottom: 16rpx;\n  display: block;\n}\n\n.card-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 8rpx;\n}\n\n.card-desc {\n  font-size: 24rpx;\n  color: #6b7280;\n  line-height: 1.4;\n}\n\n.card-badge {\n  position: absolute;\n  top: 12rpx;\n  right: 12rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  font-size: 20rpx;\n  padding: 4rpx 8rpx;\n  border-radius: 8rpx;\n}\n\n/* 使用提示 */\n.usage-tips {\n  background: rgba(102, 126, 234, 0.05);\n  border-radius: 16rpx;\n  padding: 24rpx;\n  margin-top: 32rpx;\n  border: 1rpx solid rgba(102, 126, 234, 0.1);\n}\n\n.tips-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 16rpx;\n}\n\n.tips-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.tip-item {\n  font-size: 24rpx;\n  color: #6b7280;\n  line-height: 1.5;\n}\n\n/* 消息列表 */\n.message-list {\n  padding-bottom: 20rpx;\n}\n\n"}