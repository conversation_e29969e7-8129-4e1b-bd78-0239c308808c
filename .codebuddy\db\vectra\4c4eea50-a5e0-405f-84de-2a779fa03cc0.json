{"chunk": 3, "numChunks": 5, "fileHash": "l8atZPeX8jkot2mARj8XbI3v0y6T6d3LZJDLwbL7UTg=", "filePath": "pages/settings/index.js", "content": "const app = getApp()\nPage({\n  data: {\n    // 通用设置\n    generalSettings: {\n      darkMode: false,\n      language: 'zh_CN',\n      fontSize: 'medium'\n    },\n    // 隐私设置\n    privacySettings: {\n      showOnline: true,\n      allowSearch: true,\n      showLastSeen: true\n    },\n    // 消息通知\n    notificationSettings: {\n      pushEnabled: true,\n      sound: true,\n      vibrate: true,\n      showPreview: true\n    },\n    // 字体大小选项\n    fontSizeOptions: [\n      { value: 'small', label: '小' },\n      { value: 'medium', label: '中' },\n      { value: 'large', label: '大' }\n    ],\n    // 语言选项\n    languageOptions: [\n      { value: 'zh_CN', label: '简体中文' },\n      { value: 'zh_TW', label: '繁体中文' },\n      { value: 'en_US', label: 'English' }\n    ],\n    // 缓存大小\n    cacheSize: '0MB'\n  },\n  onLoad() {\n    this.loadSettings()\n    this.calculateCacheSize()\n  },\n  // 加载设置\n  loadSettings() {\n    try {\n      const settings = wx.getStorageSync('appSettings')\n      if (settings) {\n        this.setData({\n          generalSettings: { ...this.data.generalSettings, ...settings.general },\n          privacySettings: { ...this.data.privacySettings, ...settings.privacy },\n          notificationSettings: { ...this.data.notificationSettings, ...settings.notification }\n        })\n      }\n    } catch (err) {\n      console.error('加载设置失败:', err)\n    }\n  },\n  // 保存设置"}