{"chunk": 1, "numChunks": 6, "fileHash": "QLx6GhwgSxPofuK/CUpTrLuKBgVetRumHHMileilK58=", "filePath": "pages/ziwei/ziwei.wxss", "content": "/* 紫微斗数命盘样式 */\n.ziwei-chart {\n  margin: 20rpx 0;\n}\n\n.chart-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 2rpx;\n  background-color: var(--border-color);\n  padding: 2rpx;\n  border-radius: 12rpx;\n}\n\n.grid-cell {\n  background-color: var(--card-background);\n  padding: 10rpx;\n  text-align: center;\n  min-height: 120rpx;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  border: 2rpx solid var(--border-color);\n}\n\n.palace-name {\n  font-size: 24rpx;\n  color: var(--text-secondary);\n  margin-bottom: 6rpx;\n}\n\n.main-star {\n  font-size: 28rpx;\n  color: var(--primary-color);\n  font-weight: bold;\n  margin-bottom: 6rpx;\n}\n\n.minor-stars {\n  font-size: 20rpx;\n  color: var(--text-light);\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: center;\n  gap: 4rpx;\n}\n\n/* 命盘信息样式 */\n.chart-info {\n  display: flex;\n  justify-content: space-around;\n  padding: 20rpx 0;\n  border-top: 2rpx solid var(--border-color);\n}\n\n.info-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.info-label {\n  font-size: 24rpx;\n  color: var(--text-secondary);\n  margin-bottom: 8rpx;\n}\n\n.info-value {\n  font-size: 28rpx;\n  color: var(--primary-color);\n  font-weight: bold;\n}\n\n"}