{"chunk": 8, "numChunks": 10, "fileHash": "xkTYshMjb1fCus39fEPzOqyYq9soXCPLKD0cb2FPCl8=", "filePath": "pages/marriage/marriage.js", "content": "// marriage.js\nPage({\n  async startAnalysis() {\n    try {\n      // 调用云函数进行八字合婚分析\n      const { maleInfo, femaleInfo } = this.data\n      // 计算男女双方八字\n      const maleBazi = calculateBazi(maleInfo.birthDate, maleInfo.birthTime)\n      const femaleBazi = calculateBazi(femaleInfo.birthDate, femaleInfo.birthTime)\n      const result = await wx.cloud.callFunction({\n        name: 'analyzeMarriage',\n        data: {\n          male: {\n            ...maleInfo,\n            bazi: maleBazi\n          },\n          female: {\n            ...femaleInfo,\n            bazi: femaleBazi\n          }\n        }\n      })\n      // 处理分析结果"}