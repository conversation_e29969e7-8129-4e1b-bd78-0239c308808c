{"chunk": 5, "numChunks": 6, "fileHash": "VToXv7ATbmi0am+Amh5z0qVhRJK7IjhEVx3t5LgFVgM=", "filePath": "pages/marriage/index.wxml", "content": "          <view class=\"detail-item\" wx:for=\"{{analysis[currentType].details}}\" wx:key=\"title\">\n            <view class=\"detail-title\">{{item.title}}</view>\n            <view class=\"detail-desc\">{{item.content}}</view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 建议卡片 -->\n    <view class=\"info-card\" wx:if=\"{{currentType !== 'overall' && analysis[currentType].suggestions}}\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">专业建议</text>\n        <text class=\"card-icon\">💡</text>\n      </view>\n      <view class=\"suggestions\">\n        <view class=\"suggestion-item\" wx:for=\"{{analysis[currentType].suggestions}}\" wx:key=\"index\">\n          <text class=\"suggestion-dot\">•</text>\n          <text class=\"suggestion-text\">{{item}}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 分享按钮 -->\n    <view class=\"submit-section\">\n      <button class=\"submit-btn active\" open-type=\"share\">\n        <text class=\"btn-text\">分享测算结果</text>\n        <text class=\"btn-icon\">📤</text>\n      </button>\n    </view>\n  </view>\n</view>\n"}