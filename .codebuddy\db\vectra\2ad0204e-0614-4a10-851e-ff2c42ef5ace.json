{"chunk": 3, "numChunks": 6, "fileHash": "Hr0NLKfsiy0m55e08Mw/WBiaaXrb6byHBEKkofIFTxk=", "filePath": "pages/fengshui/index.wxss", "content": "  gap: 20rpx;\n}\n\n.room-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 12rpx;\n}\n\n.room-item text {\n  font-size: 28rpx;\n  color: #666;\n}\n\n.number-picker {\n  width: 100%;\n  height: 88rpx;\n  background: #F8F9FD;\n  border-radius: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 32rpx;\n  color: #333;\n}\n\n/* 提交按钮 */\n.submit-section {\n  margin: 30rpx;\n}\n\n.submit-btn {\n  width: 100%;\n  height: 88rpx;\n  background: linear-gradient(135deg, #FF6B6B 0%, #FFB88C 100%);\n  border-radius: 44rpx;\n  color: #fff;\n  font-size: 32rpx;\n  font-weight: bold;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.submit-btn.disabled {\n  opacity: 0.6;\n}\n\n.price {\n  position: absolute;\n  right: 30rpx;\n  font-size: 28rpx;\n  font-weight: normal;\n}\n\n/* 评分展示 */\n.score-display {\n  display: flex;\n  align-items: baseline;\n  justify-content: center;\n  margin: 30rpx 0;\n}\n\n.score {\n  font-size: 80rpx;\n  font-weight: bold;\n  color: #FF6B6B;\n}\n\n.max-score {\n  font-size: 32rpx;\n  color: #999;\n  margin-left: 8rpx;\n}\n\n.score-desc {\n  text-align: center;\n"}