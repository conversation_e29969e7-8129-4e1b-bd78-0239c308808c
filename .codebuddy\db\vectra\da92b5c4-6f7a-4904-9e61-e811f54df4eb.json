{"chunk": 2, "numChunks": 5, "fileHash": "tA9ixVr6Arfzki0CzquWOFo8c2xDC4cgMIk+7vjNaBk=", "filePath": "pages/ziwei/ziwei.wxml", "content": "            <view class=\"detail-item\" wx:for=\"{{ziweiResult.lifeTrajectory.marriageDetails}}\" wx:key=\"index\">\n              <view class=\"detail-title\">{{item.title}}</view>\n              <view class=\"detail-content\">{{item.content}}</view>\n            </view>\n          </view>\n        </view>\n        <view class=\"trajectory-item\">\n          <view class=\"trajectory-title\">财运预测</view>\n          <view class=\"trajectory-text\">{{ziweiResult.lifeTrajectory.wealth}}</view>\n          <view class=\"trajectory-detail\">\n            <view class=\"detail-item\" wx:for=\"{{ziweiResult.lifeTrajectory.wealthDetails}}\" wx:key=\"index\">\n              <view class=\"detail-title\">{{item.title}}</view>\n              <view class=\"detail-content\">{{item.content}}</view>\n            </view>\n          </view>\n        </view>\n        <view class=\"trajectory-item\">\n          <view class=\"trajectory-title\">健康预测</view>\n          <view class=\"trajectory-text\">{{ziweiResult.lifeTrajectory.health}}</view>\n          <view class=\"trajectory-detail\">\n            <view class=\"detail-item\" wx:for=\"{{ziweiResult.lifeTrajectory.healthDetails}}\" wx:key=\"index\">\n              <view class=\"detail-title\">{{item.title}}</view>\n              <view class=\"detail-content\">{{item.content}}</view>\n            </view>\n          </view>\n        </view>\n        <view class=\"trajectory-item\">\n          <view class=\"trajectory-title\">大限流年</view>\n          <view class=\"fortune-scroll\">\n            <view class=\"fortune-list\">\n              <view class=\"fortune-item\" wx:for=\"{{ziweiResult.lifeTrajectory.fortuneYears}}\" wx:key=\"age\">\n                <view class=\"fortune-age\">{{item.age}}岁</view>\n                <view class=\"fortune-year\">{{item.year}}年</view>\n"}