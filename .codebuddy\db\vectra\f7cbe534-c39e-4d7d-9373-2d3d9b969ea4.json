{"chunk": 2, "numChunks": 76, "fileHash": "lPYWZE8QItgwSyEsMXHtl6y/HYBCI8BdGaV+1l8ICMA=", "filePath": "docs/md/API_INTERFACE_DOCUMENTATION.md", "content": "    \"token\": \"string\",              // JWT <PERSON>ken\n    \"refresh_token\": \"string\",      // 刷新Token\n    \"expires_in\": \"number\"          // Token过期时间(秒)\n  }\n}\n```\n\n#### 2. 获取用户信息\n```\nGET /api/user/profile\nAuthorization: Bearer {token}\n\nResponse:\n{\n  \"status\": \"success|error\",\n  \"message\": \"string\",\n  \"data\": {\n    \"user\": {\n      // 完整用户信息\n    },\n    \"birth_info\": {\n      // 出生信息 (如果已填写)\n    },\n    \"statistics\": {\n      \"total_consultations\": \"number\",  // 总咨询次数\n      \"total_points_earned\": \"number\",  // 总获得积分\n      \"total_points_spent\": \"number\",   // 总消费积分\n      \"sign_in_days\": \"number\",         // 连续签到天数\n      \"last_sign_in\": \"date\"            // 最后签到日期\n    }\n  }\n}\n```\n\n#### 3. 更新用户信息\n```\nPUT /api/user/profile\nAuthorization: Bearer {token}\nContent-Type: application/json\n\nRequest:\n{\n  \"nickname\": \"string\",             // 昵称 (可选)\n  \"avatar_url\": \"string\",           // 头像URL (可选)\n  \"phone\": \"string\",                // 手机号 (可选)\n  \"email\": \"string\"                 // 邮箱 (可选)\n}\n\nResponse:\n{\n  \"status\": \"success|error\",\n  \"message\": \"string\",\n  \"data\": {\n    \"user\": {\n      // 更新后的用户信息\n    }\n  }\n}\n```\n\n## 🎂 出生信息管理\n\n### 出生信息数据结构\n\n```javascript\n// 出生信息表 (birth_info)\n{\n"}