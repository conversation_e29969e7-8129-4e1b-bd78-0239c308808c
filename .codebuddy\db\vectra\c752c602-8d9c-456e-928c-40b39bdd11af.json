{"chunk": 7, "numChunks": 19, "fileHash": "PEPeuJm140RAD7VBd85WlnsfcWUdoorqVSQj8ISByl0=", "filePath": "pages/ai-chat/ai-chat.wxss", "content": "  font-size: 22rpx;\n  color: #6b7280;\n}\n\n/* 悬浮输入区域 - 屏幕中间位置 */\n.floating-input-container {\n  position: fixed;\n  top: 30%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 1000;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.floating-input-container.collapsed {\n  top: 50%;\n  bottom: auto;\n  left: auto;\n  right: 40rpx;\n  transform: translateY(-50%);\n}\n\n/* 键盘弹起时的适配 */\n.floating-input-container.expanded {\n  top: 30%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n/* 展开状态的输入面板 */\n.expanded-input-panel {\n  width: 90vw;\n  max-width: 600rpx;\n  background: white;\n  border-radius: 24rpx;\n  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);\n  overflow: hidden;\n  animation: panel-expand 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n/* 输入框区域 */\n.input-section {\n  padding: 32rpx;\n  border-bottom: 1rpx solid #f3f4f6;\n}\n\n.input-wrapper {\n  position: relative;\n}\n\n.message-input {\n  width: 95%;\n  min-height: 120rpx;\n  max-height: 300rpx;\n  background: #f9fafb;\n  border: 2rpx solid #e5e7eb;\n"}