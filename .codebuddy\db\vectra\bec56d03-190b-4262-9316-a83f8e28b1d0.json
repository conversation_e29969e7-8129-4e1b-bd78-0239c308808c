{"chunk": 13, "numChunks": 18, "fileHash": "/DUygIsVyFRM4gmsdi8uiMJN8RFlyElKDKTgRRrPFCI=", "filePath": "utils/hehun/calculator.js", "content": "// 五行生克关系\nclass HehunCalculator {\n  generateWuxingAnalysis() {\n    const maleWuxing = this.getMainWuxing(this.maleBazi)\n    const femaleWuxing = this.getMainWuxing(this.femaleBazi)\n\n    let analysis = `男方五行属${maleWuxing}，女方五行属${femaleWuxing}。`\n    \n    if (WU_XING_RELATIONS[maleWuxing].generates === femaleWuxing) {\n      analysis += '男方五行生女方，男方能给予女方支持和关爱。'\n    } else if (WU_XING_RELATIONS[femaleWuxing].generates === maleWuxing) {\n      analysis += '女方五行生男方，女方能给予男方支持和温暖。'\n    } else if (WU_XING_RELATIONS[maleWuxing].restricts === femaleWuxing) {\n      analysis += '男方五行克女方，需要男方多关心体贴女方。'\n    } else if (WU_XING_RELATIONS[femaleWuxing].restricts === maleWuxing) {\n      analysis += '女方五行克男方，需要女方多体谅包容男方。'\n    } else {\n      analysis += '双方五行平和，婚后生活稳定。'\n    }\n\n    return analysis\n  }"}