{"chunk": 1, "numChunks": 2, "fileHash": "6QyTJ5JNP12dXX0od431hqoQdy6MfszuFe76fWraxG0=", "filePath": "pages/profile/index.wxml", "content": "        <image class=\"grid-icon\" src=\"{{item.icon}}\" mode=\"aspectFit\"/>\n        <text class=\"grid-text\">{{item.name}}</text>\n      </view>\n    </view>\n  </view>\n\n  <!-- 设置列表 -->\n  <view class=\"section\">\n    <view class=\"section-title\">设置</view>\n    <view class=\"setting-list\">\n      <view class=\"setting-item\" \n            wx:for=\"{{settingList}}\" \n            wx:key=\"id\"\n            bindtap=\"navigateToSetting\"\n            data-url=\"{{item.url}}\">\n        <view class=\"setting-left\">\n          <image class=\"setting-icon\" src=\"{{item.icon}}\" mode=\"aspectFit\"/>\n          <text class=\"setting-name\">{{item.name}}</text>\n        </view>\n        <image class=\"arrow-icon\" src=\"/assets/icons/arrow-right.png\" mode=\"aspectFit\"/>\n      </view>\n    </view>\n  </view>\n\n  <!-- 退出登录 -->\n  <view class=\"logout-section\" wx:if=\"{{userInfo.nickName !== '未登录'}}\">\n    <button class=\"logout-btn\" bindtap=\"handleLogout\">退出登录</button>\n  </view>\n</view> \n"}