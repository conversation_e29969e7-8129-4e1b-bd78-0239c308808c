{"chunk": 1, "numChunks": 3, "fileHash": "BRAF9JGlBO00SEVvRN60jn0BCr8J7hnfkZOtWc/kJUA=", "filePath": "pages/divination/divination.wxml", "content": "  <view class=\"result-section\" wx:if=\"{{divinationResult}}\">\n    <view class=\"section-title\">占卜结果</view>\n    \n    <!-- 卦象/牌阵展示 -->\n    <view class=\"divination-display\">\n      <block wx:if=\"{{selectedType === 'yijing'}}\">\n        <view class=\"gua-display\">\n          <view class=\"gua-title\">本卦：{{divinationResult.benGua.name}}</view>\n          <view class=\"gua-image\">\n            <view class=\"yao-line\" wx:for=\"{{divinationResult.benGua.yao}}\" wx:key=\"index\">\n              <view class=\"yao-content {{item.type === 'yang' ? 'yang' : 'yin'}}\"></view>\n            </view>\n          </view>\n        </view>\n      </block>\n      \n      <block wx:elif=\"{{selectedType === 'tarot'}}\">\n        <view class=\"tarot-display\">\n          <view class=\"card-grid\">\n            <view class=\"card-item\" wx:for=\"{{divinationResult.cards}}\" wx:key=\"index\">\n              <image class=\"card-image\" src=\"{{item.image}}\" mode=\"aspectFit\"></image>\n              <text class=\"card-name\">{{item.name}}</text>\n            </view>\n          </view>\n        </view>\n      </block>\n    </view>\n\n    <!-- 解释区域 -->\n    <view class=\"interpretation-section\">\n      <view class=\"interpretation-item\">\n        <view class=\"item-title\">总体解释</view>\n        <view class=\"item-content\">{{divinationResult.interpretation.general}}</view>\n      </view>\n\n      <view class=\"interpretation-item\">\n        <view class=\"item-title\">事业运势</view>\n        <view class=\"item-content\">{{divinationResult.interpretation.career}}</view>\n"}