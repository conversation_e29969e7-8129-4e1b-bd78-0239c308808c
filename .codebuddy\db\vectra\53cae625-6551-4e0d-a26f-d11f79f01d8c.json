{"chunk": 6, "numChunks": 19, "fileHash": "PEPeuJm140RAD7VBd85WlnsfcWUdoorqVSQj8ISByl0=", "filePath": "pages/ai-chat/ai-chat.wxss", "content": ".message-actions {\n  margin-top: 16rpx;\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12rpx;\n}\n\n.action-button {\n  background: rgba(102, 126, 234, 0.1);\n  border: 1rpx solid rgba(102, 126, 234, 0.2);\n  border-radius: 16rpx;\n  padding: 12rpx 16rpx;\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  transition: all 0.3s ease;\n}\n\n.action-button:active {\n  background: rgba(102, 126, 234, 0.2);\n  transform: scale(0.95);\n}\n\n.action-text {\n  font-size: 26rpx;\n  color: #667eea;\n}\n\n.action-icon {\n  font-size: 24rpx;\n  color: #667eea;\n}\n\n/* 消息反馈 */\n.message-feedback {\n  margin-top: 12rpx;\n  display: flex;\n  gap: 8rpx;\n}\n\n.feedback-btn {\n  padding: 12rpx 16rpx;\n  border-radius: 20rpx;\n  background: rgba(0, 0, 0, 0.05);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8rpx;\n  transition: all 0.3s ease;\n  min-width: 80rpx;\n}\n\n.feedback-btn:active {\n  background: rgba(0, 0, 0, 0.1);\n  transform: scale(0.95);\n}\n\n.feedback-icon {\n  font-size: 24rpx;\n}\n\n.feedback-icon-img {\n  width: 24rpx;\n  height: 24rpx;\n}\n\n.feedback-text {\n"}