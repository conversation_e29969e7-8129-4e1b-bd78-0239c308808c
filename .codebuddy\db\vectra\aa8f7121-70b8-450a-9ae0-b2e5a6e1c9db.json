{"chunk": 72, "numChunks": 74, "fileHash": "UWh+hTmku0OFKrrOI8FY7Ob/w0LwUBFFaP/YfXcJBpg=", "filePath": "utils/xingming/calculator.js", "content": "// 汉字笔画数据库\nclass XingmingCalculator {\n  getWuxingAdvice() {\n    const { bazi } = this.options\n    const nameWuxing = this.getMainWuxing()\n    const riZhu = this.getRiZhuWuxing(bazi)\n    const daYun = this.getDaYunWuxing(bazi)\n    \n    let advice = '从五行配置来看，'\n    if (this.isWuxingHelpful(nameWuxing, riZhu)) {\n      advice += '姓名与八字相生，可以多用' + nameWuxing + '性物品来增强运势。'\n    } else if (this.isWuxingConflict(nameWuxing, riZhu)) {\n      advice += '需要注意调和五行，可以使用' + this.getSuitableElements(riZhu)[0] + '性物品来化解。'\n    }\n    \n    if (this.isWuxingHelpful(nameWuxing, daYun)) {\n      advice += '当前大运有利于发展，把握机会。'\n    } else {\n      advice += '当前大运需要谨慎行事，稳中求进。'\n    }\n    \n    return advice\n  }"}