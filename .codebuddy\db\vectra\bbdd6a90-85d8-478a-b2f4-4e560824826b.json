{"chunk": 0, "numChunks": 1, "fileHash": "k/3Utud+Ii3y6CvydaybPp3THfzwBuUMxQ8y3IteH2k=", "filePath": "pages/logs/logs.js", "content": "// logs.js\nconst app = getApp()\n\nPage({\n  data: {\n    logs: []\n  },\n  onLoad() {\n    try {\n      const logs = wx.getStorageSync('logs') || []\n      this.setData({\n        logs: logs.map(log => {\n          return {\n            date: app.globalData.util.formatTime(new Date(log)),\n            timeStamp: log\n          }\n        })\n      })\n      console.log('日志加载成功')\n    } catch (error) {\n      console.error('日志加载失败:', error)\n      this.setData({\n        logs: []\n      })\n    }\n  }\n})\n"}