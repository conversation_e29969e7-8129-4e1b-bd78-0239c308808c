{"chunk": 5, "numChunks": 11, "fileHash": "i8jYYz/gAcuprKem1BBbiBaSat5ZFs+bjwiR2/ZQErE=", "filePath": "pages/birth-info/birth-info.js", "content": "const globalState = require('../../utils/global-state')\nPage({\n  onDateTimeColumnChange(e) {\n    const { column, value } = e.detail;\n    const { dateTime, dateTimeArray, years, months } = this.data;\n    const newDateTimeArray = [...dateTimeArray];\n    newDateTimeArray[column] = value;\n\n    // 如果修改了年份或月份，需要更新天数\n    if (column === 0 || column === 1) {\n      const year = parseInt(years[newDateTimeArray[0]]);\n      const month = parseInt(months[newDateTimeArray[1]]);\n      const newDays = this.updateDays(year, month);\n      const newDateTime = [...dateTime];\n      newDateTime[2] = newDays;\n      \n      this.setData({\n        dateTime: newDateTime\n      });\n    }\n\n    this.setData({\n      dateTimeArray: newDateTimeArray\n    });\n  },\n  onDateTimeChange(e) {\n    const { value } = e.detail;\n    const { dateTime } = this.data;\n\n    const selectedDateTime = dateTime.map((arr, index) => arr[value[index]]).join('');\n\n    this.setData({\n      dateTimeArray: value,\n      selectedDateTime\n    });\n\n    // 检查是否可以提交\n    this.checkCanSubmit();\n  },\n  onNameInput(e) {\n    this.setData({\n      name: e.detail.value\n    });\n    this.checkCanSubmit();\n  },\n  // 新的性别选择方法\n  selectGender(e) {\n    const gender = e.currentTarget.dataset.gender;\n    this.setData({\n      gender\n    });\n    this.checkCanSubmit();\n  },\n  // 兼容旧的性别选择方法\n  onGenderChange(e) {\n    this.setData({\n      gender: e.detail.value\n    });\n    this.checkCanSubmit();\n  },\n  onZodiacChange(e) {\n    this.setData({\n      zodiacIndex: parseInt(e.detail.value)\n    });\n  },"}