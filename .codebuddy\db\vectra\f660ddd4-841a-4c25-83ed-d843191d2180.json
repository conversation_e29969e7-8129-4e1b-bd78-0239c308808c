{"chunk": 8, "numChunks": 15, "fileHash": "An9WOmGw20FYVB4+SrCnlnnpOBHN6cohdD4SvY7rXL8=", "filePath": "pages/ziwei/ziwei.js", "content": "// ziwei.js\nPage({\n  },\n  // 生成大限流年\n  generateFortuneYears(birthDate, mingGong) {\n    const birthYear = new Date(birthDate).getFullYear()\n    const fortuneYears = []\n    \n    // 生成从出生到80岁的大限流年\n    for (let age = 0; age <= 80; age += 10) {\n      const year = birthYear + age\n      const palace = ((mingGong + Math.floor(age / 10)) % 12) || 12\n      fortuneYears.push({\n        age: age,\n        year: year,\n        palace: `第${palace}宫`\n      })\n    }\n    \n    return fortuneYears\n  },\n  // 生成事业详细预测"}