{"chunk": 5, "numChunks": 18, "fileHash": "yi4gCVr62AiqkHDrZGxGnPy2D7On+5yxIebQ7IGvQ04=", "filePath": "pages/community/community.js", "content": "// pages/community/community.js\nPage({\n  onReachBottom() {\n    if (this.data.hasMore && !this.data.isLoading && this.data.isCloudInited) {\n      this.loadPosts()\n    }\n  },\n  /**\n   * 用户点击右上角分享\n   */\n  onShareAppMessage(e) {\n    if (e.from === 'button') {\n      const { post } = e.target.dataset\n      return {\n        title: post.title,\n        path: `/pages/post-detail/post-detail?id=${post.id}`,\n        imageUrl: post.images?.[0] // 使用帖子第一张图片作为分享图片\n      }\n    }\n    return {\n      title: '玄学社区',\n      path: '/pages/community/community'\n    }\n  },\n  /**\n   * 加载帖子列表\n   */"}