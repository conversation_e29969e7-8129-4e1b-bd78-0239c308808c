{"chunk": 5, "numChunks": 18, "fileHash": "/DUygIsVyFRM4gmsdi8uiMJN8RFlyElKDKTgRRrPFCI=", "filePath": "utils/hehun/calculator.js", "content": "// 五行生克关系\nclass HehunCalculator {\n  calculateBaziScore() {\n    let score = 0\n    const maleElements = this.getBaziElements(this.maleBazi)\n    const femaleElements = this.getBaziElements(this.femaleBazi)\n\n    // 天干相合\n    score += this.calculateGanHe(maleElements.gan, femaleElements.gan)\n    \n    // 地支相合\n    score += this.calculateZhiHe(maleElements.zhi, femaleElements.zhi)\n    \n    // 日柱相合\n    score += this.calculateRiZhuHe(\n      maleElements.gan[2], maleElements.zhi[2],\n      femaleElements.gan[2], femaleElements.zhi[2]\n    )\n\n    return Math.min(100, score)\n  }\n  // 计算五行相合度\n  calculateWuxingScore() {\n    let score = 0\n    const maleWuxing = this.getMainWuxing(this.maleBazi)\n    const femaleWuxing = this.getMainWuxing(this.femaleBazi)\n\n    // 相生关系\n    if (WU_XING_RELATIONS[maleWuxing].generates === femaleWuxing) {\n      score += 40\n    }\n    if (WU_XING_RELATIONS[femaleWuxing].generates === maleWuxing) {\n      score += 40\n    }\n\n    // 相克关系\n    if (WU_XING_RELATIONS[maleWuxing].restricts === femaleWuxing) {\n      score -= 20\n    }\n    if (WU_XING_RELATIONS[femaleWuxing].restricts === maleWuxing) {\n      score -= 20\n    }\n\n    return Math.max(0, Math.min(100,"}