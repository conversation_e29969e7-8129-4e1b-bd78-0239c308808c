{"chunk": 68, "numChunks": 74, "fileHash": "UWh+hTmku0OFKrrOI8FY7Ob/w0LwUBFFaP/YfXcJBpg=", "filePath": "utils/xingming/calculator.js", "content": "// 汉字笔画数据库\nclass XingmingCalculator {\n  getMainWuxing() {\n    const renGe = this.calculateRenGe()\n    return this.getWuxingAttribute(renGe)\n  }\n  getRiZhuWuxing(bazi) {\n    return bazi.day.ganWuxing\n  }\n  getDaYunWuxing(bazi) {\n    return bazi.currentDayun.ganWuxing\n  }\n  isWuxingHelpful(wuxing1, wuxing2) {\n    const relations = {\n      '金': ['土'],\n      '木': ['水'],\n      '水': ['金'],\n      '火': ['木'],\n      '土': ['火']\n    }\n    return relations[wuxing1]?.includes(wuxing2) || relations[wuxing2]?.includes(wuxing1)\n  }\n  isWuxingConflict(wuxing1, wuxing2) {\n    const conflicts = {\n      '金': ['火'],\n      '木': ['金'],\n      '水': ['土'],\n      '火': ['水'],\n      '土': ['木']\n    }\n    return conflicts[wuxing1]?.includes(wuxing2)\n  }\n  isElementSuitableForGender(element) {\n    const suitableElements = {\n      'male': ['金', '火'],\n      'female': ['木', '水']\n    }\n    return suitableElements[this.options.gender]?.includes(element)\n  }"}