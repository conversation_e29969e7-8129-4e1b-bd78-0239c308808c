{"chunk": 4, "numChunks": 6, "fileHash": "3s34je0qL5+ULluXZdiBSih2mwyLkPtuCPPtZ9V/C7o=", "filePath": "pages/community/community.wxml", "content": "      <view class=\"modal-body\">\n        <input class=\"title-input\" placeholder=\"请输入标题\" value=\"{{newPost.title}}\" bindinput=\"onTitleInput\"/>\n        <textarea class=\"content-input\" placeholder=\"请输入内容\" value=\"{{newPost.content}}\" bindinput=\"onContentInput\"/>\n        \n        <!-- 话题标签输入 -->\n        <view class=\"tags-input\">\n          <text class=\"input-label\">话题标签（用空格分隔）</text>\n          <input placeholder=\"例如：命理 风水 占卜\" value=\"{{newPost.tagsInput}}\" bindinput=\"onTagsInput\"/>\n          <view class=\"tags-preview\" wx:if=\"{{newPost.tags && newPost.tags.length > 0}}\">\n            <text class=\"tag\" wx:for=\"{{newPost.tags}}\" wx:key=\"*this\">#{{item}}</text>\n          </view>\n        </view>\n        \n        <view class=\"image-uploader\">\n          <view class=\"image-list\">\n            <view class=\"image-item\" wx:for=\"{{newPost.images}}\" wx:key=\"*this\">\n              <image src=\"{{item}}\" mode=\"aspectFill\"></image>\n              <view class=\"delete-btn\" bindtap=\"deleteImage\" data-index=\"{{index}}\">×</view>\n            </view>\n            <view class=\"upload-btn\" bindtap=\"chooseImage\" wx:if=\"{{newPost.images.length < 9}}\">\n              <image src=\"/assets/icons/community/upload.png\"></image>\n              <text>上传图片</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"category-selector\">\n          <picker \n            bindchange=\"onCategoryChange\" \n            value=\"{{newPost.categoryIndex}}\" \n            range=\"{{publishCategories}}\" \n            range-key=\"name\"\n          >\n            <view class=\"picker\">\n              分类：{{publishCategories[newPost.categoryIndex].name}}\n"}