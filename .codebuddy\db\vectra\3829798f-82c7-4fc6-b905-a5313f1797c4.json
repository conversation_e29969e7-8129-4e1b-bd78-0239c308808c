{"chunk": 0, "numChunks": 6, "fileHash": "u49Rqom2MlWUCSY5d2kW1CfpdWY95ku7je8yGKj9+oc=", "filePath": "pages/name-test/name-test.wxss", "content": "/* 姓名测试页面样式 */\n.page-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding-bottom: 60rpx;\n}\n\n.header {\n  padding: 60rpx 40rpx 40rpx;\n  text-align: center;\n  color: white;\n}\n\n.title {\n  font-size: 56rpx;\n  font-weight: bold;\n  margin-bottom: 20rpx;\n}\n\n.subtitle {\n  font-size: 28rpx;\n  opacity: 0.9;\n}\n\n/* 输入区域 */\n.input-section {\n  margin: 40rpx 30rpx;\n  background: white;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.1);\n}\n\n.input-group {\n  margin-bottom: 40rpx;\n}\n\n.input-group:last-of-type {\n  margin-bottom: 60rpx;\n}\n\n.label {\n  font-size: 32rpx;\n  color: #333;\n  margin-bottom: 20rpx;\n  font-weight: 500;\n}\n\n.name-input {\n  width: 100%;\n  height: 80rpx;\n  border: 2rpx solid #e0e0e0;\n  border-radius: 12rpx;\n  padding: 0 20rpx;\n  font-size: 32rpx;\n  background: #f9f9f9;\n}\n\n.name-input:focus {\n  border-color: #667eea;\n  background: white;\n}\n\n.gender-group {\n  display: flex;\n  gap: 40rpx;\n}\n\n.radio-item {\n  display: flex;\n  align-items: center;\n  gap: 10rpx;\n"}