{"chunk": 51, "numChunks": 55, "fileHash": "fCZ6nFoNZXQqhvADUzMwGWD6jOU+ajj61B8BD3CwDgs=", "filePath": "pages/name-test/name-test.js", "content": "// 姓名测试页面逻辑\nPage({\n  },\n  // 获取事业分析\n  getCareerAnalysis(number) {\n    const analyses = {\n      1: '事业运势强劲，容易获得成功',\n      2: '需要与人合作才能在事业上有所建树',\n      3: '适合从事创意性工作，发展前景良好',\n      4: '事业发展稳定，但进展相对缓慢',\n      5: '事业多变，需要把握机会'\n    };\n    return analyses[number % 5 + 1] || '事业发展平稳，需要持续努力';\n  },\n  // 获取婚姻分析\n  getMarriageAnalysis(number, gender) {\n    const genderText = gender === 'male' ? '他' : '她';\n    const analyses = {\n      1: `${genderText}在感情中比较主动，容易找到合适的伴侣`,\n      2: `${genderText}重视家庭和谐，婚姻生活美满`,\n      3: `${genderText}感情丰富，但需要学会专一`,\n      4: `${genderText}对感情认真负责，但表达能力需要提升`,\n      5: `${genderText}向往自由的感情生活，需要理解的伴侣`\n    };\n    return analyses[number % 5 + 1] || `${genderText}感情运势一般，需要主动经营`;\n  },\n  // 获取�"}