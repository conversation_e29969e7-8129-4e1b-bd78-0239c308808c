{"chunk": 1, "numChunks": 3, "fileHash": "h8VEoH7XgUTaD2cCx23MEZncAvUaCL5srdlMHNmVy4c=", "filePath": "pages/name/index.wxss", "content": ".result-section {\n  margin-top: 40rpx;\n}\n\n.result-card {\n  background: #fff;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n  padding-bottom: 20rpx;\n  border-bottom: 2rpx solid #eee;\n}\n\n.wuge-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 20rpx;\n}\n\n.grid-item {\n  background: #f8f8f8;\n  padding: 20rpx;\n  border-radius: 8rpx;\n  text-align: center;\n}\n\n.grid-name {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 8rpx;\n}\n\n.grid-number {\n  font-size: 36rpx;\n  color: #4a5568;\n  display: block;\n  margin-bottom: 8rpx;\n}\n\n.grid-meaning {\n  font-size: 24rpx;\n  color: #666;\n}\n\n.score-section {\n  text-align: center;\n  padding: 30rpx 0;\n}\n\n.total-score {\n  font-size: 72rpx;\n  font-weight: bold;\n  color: #4a5568;\n}\n\n.max-score {\n  font-size: 32rpx;\n  color: #666;\n}\n\n.score-desc {\n  font-size: 28rpx;\n  color: #666;\n  display: block;\n  margin-top: 20rpx;\n}\n\n.wuxing-analysis {\n  margin-top: 20rpx;\n}\n\n.wuxing-item {\n  margin-bottom: 20rpx;\n}\n\n"}