{"chunk": 2, "numChunks": 4, "fileHash": "euo6OT+uMcA1jHrVDL9HcA2afhD9hscN1rx91h/6LSA=", "filePath": "pages/birth-info/birth-info.wxml", "content": "        <text class=\"card-icon\">🎯</text>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">生肖</text>\n          <text class=\"optional\">选填</text>\n        </view>\n        <picker bindchange=\"onZodiacChange\" value=\"{{zodiacIndex}}\" range=\"{{zodiacList}}\">\n          <view class=\"picker-wrapper\">\n            <text class=\"picker-text {{zodiacList[zodiacIndex] ? 'selected' : 'placeholder'}}\">\n              {{zodiacList[zodiacIndex] || '请选择生肖'}}\n            </text>\n            <text class=\"picker-icon\">🐲</text>\n          </view>\n        </picker>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">幸运数字</text>\n          <text class=\"optional\">选填</text>\n        </view>\n        <picker bindchange=\"onNumberChange\" value=\"{{numberIndex}}\" range=\"{{numberList}}\">\n          <view class=\"picker-wrapper\">\n            <text class=\"picker-text {{numberList[numberIndex] ? 'selected' : 'placeholder'}}\">\n              {{numberList[numberIndex] || '请选择数字(1-62)'}}\n            </text>\n            <text class=\"picker-icon\">🔢</text>\n          </view>\n        </picker>\n      </view>\n    </view>\n\n    <!-- 提示信息 -->\n    <view class=\"tips-card\">\n      <view class=\"tips-header\">\n        <text class=\"tips-icon\">💡</text>\n        <text class=\"tips-title\">温馨提示</text>\n      </view>\n      <view class=\"tips-content\">\n        <text class=\"tip-item\">• 姓名和出生时间为必填项</text>\n"}