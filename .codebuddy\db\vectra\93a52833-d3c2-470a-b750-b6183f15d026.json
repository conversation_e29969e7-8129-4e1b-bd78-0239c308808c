{"chunk": 8, "numChunks": 10, "fileHash": "lg9EuzAhq20Uogzg9r07DedW1yISmqoEvsYKCcdN8GI=", "filePath": "utils/fengshui/calculator.js", "content": "// 八卦方位数据\nclass FengshuiCalculator {\n  calculateFactoryPositions(mainDirection, positions) {\n    // 厂房吉凶方位计算\n    const auspiciousDirections = {\n      '南': ['成品区', '展示区'],\n      '东': ['生产线', '加工区'],\n      '北': ['原料区', '仓储区'],\n      '西': ['办公区', '休息区']\n    };\n\n    const inauspiciousDirections = {\n      '东南': ['危险品存放', '废料区'],\n      '西北': ['污水处理', '垃圾站'],\n      '西南': ['设备间', '变电室'],\n      '东北': ['化学品', '易燃物']\n    };\n\n    for (const [direction, usage] of Object.entries(auspiciousDirections)) {\n      if (this.isCompatibleDirection(mainDirection, direction)) {\n        positions.auspicious.push({\n          direction,\n          recommendedUsage: usage.join('、'),\n          description: `此方位适合设置${usage.join('、')}，有利于生产效率。`\n        });\n      }\n    }\n\n    for (const [direction, usage] of Object.entries(inauspiciousDirections)) {\n      if (!this.isCompatibleDirection(mainDirection, direction)) {\n        positions.inauspicious.push({\n          direction,\n          avoidance: `避免设置${usage.join('、')}`,\n          description: `此方位不宜设置${usage.join('、')}，以免影响安全生产。`\n        });\n      }\n    }\n  }"}