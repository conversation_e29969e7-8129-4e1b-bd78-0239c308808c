{"chunk": 0, "numChunks": 3, "fileHash": "yBfSiux97Av5YAlaSefOy5NSKkeIfX1ubntqZcbY4Hg=", "filePath": "subpages/admin/logs/logs.wxss", "content": "/* subpages/admin/logs/logs.wxss */\n\n.logs-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n  padding: 20rpx;\n}\n\n.logs-header {\n  background: white;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n  margin-bottom: 30rpx;\n}\n\n.logs-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #8a2be2;\n  display: block;\n  margin-bottom: 20rpx;\n  text-align: center;\n}\n\n.logs-filters {\n  display: flex;\n  gap: 10rpx;\n  justify-content: center;\n}\n\n.filter-btn {\n  background: #f8f9fa;\n  color: #666;\n  border: none;\n  border-radius: 20rpx;\n  padding: 12rpx 24rpx;\n  font-size: 24rpx;\n  transition: all 0.3s ease;\n}\n\n.filter-btn.active {\n  background: linear-gradient(135deg, #8a2be2, #9932cc);\n  color: white;\n}\n\n.filter-btn:not(.active):active {\n  background: #e9ecef;\n}\n\n.logs-content {\n  background: white;\n  border-radius: 16rpx;\n  padding: 20rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n  margin-bottom: 30rpx;\n  max-height: 70vh;\n  overflow-y: auto;\n}\n\n.logs-list {\n  display: flex;\n"}