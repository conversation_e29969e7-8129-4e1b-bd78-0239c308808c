{"chunk": 8, "numChunks": 13, "fileHash": "UUsAqDc9MY1pjBTLokkGc1GjjJmTwiOLz2lDZFa/4nc=", "filePath": "pages/index/index.wxss", "content": "  left: 0;\n  right: 0;\n  height: 4rpx;\n  background: linear-gradient(90deg, #9575cd 0%, #b39ddb 100%);\n  transform: scaleX(0);\n  transform-origin: left;\n  transition: transform 0.3s ease;\n}\n\n.article-item:active::after {\n  transform: scaleX(1);\n}\n\n.article-item:active {\n  transform: translateY(2rpx);\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);\n}\n\n.article-image {\n  width: 160rpx;\n  height: 120rpx;\n  border-radius: 20rpx;\n  margin-right: 25rpx;\n  object-fit: cover;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n}\n\n.article-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.article-title {\n  font-size: 30rpx;\n  color: #333;\n  font-weight: 500;\n  margin-bottom: 15rpx;\n  line-height: 1.5;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n  transition: color 0.3s ease;\n}\n\n.article-meta {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 24rpx;\n  color: #999;\n}\n\n.article-author {\n  display: flex;\n  align-items: center;\n  padding-left: 25rpx;\n  position: relative;\n}\n\n.article-author::before {\n  content: '✍';\n  position: absolute;\n"}