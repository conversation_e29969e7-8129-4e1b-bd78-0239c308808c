{"chunk": 8, "numChunks": 9, "fileHash": "RJtqiMp6ufLzCDA9+EFDGWW4xytfcsd5HAKWaRu2Drc=", "filePath": "frontend-implementation/pages/index/index.js", "content": "// 首页 - 使用示例\ncreatePage({\n  onAnalysisTypeTap(e) {\n    const { type } = e.currentTarget.dataset\n    \n    // 检查是否需要出生信息\n    const needsBirthInfo = ['bazi', 'wuxing', 'ziwei', 'marriage'].includes(type)\n    \n    if (needsBirthInfo && !this.data.birthInfo) {\n      wx.showModal({\n        title: '需要出生信息',\n        content: '此功能需要您的出生信息，是否前往完善？',\n        confirmText: '去完善',\n        success: (res) => {\n          if (res.confirm) {\n            wx.navigateTo({\n              url: '/pages/birth-info/index'\n            })\n          }\n        }\n      })\n      return\n    }\n    \n    wx.navigateTo({\n      url: `/pages/analysis/index?type=${type}`\n    })\n  },\n  /**\n   * 点击快捷操作\n   */\n  onQuickActionTap(e) {\n    const { action } = e.currentTarget.dataset\n    \n    if (action.type === 'analysis') {\n      this.onAnalysisTypeTap({ currentTarget: { dataset: { type: action.analysis_type } } })\n    } else if (action.type === 'navigate') {\n      wx.navigateTo({\n        url: action.url\n      })\n    } else if (action.type === 'chat') {\n      wx.navigateTo({\n        url: '/pages/ai-chat/index'\n      })\n    }\n  },\n  /**\n   * 查看分析详情\n   */\n  onAnalysisDetailTap(e) {\n    const { analysisId } = e.currentTarget.dataset\n    wx.navigateTo({\n      url: `/pages/analysis-result/index?id=${analysisId}`\n    })\n  },\n  /**\n   * 查看聊天会话\n   */"}