{"chunk": 10, "numChunks": 12, "fileHash": "a4LdV5V9/sM95i4esh3ydW49XOc5FHg6OAvfDYJ8/so=", "filePath": "pages/name/index.js", "content": "const { NameCalculator } = require('../../utils/name/calculator');\nPage({\n  async analyzeName() {\n    try {\n      this.setData({\n        loading: false,\n        showResult: true,\n        wugeResult: [\n          { name: '天格', number: result.wuge.tian, meaning: result.wuge.tianMeaning },\n          { name: '人格', number: result.wuge.ren, meaning: result.wuge.renMeaning },\n          { name: '地格', number: result.wuge.di, meaning: result.wuge.diMeaning },\n          { name: '外格', number: result.wuge.wai, meaning: result.wuge.waiMeaning },\n          { name: '总格', number: result.wuge.zong, meaning: result.wuge.zongMeaning }\n        ],\n        nameScore: result.score,\n        scoreDescription: result.scoreDescription,\n        wuxingResult: [\n          { element: '金', percentage: result.wuxing.metal, color: '#FFD700' },\n          { element: '木', percentage: result.wuxing.wood, color: '#90EE90' },\n          { element: '水', percentage: result.wuxing.water, color: '#87CEEB' },\n          { element: '火', percentage: result.wuxing.fire, color: '#FF6B6B' },\n          { element: '土', percentage: result.wuxing.earth, color: '#DEB887' }\n        ],\n        nameDetails: result.details.map(item => ({\n          aspect: item.aspect,\n          description: item.description\n        })),"}