{"chunk": 2, "numChunks": 7, "fileHash": "nGMyzD5kIPI75b8ATQIO6k03owW27BytPf8s1C7ATWc=", "filePath": "pages/bazi/index.wxss", "content": "}\n\n/* 顶部banner */\n.banner {\n  position: relative;\n  width: 100%;\n  height: 360rpx;\n  overflow: hidden;\n}\n\n.banner-bg {\n  width: 100%;\n  height: 100%;\n}\n\n.banner-title {\n  position: absolute;\n  left: 40rpx;\n  bottom: 40rpx;\n  color: #fff;\n  z-index: 1;\n}\n\n.main-title {\n  font-size: 48rpx;\n  font-weight: bold;\n  display: block;\n  margin-bottom: 12rpx;\n  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);\n}\n\n.sub-title {\n  font-size: 28rpx;\n  opacity: 0.9;\n}\n\n/* 表单区域 */\n.form-section {\n  margin: 30rpx;\n  padding: 30rpx;\n  background: #fff;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);\n}\n\n.form-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 30rpx;\n}\n\n/* 性别选择器 */\n.gender-picker {\n  display: flex;\n  gap: 20rpx;\n}\n\n.gender-option {\n  flex: 1;\n  height: 88rpx;\n  background: #F8F9FD;\n  border-radius: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12rpx;\n}\n\n.gender-option.active {\n  background: #6236FF;\n  color: #fff;\n}\n\n.gender-icon {\n  width: 36rpx;\n  height: 36rpx;\n}\n\n"}