{"chunk": 1, "numChunks": 3, "fileHash": "grwM+uqFMSn0OFGeZxTcU6dtA4Dxde56TBRIWq2COXA=", "filePath": "pages/community/index.wxss", "content": "  color: #999;\n}\n\n/* 帖子内容 */\n.post-content {\n  margin-bottom: 20rpx;\n}\n\n.content-text {\n  font-size: 28rpx;\n  color: #333;\n  line-height: 1.6;\n  margin-bottom: 20rpx;\n  display: block;\n}\n\n.image-list {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 10rpx;\n}\n\n.post-image {\n  width: 100%;\n  height: 200rpx;\n  border-radius: 8rpx;\n}\n\n.post-image.single {\n  grid-column: span 3;\n  height: 400rpx;\n}\n\n/* 互动栏 */\n.interaction-bar {\n  display: flex;\n  justify-content: space-around;\n  padding-top: 20rpx;\n  border-top: 2rpx solid #f5f5f5;\n}\n\n.interaction-item {\n  display: flex;\n  align-items: center;\n  padding: 10rpx 30rpx;\n}\n\n.interaction-item .icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-right: 10rpx;\n}\n\n.interaction-item text {\n  font-size: 26rpx;\n  color: #666;\n}\n\n.interaction-item.liked {\n  color: #07c160;\n}\n\n.interaction-item.liked text {\n  color: #07c160;\n}\n\n/* 加载状态 */\n.loading-status {\n  padding: 30rpx;\n  text-align: center;\n}\n\n.loading {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.loading-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-right: 10rpx;\n}\n\n.loading text,\n.no-more,\n.empty text {\n  font-size: 26rpx;\n"}