{"chunk": 1, "numChunks": 5, "fileHash": "4lpNnoqGg1eEPtIgkdLgszOng5dzR4kuUtSZ8HRwytk=", "filePath": "pages/yijing/yijing.wxss", "content": "  color: var(--text-secondary);\n  font-size: 28rpx;\n}\n\n.value {\n  flex: 1;\n  color: var(--text-primary);\n  font-size: 28rpx;\n}\n\n.gender-label {\n  color: var(--text-secondary);\n  font-size: 28rpx;\n  margin-bottom: 15rpx;\n}\n\n.gender-group {\n  display: flex;\n  gap: 60rpx;\n}\n\n.gender-option {\n  display: flex;\n  align-items: center;\n  font-size: 28rpx;\n  color: var(--text-primary);\n}\n\n.save-btn {\n  margin-top: 30rpx;\n  background-color: var(--primary-color);\n  color: #FFFFFF;\n  font-size: 28rpx;\n  border-radius: 40rpx;\n  height: 80rpx;\n  line-height: 80rpx;\n  box-shadow: 0 4rpx 8rpx var(--shadow-color);\n}\n\n/* 问题输入区域样式 */\n.question-section {\n  position: relative;\n}\n\n.question-input {\n  width: 100%;\n  height: 200rpx;\n  padding: 20rpx;\n  box-sizing: border-box;\n  border: 2rpx solid var(--border-color);\n  border-radius: 12rpx;\n  font-size: 28rpx;\n  color: var(--text-primary);\n  background-color: var(--primary-lightest);\n}\n\n.char-count {\n  position: absolute;\n  right: 20rpx;\n  bottom: 20rpx;\n  font-size: 24rpx;\n  color: var(--text-light);\n}\n\n/* 占卜按钮样式 */\n.divine-btn {\n  width: 80%;\n  height: 90rpx;\n  line-height: 90rpx;\n"}