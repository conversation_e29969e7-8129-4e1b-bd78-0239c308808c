{"chunk": 1, "numChunks": 3, "fileHash": "+UnDZ2Oz2YO43xAn0K/rzwUpkljb+ipIs7Av95FRNE8=", "filePath": "pages/community/index.wxml", "content": "          class=\"interaction-item {{item.liked ? 'liked' : ''}}\" \n          bindtap=\"handleLike\"\n          data-post-id=\"{{item._id}}\"\n          data-index=\"{{index}}\"\n        >\n          <image class=\"icon\" src=\"{{item.liked ? '/assets/icons/liked.png' : '/assets/icons/like.png'}}\"/>\n          <text>{{item.likes || 0}}</text>\n        </view>\n        <view \n          class=\"interaction-item\" \n          bindtap=\"handleComment\"\n          data-post-id=\"{{item._id}}\"\n        >\n          <image class=\"icon\" src=\"/assets/icons/comment.png\"/>\n          <text>{{item.comments || 0}}</text>\n        </view>\n        <view \n          class=\"interaction-item\" \n          bindtap=\"handleShare\"\n          data-post-id=\"{{item._id}}\"\n          data-index=\"{{index}}\"\n        >\n          <image class=\"icon\" src=\"/assets/icons/share.png\"/>\n          <text>分享</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 加载状态 -->\n    <view class=\"loading-status\">\n      <view class=\"loading\" wx:if=\"{{loading}}\">\n        <image class=\"loading-icon\" src=\"/assets/icons/loading.gif\"/>\n        <text>加载中...</text>\n      </view>\n      <view class=\"no-more\" wx:if=\"{{!hasMore && posts.length > 0}}\">\n        没有更多内容了\n      </view>\n      <view class=\"empty\" wx:if=\"{{!loading && posts.length === 0}}\">\n        <image class=\"empty-icon\" src=\"/assets/icons/empty.png\"/>\n        <text>暂无内容</text>\n      </view>\n    </view>\n  </scroll-view>\n\n  <!-- 发帖按钮 -->\n  <view class=\"post-btn\" bindtap=\"navigateToPost\">\n"}