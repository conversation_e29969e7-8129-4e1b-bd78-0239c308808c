{"chunk": 1, "numChunks": 4, "fileHash": "0WJlPsLtY6dByumgky6y0geRpquXpBvPySRi7WFItpI=", "filePath": "pages/bazi/bazi.wxml", "content": "    <view class=\"section-title\">五行分析</view>\n    <view class=\"wuxing-analysis\">\n      <view class=\"wuxing-item\" wx:for=\"{{baziResult.wuxing}}\" wx:key=\"element\">\n        <text class=\"wuxing-label\">{{item.element}}</text>\n        <view class=\"wuxing-bar\">\n          <view class=\"wuxing-progress\" style=\"width: {{item.percentage}}%\"></view>\n        </view>\n        <text class=\"wuxing-value\">{{item.percentage}}%</text>\n      </view>\n    </view>\n\n    <!-- 十神格局 -->\n    <view class=\"section-title\">十神格局</view>\n    <view class=\"shishen-section\">\n      <view class=\"pattern-title\">{{baziResult.shishen.pattern}}</view>\n      <view class=\"pattern-desc\">{{baziResult.shishen.description}}</view>\n      <view class=\"shishen-details\">\n        <view class=\"detail-item\" wx:for=\"{{baziResult.shishen.details}}\" wx:key=\"name\">\n          <text class=\"detail-name\">{{item.name}}</text>\n          <text class=\"detail-value\">{{item.value}}</text>\n          <text class=\"detail-meaning\">{{item.meaning}}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 喜用神分析 -->\n    <view class=\"section-title\">喜用神分析</view>\n    <view class=\"xiyong-section\">\n      <view class=\"favorable-section\">\n        <view class=\"subsection-title\">喜神</view>\n        <view class=\"element-list\">\n          <view class=\"element-item\" wx:for=\"{{baziResult.xiyong.favorable}}\" wx:key=\"element\">\n            <text class=\"element-name\">{{item.element}}</text>\n"}