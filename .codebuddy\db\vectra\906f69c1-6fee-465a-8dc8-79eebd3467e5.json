{"chunk": 12, "numChunks": 19, "fileHash": "PEPeuJm140RAD7VBd85WlnsfcWUdoorqVSQj8ISByl0=", "filePath": "pages/ai-chat/ai-chat.wxss", "content": "  gap: 16rpx;\n  padding: 20rpx 24rpx;\n}\n\n.typing-animation {\n  display: flex;\n  gap: 8rpx;\n}\n\n.typing-dot {\n  width: 12rpx;\n  height: 12rpx;\n  border-radius: 50%;\n  background: #667eea;\n  animation: typing-bounce 1.4s ease-in-out infinite;\n}\n\n.typing-dot:nth-child(2) {\n  animation-delay: 0.2s;\n}\n\n.typing-dot:nth-child(3) {\n  animation-delay: 0.4s;\n}\n\n.typing-text {\n  font-size: 26rpx;\n  color: #6b7280;\n  font-style: italic;\n}\n\n.typing-glow {\n  animation: glow-pulse 1s ease-in-out infinite;\n}\n\n@keyframes typing-bounce {\n  0%, 60%, 100% {\n    transform: translateY(0);\n    opacity: 0.4;\n  }\n  30% {\n    transform: translateY(-12rpx);\n    opacity: 1;\n  }\n}\n\n.fab-ball:active {\n  transform: scale(0.95);\n  animation: none;\n}\n\n.fab-icon {\n  font-size: 50rpx;\n  color: white;\n  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\n}\n\n/* 展开后的输入区域 */\n.expanded-input-area {\n  background: white;\n  border-radius: 25rpx;\n  padding: 20rpx;\n  box-shadow: 0 8rpx 32rpx rgba(149, 117, 205, 0.2);\n  display: flex;\n  align-items: flex-end;\n  gap: 20rpx;\n"}