{"chunk": 9, "numChunks": 10, "fileHash": "zs0lx95Rie79XClG/B2xJnr9ZnTEU5TU8Cf7eq2kuOY=", "filePath": "frontend-implementation/pages/birth-info/index.js", "content": "// 出生信息页面\nPage({\n  async submitForm() {\n    if (!this.validateForm()) {\n      showToast({\n        title: '请完善信息',\n        icon: 'none'\n      })\n      return\n    }\n    \n    try {\n      this.setData({ loading: true })\n      showLoading({ title: '保存中...' })\n      \n      const apiMethod = this.data.isEdit ? updateBirthInfo : saveBirthInfo\n      const result = await apiMethod(this.data.formData)\n      \n      if (result.status === 'success') {\n        showToast({\n          title: '保存成功',\n          icon: 'success'\n        })\n        \n        // 保存成功后跳转\n        setTimeout(() => {\n          if (this.data.isEdit) {\n            wx.navigateBack()\n          } else {\n            wx.switchTab({\n              url: '/pages/index/index'\n            })\n          }\n        }, 1500)\n      } else {\n        throw new Error(result.message || '保存失败')\n      }\n    } catch (error) {\n      console.error('保存出生信息失败:', error)\n      showToast({\n        title: error.message || '保存失败',\n        icon: 'none'\n      })\n    } finally {\n      this.setData({ loading: false })\n      hideLoading()\n    }\n  },\n  /**\n   * 重置表单\n   */"}