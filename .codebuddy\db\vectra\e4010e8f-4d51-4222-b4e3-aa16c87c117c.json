{"chunk": 0, "numChunks": 1, "fileHash": "kqUL9bRVG0kV8kI8KXmjWMG1wk0L7dniRKrldcd2dhY=", "filePath": "pages/feedback/feedback.js", "content": "// pages/feedback/feedback.js\r\nPage({\r\n\r\n  /**\r\n   * 页面的初始数据\r\n   */\r\n  data: {\r\n\r\n  },\r\n\r\n  /**\r\n   * 生命周期函数--监听页面加载\r\n   */\r\n  onLoad(options) {\r\n\r\n  },\r\n\r\n  /**\r\n   * 生命周期函数--监听页面初次渲染完成\r\n   */\r\n  onReady() {\r\n\r\n  },\r\n\r\n  /**\r\n   * 生命周期函数--监听页面显示\r\n   */\r\n  onShow() {\r\n\r\n  },\r\n\r\n  /**\r\n   * 生命周期函数--监听页面隐藏\r\n   */\r\n  onHide() {\r\n\r\n  },\r\n\r\n  /**\r\n   * 生命周期函数--监听页面卸载\r\n   */\r\n  onUnload() {\r\n\r\n  },\r\n\r\n  /**\r\n   * 页面相关事件处理函数--监听用户下拉动作\r\n   */\r\n  onPullDownRefresh() {\r\n\r\n  },\r\n\r\n  /**\r\n   * 页面上拉触底事件的处理函数\r\n   */\r\n  onReachBottom() {\r\n\r\n  },\r\n\r\n  /**\r\n   * 用户点击右上角分享\r\n   */\r\n  onShareAppMessage() {\r\n\r\n  }\r\n})"}