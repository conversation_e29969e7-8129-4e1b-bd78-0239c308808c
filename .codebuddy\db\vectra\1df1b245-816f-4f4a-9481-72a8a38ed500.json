{"chunk": 0, "numChunks": 2, "fileHash": "6QyTJ5JNP12dXX0od431hqoQdy6MfszuFe76fWraxG0=", "filePath": "pages/profile/index.wxml", "content": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<view class=\"container\">\n  <!-- 用户信息卡片 -->\n  <view class=\"user-card\" bindtap=\"handleLogin\">\n    <view class=\"user-info\">\n      <image class=\"avatar\" src=\"{{userInfo.avatarUrl || '/assets/images/default-avatar.png'}}\" mode=\"aspectFill\"/>\n      <view class=\"info-right\">\n        <view class=\"nickname\">{{userInfo.nickName}}</view>\n        <view class=\"user-level\">\n          <text class=\"level-tag\">{{userInfo.level}}</text>\n          <text class=\"points\">积分: {{userInfo.points}}</text>\n        </view>\n      </view>\n    </view>\n    <view class=\"card-bottom\">\n      <view class=\"stat-item\">\n        <text class=\"stat-num\">{{statistics.orders}}</text>\n        <text class=\"stat-label\">订单</text>\n      </view>\n      <view class=\"stat-item\">\n        <text class=\"stat-num\">{{statistics.favorites}}</text>\n        <text class=\"stat-label\">收藏</text>\n      </view>\n      <view class=\"stat-item\">\n        <text class=\"stat-num\">{{statistics.points}}</text>\n        <text class=\"stat-label\">积分</text>\n      </view>\n      <view class=\"stat-item\">\n        <text class=\"stat-num\">{{statistics.coupons}}</text>\n        <text class=\"stat-label\">优惠券</text>\n      </view>\n    </view>\n  </view>\n\n  <!-- 功能列表 -->\n  <view class=\"section\">\n    <view class=\"section-title\">我的服务</view>\n    <view class=\"function-grid\">\n      <view class=\"grid-item\" \n            wx:for=\"{{functionList}}\" \n            wx:key=\"id\"\n            bindtap=\"navigateToFunction\"\n            data-url=\"{{item.url}}\">\n"}