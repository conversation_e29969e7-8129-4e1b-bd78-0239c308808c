{"chunk": 18, "numChunks": 76, "fileHash": "lPYWZE8QItgwSyEsMXHtl6y/HYBCI8BdGaV+1l8ICMA=", "filePath": "docs/md/API_INTERFACE_DOCUMENTATION.md", "content": "  \"session_ids\": [\"string\"],        // 会话ID列表 (可选，不传则导出所有)\n  \"start_date\": \"date\",             // 开始日期 (可选)\n  \"end_date\": \"date\",               // 结束日期 (可选)\n  \"format\": \"string\",               // 导出格式 (json/csv/pdf)\n  \"include_analysis\": \"boolean\"     // 是否包含分析结果\n}\n\nResponse:\n{\n  \"status\": \"success|error\",\n  \"message\": \"string\",\n  \"data\": {\n    \"export_id\": \"string\",          // 导出任务ID\n    \"download_url\": \"string\",       // 下载链接 (异步生成)\n    \"estimated_time\": \"number\",     // 预估完成时间(秒)\n    \"file_size\": \"number\"           // 预估文件大小(字节)\n  }\n}\n```\n\n#### 11. 获取导出状态\n```\nGET /api/ai-chat/export/{export_id}/status\nAuthorization: Bearer {token}\n\nResponse:\n{\n  \"status\": \"success|error\",\n  \"message\": \"string\",\n  \"data\": {\n    \"export_status\": \"string\",      // 导出状态 (pending/processing/completed/failed)\n    \"progress\": \"number\",           // 进度百分比\n    \"download_url\": \"string\",       // 下载链接 (完成后可用)\n    \"file_size\": \"number\",          // 文件大小\n    \"expires_at\": \"datetime\"        // 下载链接过期时间\n  }\n}\n```\n\n#### 12. AI问答管理接口 (管理员)\n\n##### 12.1 获取所有会话 (管理员)\n```\nGET /api/admin/ai-chat/sessions\nAuthorization: Bearer {admin_token}\nQuery Parameters:\n- page: number (页码)\n"}