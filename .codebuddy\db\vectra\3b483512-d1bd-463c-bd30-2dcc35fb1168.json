{"chunk": 14, "numChunks": 18, "fileHash": "yi4gCVr62AiqkHDrZGxGnPy2D7On+5yxIebQ7IGvQ04=", "filePath": "pages/community/community.js", "content": "// pages/community/community.js\nPage({\n  async publishPost() {\n    try {\n      // 上传图片到云存储\n      const fileIDs = await this.uploadImages(images)\n      \n      // 获取用户信息\n      const userInfo = await wx.cloud.callFunction({\n        name: 'getUserInfo'\n      }).catch(() => ({ result: { openid: 'anonymous' } }))\n      \n      // 创建帖子数据\n      const postData = {\n        title: title.trim(),\n        content: content.trim(),\n        images: fileIDs,\n        category: category.value,\n        categoryName: category.name,\n        tag: tag,\n        createTime: new Date(),\n        updateTime: new Date(),\n        likes: 0,\n        comments: 0,\n        views: 0,\n        author: {\n          openid: userInfo.result.openid,\n          nickname: '匿名用户',\n          avatar: '/assets/images/default-avatar.png'\n        }\n      }\n      \n      // 添加到云数据库\n      const db = wx.cloud.database()\n      const result = await db.collection('posts').add({\n        data: postData\n      })\n      \n      // 创建新帖子对象用于本地显示\n      const newPost = {\n        id: result._id,\n        username: postData.author.nickname,\n        avatar: postData.author.avatar,\n        title: postData.title,\n        content: postData.content,\n        images: postData.images,\n        category: postData.category,\n        categoryName: postData.categoryName,\n        createTime: postData.createTime.toLocaleString(),\n        likes: postData.likes,\n        comments: postData.comments,\n        tag: postData.tag\n      }\n      \n      // 更新本地数据\n      this.setData({\n        posts: [newPost, ...this.data.posts],\n        showPublishModal: false\n      })\n      \n      wx.hideLoading()\n      wx.showToast({\n        title: '发布成功',\n        icon: 'success'\n      })\n    } catch (error) {"}