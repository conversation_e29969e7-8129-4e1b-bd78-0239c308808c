{"chunk": 2, "numChunks": 6, "fileHash": "QLx6GhwgSxPofuK/CUpTrLuKBgVetRumHHMileilK58=", "filePath": "pages/ziwei/ziwei.wxss", "content": "/* 命理解读样式 */\n.interpretation-section {\n  margin-top: 30rpx;\n}\n\n.interpretation-item {\n  margin-bottom: 24rpx;\n  padding: 16rpx;\n  background-color: var(--primary-lightest);\n  border-radius: 12rpx;\n  border: 2rpx solid var(--border-color);\n}\n\n.item-title {\n  font-size: 28rpx;\n  color: var(--primary-color);\n  font-weight: bold;\n  margin-bottom: 12rpx;\n}\n\n.item-content {\n  font-size: 26rpx;\n  color: var(--text-secondary);\n  line-height: 1.6;\n}\n\n/* 大限流年样式 */\n.fortune-scroll {\n  margin-top: 20rpx;\n  white-space: nowrap;\n  overflow-x: auto;\n  padding: 10rpx 0;\n}\n\n.fortune-list {\n  display: inline-flex;\n  padding: 10rpx 0;\n}\n\n.fortune-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-right: 30rpx;\n  background-color: var(--primary-lightest);\n  padding: 20rpx;\n  border-radius: 12rpx;\n  min-width: 120rpx;\n  border: 2rpx solid var(--border-color);\n}\n\n.fortune-age {\n  font-size: 24rpx;\n  color: var(--text-secondary);\n  margin-bottom: 10rpx;\n}\n\n.fortune-year {\n  font-size: 28rpx;\n  color: var(--primary-color);\n  font-weight: bold;\n  margin-bottom: 10rpx;\n}\n\n.fortune-palace {\n  font-size: 24rpx;\n  color: var(--primary-dark);\n}\n\n.chart-section {\n"}