{"chunk": 6, "numChunks": 13, "fileHash": "UUsAqDc9MY1pjBTLokkGc1GjjJmTwiOLz2lDZFa/4nc=", "filePath": "pages/index/index.wxss", "content": "/* 热门测算 */\n.hot-section {\n  margin-top: 50rpx;\n}\n\n.hot-list {\n  display: flex;\n  flex-direction: column;\n  gap: 25rpx;\n}\n\n.hot-item {\n  display: flex;\n  background: white;\n  border-radius: 25rpx;\n  padding: 25rpx;\n  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.hot-item::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 100rpx;\n  height: 100rpx;\n  background: linear-gradient(135deg, rgba(149, 117, 205, 0.1), transparent);\n  border-radius: 0 0 0 100%;\n}\n\n.hot-item:active {\n  transform: translateY(2rpx);\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);\n}\n\n.hot-image {\n  width: 180rpx;\n  height: 140rpx;\n  border-radius: 20rpx;\n  margin-right: 25rpx;\n  object-fit: cover;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n}\n\n.hot-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.hot-title {\n  font-size: 32rpx;\n  color: #333;\n  font-weight: bold;\n  margin-bottom: 10rpx;\n  line-height: 1.4;\n"}