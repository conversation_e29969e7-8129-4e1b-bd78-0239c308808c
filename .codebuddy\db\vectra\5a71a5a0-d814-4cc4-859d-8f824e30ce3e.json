{"chunk": 1, "numChunks": 3, "fileHash": "15fIrrZZavpLMg/CeD22sxtSKr3cmD/mbJHXpMwsVQQ=", "filePath": "pages/hehun/index.wxml", "content": "        </picker>\n      </view>\n    </view>\n  </view>\n\n  <button class=\"analyze-btn\" bindtap=\"analyzeMarriage\" loading=\"{{loading}}\">\n    开始测算\n  </button>\n\n  <view class=\"result-section\" wx:if=\"{{showResult}}\">\n    <view class=\"result-card\">\n      <view class=\"card-title\">八字合婚</view>\n      <view class=\"bazi-comparison\">\n        <view class=\"person-bazi\">\n          <text class=\"person-title\">男方八字</text>\n          <view class=\"bazi-grid\">\n            <view class=\"grid-item\" wx:for=\"{{maleResult.pillars}}\" wx:key=\"index\">\n              <text class=\"pillar-name\">{{item.name}}</text>\n              <view class=\"pillar-content\">\n                <text>{{item.heavenlyStem}}{{item.earthlyBranch}}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        <view class=\"person-bazi\">\n          <text class=\"person-title\">女方八字</text>\n          <view class=\"bazi-grid\">\n            <view class=\"grid-item\" wx:for=\"{{femaleResult.pillars}}\" wx:key=\"index\">\n              <text class=\"pillar-name\">{{item.name}}</text>\n              <view class=\"pillar-content\">\n                <text>{{item.heavenlyStem}}{{item.earthlyBranch}}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <view class=\"result-card\">\n      <view class=\"card-title\">五行相合</view>\n      <view class=\"wuxing-analysis\">\n        <view class=\"wuxing-item\" wx:for=\"{{marriageResult.wuxing}}\" wx:key=\"element\">\n"}