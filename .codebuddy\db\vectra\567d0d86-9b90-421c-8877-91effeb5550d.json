{"chunk": 1, "numChunks": 3, "fileHash": "RttDx+iB5hfM6jraH4ey2N9z0fr1jgnCDw08WhBbAn8=", "filePath": "pages/fortune/fortune.wxss", "content": "  margin-bottom: 16rpx;\n}\n\n.info-label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #666;\n}\n\n.info-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n\n.info-actions {\n  margin-top: 20rpx;\n  display: flex;\n  justify-content: flex-end;\n}\n\n.action-button {\n  font-size: 28rpx;\n  color: #8a2be2;\n  background-color: transparent;\n  border: 1px solid #8a2be2;\n  border-radius: 30rpx;\n  padding: 10rpx 30rpx;\n  line-height: 1.5;\n}\n\n.empty-tip {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n}\n\n.tip-text {\n  font-size: 30rpx;\n  color: #999;\n  margin-bottom: 30rpx;\n}\n\n.tip-button {\n  font-size: 30rpx;\n  color: #fff;\n  background-color: #8a2be2;\n  border-radius: 40rpx;\n  padding: 16rpx 60rpx;\n}\n\n.chart-section {\n  margin: 30rpx 0;\n  background-color: #fff;\n  border-radius: 16rpx;\n  padding: 20rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n}\n\n.chart-container {\n  width: 100%;\n  height: 400rpx;\n  margin: 20rpx 0;\n}\n\n.fortune-chart {\n  width: 100%;\n  height: 100%;\n}\n\n.month-nodes {\n  display: flex;\n"}