{"chunk": 0, "numChunks": 4, "fileHash": "0WJlPsLtY6dByumgky6y0geRpquXpBvPySRi7WFItpI=", "filePath": "pages/bazi/bazi.wxml", "content": "<view class=\"container\">\n  <!-- 出生信息悬浮窗 -->\n  <birth-info top=\"120\" />\n\n  <!-- 加载状态 -->\n  <view class=\"loading-container\" wx:if=\"{{loading}}\">\n    <view class=\"loading-spinner\"></view>\n    <text class=\"loading-text\">正在计算八字...</text>\n  </view>\n\n  <!-- 错误提示 -->\n  <view class=\"error-container\" wx:if=\"{{error}}\">\n    <text class=\"error-text\">{{error}}</text>\n    <button class=\"retry-button\" bindtap=\"recalculate\">重新计算</button>\n  </view>\n\n  <!-- 八字结果 -->\n  <block wx:if=\"{{baziResult && !loading && !error}}\">\n    <!-- 八字命盘 -->\n    <view class=\"section-title\">八字命盘</view>\n    <view class=\"bazi-chart\">\n      <view class=\"pillar-row\">\n        <view class=\"pillar\">\n          <text class=\"pillar-label\">年柱</text>\n          <text class=\"pillar-content\">{{baziResult.yearPillar}}</text>\n        </view>\n        <view class=\"pillar\">\n          <text class=\"pillar-label\">月柱</text>\n          <text class=\"pillar-content\">{{baziResult.monthPillar}}</text>\n        </view>\n        <view class=\"pillar\">\n          <text class=\"pillar-label\">日柱</text>\n          <text class=\"pillar-content\">{{baziResult.dayPillar}}</text>\n        </view>\n        <view class=\"pillar\">\n          <text class=\"pillar-label\">时柱</text>\n          <text class=\"pillar-content\">{{baziResult.hourPillar}}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 五行分析 -->\n"}