{"chunk": 6, "numChunks": 11, "fileHash": "6cvegKFbV+qpODqHzHmmaj3L2MYUXg3Sv6AOEWns8A8=", "filePath": "frontend-implementation/mixins/basePage.js", "content": "// 页面基础混入\nexport const basePageMixin = {\n  /**\n   * 显示加载状态\n   * @param {string} title 加载提示文字\n   */\n  showLoading(title = '加载中...') {\n    this.setData({ loading: true })\n    showLoading({ title })\n  },\n  /**\n   * 隐藏加载状态\n   */\n  hideLoading() {\n    this.setData({ loading: false })\n    hideLoading()\n  },\n  /**\n   * 显示错误信息\n   * @param {string} message 错误信息\n   * @param {string} icon 图标类型\n   */\n  showError(message, icon = 'none') {\n    this.setData({ error: message })\n    showToast({\n      title: message,\n      icon\n    })\n  },\n  /**\n   * 清除错误状态\n   */\n  clearError() {\n    this.setData({ error: null })\n  },\n  /**\n   * 安全的API调用\n   * @param {function} apiCall API调用函数\n   * @param {object} options 配置选项\n   */"}