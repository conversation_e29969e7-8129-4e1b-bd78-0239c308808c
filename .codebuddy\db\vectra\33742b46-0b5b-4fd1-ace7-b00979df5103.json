{"chunk": 9, "numChunks": 13, "fileHash": "UUsAqDc9MY1pjBTLokkGc1GjjJmTwiOLz2lDZFa/4nc=", "filePath": "pages/index/index.wxss", "content": "  left: 0;\n  font-size: 20rpx;\n}\n\n.article-views {\n  background: linear-gradient(135deg, #f5f0ff 0%, #ece7f5 100%);\n  padding: 6rpx 16rpx;\n  border-radius: 20rpx;\n  font-weight: 500;\n}\n\n/* 节气提醒和签到区域 - 美化 */\n.header-section {\n  margin-bottom: 30rpx;\n}\n\n.solar-terms-notice {\n  display: flex;\n  align-items: center;\n  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);\n  padding: 20rpx 30rpx;\n  border-radius: 20rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.1);\n}\n\n.notice-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-right: 15rpx;\n}\n\n.notice-text {\n  font-size: 28rpx;\n  color: #e65100;\n  font-weight: 500;\n}\n\n.sign-in-section {\n  margin-bottom: 32rpx;\n}\n\n.sign-in-card {\n  display: flex;\n  align-items: center;\n  background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);\n  padding: 30rpx;\n  border-radius: 25rpx;\n  box-shadow: 0 6rpx 20rpx rgba(76, 175, 80, 0.15);\n}\n\n.sign-in-icon {\n  font-size: 60rpx;\n  margin-right: 25rpx;\n"}