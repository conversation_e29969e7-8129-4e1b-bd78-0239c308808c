{"chunk": 0, "numChunks": 3, "fileHash": "gBZ6rOH7FJiiaL1bL4AO7Rv3z4sWp7hh/CVaiX5DB/k=", "filePath": "pages/name/index.wxml", "content": "<view class=\"container\">\n  <view class=\"header\">\n    <text class=\"title\">姓名测算</text>\n    <text class=\"subtitle\">解析姓名中的天机玄机</text>\n  </view>\n\n  <view class=\"form-section\">\n    <view class=\"form-item\">\n      <text class=\"label\">姓氏</text>\n      <input type=\"text\" class=\"input\" placeholder=\"请输入姓氏\" value=\"{{surname}}\" bindinput=\"onSurnameInput\"/>\n    </view>\n\n    <view class=\"form-item\">\n      <text class=\"label\">名字</text>\n      <input type=\"text\" class=\"input\" placeholder=\"请输入名字\" value=\"{{givenName}}\" bindinput=\"onGivenNameInput\"/>\n    </view>\n\n    <view class=\"form-item\">\n      <text class=\"label\">性别</text>\n      <radio-group class=\"radio-group\" bindchange=\"onGenderChange\">\n        <label class=\"radio\">\n          <radio value=\"male\" checked=\"{{gender === 'male'}}\"/>男\n        </label>\n        <label class=\"radio\">\n          <radio value=\"female\" checked=\"{{gender === 'female'}}\"/>女\n        </label>\n      </radio-group>\n    </view>\n\n    <view class=\"form-item\">\n      <text class=\"label\">出生日期</text>\n      <picker mode=\"date\" value=\"{{birthDate}}\" bindchange=\"onBirthDateChange\">\n        <view class=\"picker\">{{birthDate || '请选择出生日期'}}</view>\n      </picker>\n    </view>\n  </view>\n\n  <button class=\"analyze-btn\" bindtap=\"analyzeName\" loading=\"{{loading}}\">\n    开始测算\n  </button>\n\n  <view class=\"result-section\" wx:if=\"{{showResult}}\">\n    <view class=\"result-card\">\n"}