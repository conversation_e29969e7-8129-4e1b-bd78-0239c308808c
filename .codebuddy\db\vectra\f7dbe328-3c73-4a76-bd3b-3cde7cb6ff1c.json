{"chunk": 0, "numChunks": 5, "fileHash": "yLT5DZWgzA1pMyoKDto/o26wX3DjjCYshSeMtgr/cn4=", "filePath": "pages/xingming/index.wxml", "content": "<view class=\"container\">\n  <!-- 顶部banner -->\n  <view class=\"banner\">\n    <image class=\"banner-bg\" src=\"/assets/images/xingming/banner-bg.jpg\" mode=\"aspectFill\"/>\n    <view class=\"banner-title\">\n      <text class=\"main-title\">姓名测算</text>\n      <text class=\"sub-title\">一字千金，名字决定命运</text>\n    </view>\n  </view>\n\n  <!-- 信息输入表单 -->\n  <view class=\"form-section\">\n    <view class=\"form-title\">基本信息</view>\n    \n    <!-- 姓氏输入 -->\n    <view class=\"form-item\">\n      <text class=\"label\">姓氏</text>\n      <input class=\"input\" type=\"text\" placeholder=\"请输入姓氏\" model:value=\"{{surname}}\" bindinput=\"onSurnameInput\"/>\n    </view>\n\n    <!-- 名字输入 -->\n    <view class=\"form-item\">\n      <text class=\"label\">名字</text>\n      <input class=\"input\" type=\"text\" placeholder=\"请输入名字\" model:value=\"{{givenName}}\" bindinput=\"onGivenNameInput\"/>\n    </view>\n\n    <!-- 性别选择 -->\n    <view class=\"form-item\">\n      <text class=\"label\">性别</text>\n      <view class=\"gender-picker\">\n        <view \n          class=\"gender-option {{gender === 'male' ? 'active' : ''}}\" \n          bindtap=\"onGenderSelect\" \n          data-gender=\"male\"\n        >男</view>\n        <view \n          class=\"gender-option {{gender === 'female' ? 'active' : ''}}\" \n          bindtap=\"onGenderSelect\" \n          data-gender=\"female\"\n        >女</view>\n      </view>\n    </view>\n\n    <!-- 出生日期 -->\n"}