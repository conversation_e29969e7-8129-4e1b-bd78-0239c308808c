{"chunk": 9, "numChunks": 38, "fileHash": "DpyinnhZJO6jrs0/VY0BXwrNjORlEQ1BeRywBJybhxQ=", "filePath": "pages/ai-chat/ai-chat.js", "content": "const app = getApp()\nPage({\n  showHelp() {\n    const helpMessage = {\n      id: Date.now(),\n      type: 'ai',\n      content: '🤖 AI助手使用帮助：\\n\\n1. 点击快捷功能卡片快速开始分析\\n2. 直接输入问题进行自由对话\\n3. 完善出生信息获得更精准结果\\n4. 支持八字、易经、风水等多种分析\\n\\n有任何问题都可以直接问我哦！',\n      timestamp: this.formatTime(new Date()),\n      showTime: true\n    }\n\n    this.setData({\n      messages: [...this.data.messages, helpMessage],\n      showQuickActions: false\n    })\n\n    this.scrollToBottom()\n  },\n  /**\n   * 消息反馈\n   */\n  onMessageFeedback(e) {\n    const { type } = e.currentTarget.dataset\n    const feedbackText = type === 'like' ? '感谢您的反馈！' : '我们会继续改进'\n\n    wx.showToast({\n      title: feedbackText,\n      icon: type === 'like' ? 'success' : 'none'\n    })\n  },\n  /**\n   * 复制消息\n   */\n  copyMessage(e) {\n    const { content } = e.currentTarget.dataset\n    wx.setClipboardData({\n      data: content,\n      success: () => {\n        wx.showToast({\n          title: '已复制',\n          icon: 'success'\n        })\n      }\n    })\n  },\n  /**\n   * 分享消息\n   */\n  shareMessage(e) {\n    const content = e.currentTarget.dataset.content\n\n    wx.showShareMenu({\n      withShareTicket: true,\n      menus: ['shareAppMessage', 'shareTimeline']\n   "}