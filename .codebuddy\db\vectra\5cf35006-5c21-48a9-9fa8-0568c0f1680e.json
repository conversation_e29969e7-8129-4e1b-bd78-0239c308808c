{"chunk": 11, "numChunks": 18, "fileHash": "yi4gCVr62AiqkHDrZGxGnPy2D7On+5yxIebQ7IGvQ04=", "filePath": "pages/community/community.js", "content": "// pages/community/community.js\nPage({\n  deleteImage(e) {\n    const index = e.currentTarget.dataset.index\n    const images = [...this.data.newPost.images]\n    images.splice(index, 1)\n    this.setData({\n      'newPost.images': images\n    })\n  },\n  /**\n   * 上传图片到云存储\n   */\n  async uploadImages(images) {\n    if (!images || images.length === 0) return []\n    \n    const uploadTasks = images.map(filePath => {\n      return wx.cloud.uploadFile({\n        cloudPath: `posts/${Date.now()}-${Math.floor(Math.random() * 1000)}.${filePath.match(/\\.(\\w+)$/)[1]}`,\n        filePath\n      })\n    })\n    \n    try {\n      const results = await Promise.all(uploadTasks)\n      return results.map(res => res.fileID)\n    } catch (error) {\n      console.error('上传图片失败:', error)\n      wx.showToast({\n        title: '图片上传失败',\n        icon: 'none'\n      })\n      return []\n    }\n  },\n  /**\n   * 发布帖子到云数据库\n   */"}