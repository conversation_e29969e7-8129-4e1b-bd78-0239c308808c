{"chunk": 13, "numChunks": 14, "fileHash": "3g+f4UKfyPQOeFOGstuRr+TvNVx6NqSI5ZfRnXdVunU=", "filePath": "pages/profile/profile.js", "content": "// pages/profile/profile.js\nPage({\n  getUserProfile(e) {\n    wx.getUserProfile({\n      desc: '用于完善会员资料',\n      success: (res) => {\n        this.setData({\n          userInfo: res.userInfo,\n          hasUserInfo: true\n        })\n        this.updateUserInfo(res.userInfo)\n      }\n    })\n  },\n  async updateUserInfo(userInfo) {\n    try {\n      const db = wx.cloud.database()\n      await db.collection('users').where({\n        _openid: app.globalData.openid\n      }).update({\n        data: {\n          ...userInfo,\n          updateTime: new Date()\n        }\n      })\n    } catch (error) {\n      console.error('更新用户信息失败:', error)\n    }\n  },\n  switchTab(e) {\n    const { tab } = e.currentTarget.dataset\n    this.setData({\n      activeTab: tab\n    })\n  },\n  toggleTheme() {\n    const newTheme = this.data.settings.theme === 'light' ? 'dark' : 'light'\n    this.setData({\n      'settings.theme': newTheme\n    })\n    wx.setStorageSync('theme', newTheme)\n  },\n  toggleNotification() {\n    this.setData({\n      'settings.notification': !this.data.settings.notification\n    })\n    wx.setStorageSync('notification', this.data.settings.notification)\n  },\n  // 查看收藏的帖子\n  viewFavorite(e) {\n    const { id } = e.currentTarget.dataset\n    wx.navigateTo({\n      url: `/pages/post-detail/post-detail?id=${id}`\n    })\n  },\n  // 查看测算记录详情\n  viewRecord(e) {\n    const { id, type } = e.currentTarget.dataset\n    wx.navigateTo({\n      url: `/pages/${type}/detail?id=${id}`\n    })\n  },\n  // 提交反馈"}