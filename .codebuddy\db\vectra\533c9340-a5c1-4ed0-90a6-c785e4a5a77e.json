{"chunk": 1, "numChunks": 5, "fileHash": "tA9ixVr6Arfzki0CzquWOFo8c2xDC4cgMIk+7vjNaBk=", "filePath": "pages/ziwei/ziwei.wxml", "content": "    <!-- 十四主星组合 -->\n    <view class=\"star-combinations-section\" wx:if=\"{{ziweiResult}}\">\n      <view class=\"section-title\">十四主星组合</view>\n      <view class=\"combinations-list\">\n        <view class=\"combination-item\" wx:for=\"{{ziweiResult.starCombinations}}\" wx:key=\"type\">\n          <view class=\"combination-title\">{{item.type}}</view>\n          <view class=\"combination-stars\">星耀：{{item.stars.join('、')}}</view>\n          <view class=\"combination-meaning\">{{item.meaning}}</view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 人生轨迹预测 -->\n    <view class=\"life-trajectory-section\" wx:if=\"{{ziweiResult}}\">\n      <view class=\"section-title\">人生轨迹预测</view>\n      <view class=\"trajectory-content\">\n        <view class=\"trajectory-item\">\n          <view class=\"trajectory-title\">事业预测</view>\n          <view class=\"trajectory-text\">{{ziweiResult.lifeTrajectory.career}}</view>\n          <view class=\"trajectory-detail\">\n            <view class=\"detail-item\" wx:for=\"{{ziweiResult.lifeTrajectory.careerDetails}}\" wx:key=\"index\">\n              <view class=\"detail-title\">{{item.title}}</view>\n              <view class=\"detail-content\">{{item.content}}</view>\n            </view>\n          </view>\n        </view>\n        <view class=\"trajectory-item\">\n          <view class=\"trajectory-title\">婚姻预测</view>\n          <view class=\"trajectory-text\">{{ziweiResult.lifeTrajectory.marriage}}</view>\n          <view class=\"trajectory-detail\">\n"}