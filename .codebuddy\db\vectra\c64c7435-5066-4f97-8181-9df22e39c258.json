{"chunk": 2, "numChunks": 4, "fileHash": "JxYmunO+MuN/nw3IhbaSBWIwBZxgEQi9Pk/YphlGNzQ=", "filePath": "pages/post-detail/post-detail.wxml", "content": "          </view>\n      </view>\n      </view>\n    </view>\n    \n    <!-- 评论区域 -->\n    <view class=\"comment-section\">\n      <view class=\"section-title\">\n        评论 ({{comments.length}})\n  </view>\n  \n      <!-- 评论列表 -->\n    <view class=\"comment-list\">\n      <view class=\"comment-item\" wx:for=\"{{comments}}\" wx:key=\"id\">\n          <image class=\"comment-avatar\" src=\"{{item.avatar || '/assets/images/default-avatar.png'}}\" />\n        <view class=\"comment-content\">\n            <view class=\"comment-header\">\n              <text class=\"comment-author\">{{item.author}}</text>\n              <text class=\"comment-time\">{{item.time}}</text>\n            </view>\n            <text class=\"comment-text\">{{item.content}}</text>\n            <view class=\"comment-actions\">\n              <text class=\"action-btn\" bindtap=\"likeComment\" data-id=\"{{item.id}}\">\n                {{item.liked ? '❤️' : '🤍'}} {{item.likeCount || 0}}\n              </text>\n              <text class=\"action-btn\" bindtap=\"replyComment\" data-id=\"{{item.id}}\">回复</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 加载更多 -->\n      <view class=\"load-more\" wx:if=\"{{hasMoreComments}}\" bindtap=\"loadMoreComments\">\n        <text>{{loadingComments ? '加载中...' : '查看更多评论'}}</text>\n    </view>\n    </view>\n  </scroll-view>\n  \n  <!-- 底部评论输入 -->\n  <view class=\"comment-input-bar\">\n    <input \n      class=\"comment-input\"\n      placeholder=\"写下你的想法...\"\n      value=\"{{commentInput}}\"\n      bindinput=\"onCommentInput\"\n      bindconfirm=\"submitComment\"\n    />\n"}