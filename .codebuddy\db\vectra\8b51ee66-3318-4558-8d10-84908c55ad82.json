{"chunk": 8, "numChunks": 13, "fileHash": "RdYUonhiW47SLv0/ZKSqV2JJEqqe4g6tc2PZPdqonRM=", "filePath": "pages/post-detail/post-detail.js", "content": "const app = getApp()\nPage({\n  async likePost() {\n    const post = this.data.post\n    const isLiked = this.data.isLiked\n    \n    try {\n      // 获取用户信息\n      const userInfo = await wx.cloud.callFunction({\n        name: 'getUserInfo'\n      }).catch(() => ({ result: { openid: 'anonymous' } }))\n      \n      const db = wx.cloud.database()\n      \n      if (isLiked) {\n        // 取消点赞\n        await db.collection('likes')\n          .where({\n            postId: post.id,\n            userId: userInfo.result.openid\n          })\n          .remove()\n        \n        await db.collection('posts').doc(post.id).update({\n          data: {\n            likes: db.command.inc(-1)\n          }\n        })\n        \n        post.likes -= 1\n        this.setData({ \n          post,\n          isLiked: false\n        })\n        \n        wx.showToast({\n          title: '已取消点赞',\n          icon: 'success'\n        })\n      } else {\n        // 添加点赞\n        await db.collection('likes').add({\n          data: {\n            postId: post.id,\n            userId: userInfo.result.openid,\n            createTime: new Date()\n          }\n        })\n        \n        await db.collection('posts').doc(post.id).update({\n          data: {\n            likes: db.command.inc(1)\n          }\n        })\n        \n        post.likes += 1\n        this.setData({ \n          post,\n          isLiked: true\n        })\n        \n        wx.showToast({\n          title: '点赞成功',\n          icon: 'success'\n        })\n      }\n    } catch (error) {\n      console.error('点赞操作失败:', error)\n      wx.showToast({\n        title: '操作失败',\n        icon: 'none'\n      })\n    }\n  },\n  /**\n   * 输入评论\n   */\n  onCommentInput(e) {\n    this.setData({\n      commentText"}