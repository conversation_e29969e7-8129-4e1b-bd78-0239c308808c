{"chunk": 12, "numChunks": 15, "fileHash": "O/nJs1XsxYT9PDo4XFlMEnsiqZRG/Zu4Jl5KoBF5Ci8=", "filePath": "pages/index/index.wxml", "content": "              <text class=\"sign-desc\">获取积分奖励</text>\n            </view>\n            <view class=\"sign-btn\">签到</view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 顶部轮播图 -->\n        <block wx:if=\"{{banners && banners.length > 0}}\">\n          <swiper class=\"banner\" indicator-dots=\"{{true}}\" autoplay=\"{{true}}\" interval=\"3000\" duration=\"500\" circular=\"{{true}}\" indicator-color=\"rgba(255, 255, 255, 0.6)\" indicator-active-color=\"#ffffff\">\n            <swiper-item wx:for=\"{{banners}}\" wx:key=\"id\" bindtap=\"onBannerTap\" data-url=\"{{item.url}}\">\n              <view class=\"banner-item\">\n          <image src=\"{{item.imageUrl}}\" mode=\"aspectFill\" class=\"banner-image\" />\n                <view class=\"banner-title\">{{item.title}}</view>\n              </view>\n        </swiper-item>\n      </swiper>\n        </block>\n      <view class=\"error-tip\" wx:else>轮播图加载失败</view>\n\n      <!-- 最近使用功能 -->\n      <view class=\"section\" wx:if=\"{{recentUsed.length > 0}}\">\n        <view class=\"section-title\">\n          <text>最近使用</text>\n          <text class=\"section-more\" bindtap=\"clearRecentUsed\">清空</text>\n        </view>\n        <scroll-view class=\"recent-scroll\" scroll-x>\n          <view class=\"recent-item\" wx:for=\"{{recentUsed}}\" wx:key=\"type\" bindtap=\"onRecentItemTap\" data-type=\"{{item.type}}\">\n            <image src=\"{{item.icon}}\" class=\"recent-icon\" mode=\"aspectFit\" />\n            <text class=\"recent-text\">{{item.name}}</text>\n          </view>\n        </scroll-view>\n      </view>\n\n"}