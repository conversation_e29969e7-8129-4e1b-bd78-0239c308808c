{"chunk": 3, "numChunks": 11, "fileHash": "i8jYYz/gAcuprKem1BBbiBaSat5ZFs+bjwiR2/ZQErE=", "filePath": "pages/birth-info/birth-info.js", "content": "const globalState = require('../../utils/global-state')\nPage({\n  data: {\n    name: '',\n    gender: '男',\n    zodiacList: ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'],\n    zodiacIndex: null,\n    numberList: Array.from({length: 62}, (_, i) => i + 1),\n    numberIndex: null,\n    dateTime: [],\n    dateTimeArray: [],\n    selectedDateTime: '',\n    years: [],\n    months: [],\n    days: [],\n    hours: [],\n    minutes: [],\n    canSubmit: false\n  },\n  onLoad() {\n    // 初始化日期时间选择器数据\n    this.initDateTimePickerData();\n\n    // 从全局状态管理器读取出生信息\n    this.loadExistingBirthInfo();\n  },\n  // 加载已有的出生信息\n  loadExistingBirthInfo() {\n    const birthInfo = globalState.getState('birthInfo');\n    if (birthInfo) {\n      this.setData({\n        name: birthInfo.name || '',\n        gender: birthInfo.gender || '男',\n        zodiacIndex: birthInfo.zodiacIndex || null,\n        numberIndex: birthInfo.numberIndex || null,\n        selectedDateTime: birthInfo.selectedDateTime || ''\n      });\n\n      // 如果有已保存的日期时间，设置选择器的值\n      if (birthInfo.dateTimeArray) {\n        this.setData({\n          dateTimeArray: birthInfo.dateTimeArray\n        });\n      }\n\n      // 检查是否可以提交\n      this.checkCanSubmit();\n    }\n  },"}