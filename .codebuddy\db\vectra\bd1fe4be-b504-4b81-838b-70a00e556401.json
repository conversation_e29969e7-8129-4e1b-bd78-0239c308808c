{"chunk": 3, "numChunks": 6, "fileHash": "CxWKtdq0I3NaiNwiPJTyYstBQKlC237Mnt9cmUNUZpE=", "filePath": "pages/fengshui/index.js", "content": "const app = getApp()\nPage({\n  data: {\n    // 房屋类型选项\n    houseTypes: ['住宅', '办公', '商铺', '厂房'],\n    houseTypeIndex: null,\n\n    // 方位选项\n    directions: ['坐北朝南', '坐南朝北', '坐东朝西', '坐西朝东', \n                '坐东北朝西南', '坐西南朝东北', '坐东南朝西北', '坐西北朝东南'],\n    directionIndex: null,\n\n    // 户型选项\n    roomNums: [1, 2, 3, 4, 5, 6],\n    hallNums: [1, 2, 3],\n    bathNums: [1, 2, 3, 4],\n    roomNumIndex: -1,\n    hallNumIndex: -1,\n    bathNumIndex: -1,\n\n    // 基本信息\n    buildYear: '',\n    area: '',\n    loading: false,\n    showResult: false,\n    baguaResult: [],\n    positionResult: {\n      auspicious: [],\n      inauspicious: []\n    },\n    layoutAdvice: [],\n    solutions: [],\n    canSubmit: false,\n    price: 28\n  },\n  onLoad() {\n    // 初始化图表\n    this.baguaChart = new BaguaChart()\n    this.wuxingChart = new WuxingChart()\n  },\n  // 房屋类型选择\n  onHouseTypeChange(e) {\n    this.setData({\n      houseTypeIndex: e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 朝向选择"}