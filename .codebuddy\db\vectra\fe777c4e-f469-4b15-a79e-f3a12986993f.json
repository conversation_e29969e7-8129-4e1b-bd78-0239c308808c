{"chunk": 7, "numChunks": 8, "fileHash": "2gwHiceF78ljJhRnHSk/QhbjXumu/H9/2egtjP1psSk=", "filePath": "pages/fortune/fortune.js", "content": "const app = getApp()\nPage({\n  drawCurve(ctx, width, height) {\n    const padding = 40\n    const chartWidth = width - padding * 2\n    const chartHeight = height - padding * 2\n    // 绘制坐标轴\n    ctx.beginPath()\n    ctx.strokeStyle = '#ddd'\n    ctx.moveTo(padding, padding)\n    ctx.lineTo(padding, height - padding)\n    ctx.lineTo(width - padding, height - padding)\n    ctx.stroke()\n    // 绘制曲线\n    ctx.beginPath()\n    ctx.strokeStyle = '#8a2be2'\n    ctx.lineWidth = 2\n    // 生成曲线点\n    const points = []\n    for (let i = 0; i <= 12; i++) {\n      const x = padding + (chartWidth * i / 12)\n      const y = height - padding - (Math.sin(i / 2) * 0.5 + 0.5) * chartHeight\n      points.push({ x, y })\n    }\n    // 绘制曲线\n    ctx.moveTo(points[0].x, points[0].y)\n    for (let i = 1; i < points.length; i++) {\n      const xc = (points[i].x + points[i - 1].x) / 2\n      const yc = (points[i].y + points[i - 1].y) / 2\n      ctx.quadraticCurveTo(points[i - 1].x, points[i - 1].y, xc, yc)\n    }\n    ctx.stroke()\n    // 绘制节点"}