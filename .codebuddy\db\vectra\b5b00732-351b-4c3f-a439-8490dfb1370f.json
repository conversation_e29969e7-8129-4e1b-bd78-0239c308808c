{"chunk": 4, "numChunks": 6, "fileHash": "m/bcrIpornnqwv0iP6r9/jZF/qprhOPA5RFcfEHrukg=", "filePath": "pages/profile/profile.wxss", "content": "  padding: 0 8rpx;\n  background: #ff6b6b;\n  color: white;\n  font-size: 22rpx;\n  border-radius: 18rpx;\n  text-align: center;\n}\n\n/* 功能列表 */\n.menu-list {\n  margin: 30rpx;\n}\n\n.menu-group {\n  background: white;\n  border-radius: 25rpx;\n  margin-bottom: 25rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n.group-title {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #999;\n  padding: 25rpx 30rpx 15rpx;\n  background: #fafafa;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.menu-item {\n  display: flex;\n  align-items: center;\n  padding: 30rpx;\n  position: relative;\n  transition: all 0.3s ease;\n}\n\n.menu-item:not(:last-child) {\n  border-bottom: 1rpx solid #f5f5f5;\n}\n\n.menu-item:active {\n  background: #f8f5ff;\n}\n\n.menu-icon {\n  font-size: 40rpx;\n  margin-right: 25rpx;\n  width: 60rpx;\n  text-align: center;\n}\n\n.menu-text {\n  flex: 1;\n  font-size: 32rpx;\n  color: #333;\n}\n\n.menu-value {\n  font-size: 28rpx;\n  color: #999;\n  margin-right: 15rpx;\n}\n\n.menu-badge {\n  min-width: 36rpx;\n  height: 36rpx;\n  line-height: 36rpx;\n"}