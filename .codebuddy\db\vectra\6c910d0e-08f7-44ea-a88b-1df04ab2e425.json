{"chunk": 3, "numChunks": 38, "fileHash": "DpyinnhZJO6jrs0/VY0BXwrNjORlEQ1BeRywBJybhxQ=", "filePath": "pages/ai-chat/ai-chat.js", "content": "const app = getApp()\nPage({\n  data: {\n    messages: [],\n    inputValue: '',\n    isTyping: false,\n    currentTypingMessage: '',\n    scrollTop: 0,\n    isLoggedIn: false,\n    userInfo: null,\n    birthInfo: null,\n    quickActions: [\n      {\n        id: 'bazi',\n        title: '八字分析',\n        icon: '🔮',\n        query: '请帮我做一个八字分析',\n        desc: '分析您的八字命理',\n        requiresBirthInfo: true\n      },\n      {\n        id: 'yijing',\n        title: '易经卦象',\n        icon: '☯️',\n        query: '请帮我解读一下易经卦象',\n        desc: '易经占卜指导',\n        requiresBirthInfo: false\n      },\n      {\n        id: 'fengshui',\n        title: '风水分析',\n        icon: '🏠',\n        query: '请帮我分析一下房屋风水',\n        desc: '居家风水布局',\n        requiresBirthInfo: false\n      },\n      {\n        id: 'wuxing',\n        title: '五行分析',\n        icon: '🌿',\n        query: '请帮我做一个五行分析',\n        desc: '五行属性解读',\n        requiresBirthInfo: true\n      }\n    ],\n    showQuickActions: true,\n    messageHistory: [],\n    isInputExpanded: false,\n    fabIcon: '💬',\n    quickReplies: []\n  },\n  onLoad() {\n    this.initStateListeners()\n    this.initializeChat()\n  },"}