{"chunk": 1, "numChunks": 3, "fileHash": "r0aOo34YwMB6Jqlyx1ur0PKXSDJ+/cbp6WOdiOy5hjY=", "filePath": "pages/profile/index.wxss", "content": "  margin-bottom: 6rpx;\n}\n\n.stat-label {\n  font-size: 24rpx;\n  opacity: 0.9;\n}\n\n/* 功能区域 */\n.section {\n  background-color: #fff;\n  border-radius: 12rpx;\n  margin: 20rpx;\n  padding: 30rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 30rpx;\n}\n\n/* 功能网格 */\n.function-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 30rpx;\n}\n\n.grid-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20rpx;\n}\n\n.grid-icon {\n  width: 60rpx;\n  height: 60rpx;\n  margin-bottom: 10rpx;\n}\n\n.grid-text {\n  font-size: 26rpx;\n  color: #333;\n}\n\n/* 设置列表 */\n.setting-list {\n  background-color: #fff;\n}\n\n.setting-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 30rpx 0;\n  border-bottom: 2rpx solid #f5f5f5;\n}\n\n.setting-item:last-child {\n  border-bottom: none;\n}\n\n.setting-left {\n  display: flex;\n  align-items: center;\n}\n\n.setting-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-right: 20rpx;\n}\n\n.setting-name {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.arrow-icon {\n  width: 32rpx;\n  height: 32rpx;\n  opacity: 0.3;\n}\n\n/* 退出登录 */\n.logout-section {\n"}