{"chunk": 0, "numChunks": 6, "fileHash": "Hr0NLKfsiy0m55e08Mw/WBiaaXrb6byHBEKkofIFTxk=", "filePath": "pages/fengshui/index.wxss", "content": "/* pages/fengshui/index.wxss */\n.container {\n  padding: 30rpx;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 40rpx;\n}\n\n.title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.subtitle {\n  font-size: 28rpx;\n  color: #666;\n}\n\n.form-section {\n  background: #fff;\n  padding: 30rpx;\n  border-radius: 16rpx;\n  margin-bottom: 30rpx;\n}\n\n.form-item {\n  margin-bottom: 30rpx;\n}\n\n.form-item:last-child {\n  margin-bottom: 0;\n}\n\n.label {\n  display: block;\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 16rpx;\n}\n\n.picker {\n  padding: 20rpx;\n  background: #f8f8f8;\n  border-radius: 8rpx;\n  font-size: 28rpx;\n  color: #333;\n}\n\n.input {\n  padding: 20rpx;\n  background: #f8f8f8;\n  border-radius: 8rpx;\n  font-size: 28rpx;\n  color: #333;\n}\n\n.analyze-btn {\n  width: 100%;\n  height: 88rpx;\n  line-height: 88rpx;\n  background: #4a5568;\n  color: #fff;\n  font-size: 32rpx;\n  border-radius: 44rpx;\n  margin: 40rpx 0;\n}\n\n.result-section {\n  margin-top: 40rpx;\n}\n\n.result-card {\n  background: #fff;\n"}