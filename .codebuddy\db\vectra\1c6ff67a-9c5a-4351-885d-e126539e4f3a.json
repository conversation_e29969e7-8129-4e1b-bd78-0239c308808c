{"chunk": 0, "numChunks": 3, "fileHash": "7tlNFY9QuuTsIOMeR/sMN3VB67YYk3Cgo2yYtVL/J9U=", "filePath": "pages/wuxing/wuxing.wxss", "content": "/* wuxing.wxss */\n.container {\n  padding: 20rpx;\n  background-color: #f5f5f5;\n}\n\n/* 输入区域样式 */\n.input-section {\n  background-color: var(--card-background);\n  padding: 30rpx;\n  border-radius: 16rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 12rpx var(--shadow-color);\n  border: 2rpx solid var(--border-color);\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n  text-align: center;\n}\n\n.input-group {\n  margin-bottom: 20rpx;\n}\n\n.picker {\n  padding: 20rpx;\n  background-color: var(--primary-lightest);\n  border-radius: 12rpx;\n  color: var(--text-primary);\n  border: 2rpx solid var(--border-color);\n}\n\n.radio {\n  margin-right: 30rpx;\n  color: var(--text-primary);\n}\n\n.calculate-btn {\n  background-color: var(--primary-color);\n  color: #FFFFFF;\n  font-size: 32rpx;\n  margin-top: 20rpx;\n  box-shadow: 0 4rpx 8rpx var(--shadow-color);\n}\n\n/* 结果区域样式 */\n.result-section {\n  margin-top: 20rpx;\n  background-color: #fff;\n  border-radius: 10rpx;\n  padding: 20rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n}\n\n/* 五行分布图样式 */\n.wuxing-chart {\n  margin-bottom: 30rpx;\n}\n\n.chart-title {\n"}