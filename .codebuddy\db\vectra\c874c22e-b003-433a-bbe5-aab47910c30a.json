{"chunk": 21, "numChunks": 38, "fileHash": "DpyinnhZJO6jrs0/VY0BXwrNjORlEQ1BeRywBJybhxQ=", "filePath": "pages/ai-chat/ai-chat.js", "content": "const app = getApp()\nPage({\n  },\n  /**\n   * 添加消息到列表\n   */\n  addMessage(message) {\n    const messages = [...this.data.messages, {\n      ...message,\n      id: Date.now() + Math.random()\n    }]\n    \n    this.setData({ messages }, () => {\n      this.scrollToBottom()\n      this.saveMessageHistory()\n    })\n  },\n  /**\n   * 模拟AI回复\n   * @param {string} userInput 用户输入\n   */"}