{"chunk": 6, "numChunks": 11, "fileHash": "i8jYYz/gAcuprKem1BBbiBaSat5ZFs+bjwiR2/ZQErE=", "filePath": "pages/birth-info/birth-info.js", "content": "const globalState = require('../../utils/global-state')\nPage({\n  onNumberChange(e) {\n    this.setData({\n      numberIndex: parseInt(e.detail.value)\n    });\n    this.checkCanSubmit();\n  },\n  // 检查是否可以提交\n  checkCanSubmit() {\n    const { name, selectedDateTime } = this.data;\n    const canSubmit = name.trim() && selectedDateTime;\n    this.setData({ canSubmit });\n  },"}