{"chunk": 6, "numChunks": 12, "fileHash": "HkVDVZ9giUC0NxXwGhnv0R1SvQ+r07w6mr3mR/NNt+s=", "filePath": "subpages/divination/bazi/bazi.js", "content": "Page({\n  calculateBazi() {\n    const result = {\n      name: this.data.name,\n      gender: this.data.gender,\n      birthDate: this.data.birthDate,\n      birthTime: this.data.birthTime,\n      location: this.data.location,\n      tianGan: [\n        { label: '年', value: randomTianGan() },\n        { label: '月', value: randomTianGan() },\n        { label: '日', value: randomTianGan() },\n        { label: '时', value: randomTianGan() }\n      ],\n      diZhi: [\n        { label: '年', value: randomDi<PERSON>hi() },\n        { label: '月', value: randomDi<PERSON>hi() },\n        { label: '日', value: randomDiZhi() },\n        { label: '时', value: randomDi<PERSON>hi() }\n      ],\n      wuxing: [\n        { name: '金', count: Math.floor(Math.random() * 4) + 1, percentage: 0 },\n        { name: '木', count: Math.floor(Math.random() * 4) + 1, percentage: 0 },\n        { name: '水', count: Math.floor(Math.random() * 4) + 1, percentage: 0 },\n        { name: '火', count: Math.floor(Math.random() * 4) + 1, percentage: 0 },\n        { name: '土', count: Math.floor(Math.random() * 4) + 1, percentage: 0 }\n      ],\n      wuxingDesc: '',\n      personality: '',\n      career: '',\n      wealth: '',\n      love: '',\n      health: '',\n      fortune: []\n    }\n    // 计算五行百分比\n    const totalWuxing = result.wuxing.reduce((sum, item) => sum"}