{"chunk": 4, "numChunks": 5, "fileHash": "7Z35lr1az9AduoWPTD2mMH++jiP/MGPr1PHcEIC4MG4=", "filePath": "pages/customer-service/index.js", "content": "const app = getApp()\nPage({\n  async sendMessage(content, type = 'text') {\n    if (!content.trim() && type === 'text') return\n\n    // 添加用户消息\n    const userMsg = {\n      type: 'user',\n      content,\n      contentType: type,\n      time: new Date().getTime()\n    }\n\n    this.setData({\n      messages: [...this.data.messages, userMsg],\n      inputContent: '',\n      showEmoji: false,\n      showMore: false,\n      isTyping: true\n    })\n\n    // 滚动到底部\n    this.scrollToBottom()\n\n    try {\n      // 调用后端API获取回复\n      const res = await wx.cloud.callFunction({\n        name: 'getServiceReply',\n        data: { content }\n      })\n\n      // 添加客服回复\n      const serviceMsg = {\n        type: 'service',\n        content: res.result.reply,\n        time: new Date().getTime()\n      }\n\n      this.setData({\n        messages: [...this.data.messages, serviceMsg],\n        isTyping: false\n      })\n\n      // 滚动到底部\n      this.scrollToBottom()\n    } catch (err) {\n      console.error('获取回复失败:', err)\n      this.setData({ isTyping: false })\n    }\n  },\n  // 输入内容变化\n  handleInput(e) {\n    this.setData({\n      inputContent: e.detail.value\n    })\n  },\n  // 发送文本消息\n  handleSend() {\n    this.sendMessage(this.data.inputContent)\n  },\n  // 选择图片\n  handleChooseImage() {\n    wx.chooseImage({\n      count: 1,\n      success: (res) => {\n        const tempFilePath = res.tempFilePaths[0]\n        // 上传图片\n        this.uploadImage(tempFilePath)\n      }\n    })\n  },\n  // 上传图片"}