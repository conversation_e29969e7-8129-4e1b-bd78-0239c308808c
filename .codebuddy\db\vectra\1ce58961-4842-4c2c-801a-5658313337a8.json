{"chunk": 0, "numChunks": 2, "fileHash": "arNTiKbFgy8gGAUWdxTdZQBzLqVScajvT55u/SgC0O4=", "filePath": "frontend-implementation/api/aiChat.js", "content": "// AI智能聊天相关API\nimport { request } from '../utils/request'\n/**\n * 发送AI消息\n * @param {object} data - 消息数据\n */\nexport const sendAIMessage = (data) => {\n  return request({\n    url: '/api/ai-chat/message',\n    method: 'POST',\n    data\n  })\n}\n/**\n * 获取聊天历史\n * @param {object} params - 查询参数\n */\nexport const getChatHistory = (params = {}) => {\n  return request({\n    url: '/api/ai-chat/history',\n    method: 'GET',\n    params\n  })\n}\n/**\n * 删除聊天会话\n * @param {string} sessionId - 会话ID\n */\nexport const deleteChatSession = (sessionId) => {\n  return request({\n    url: `/api/ai-chat/session/${sessionId}`,\n    method: 'DELETE'\n  })\n}\n/**\n * 获取会话列表\n * @param {object} params - 查询参数\n */\nexport const getChatSessions = (params = {}) => {\n  return request({\n    url: '/api/ai-chat/sessions',\n    method: 'GET',\n    params\n  })\n}\n/**\n * 创建新会话\n * @param {object} data - 会话数据\n */\nexport const createChatSession = (data = {}) => {\n  return request({\n    url: '/api/ai-chat/session',\n    method: 'POST',\n    data\n  })\n}\n/**\n * 更新会话标题\n * @param {string} sessionId - 会话ID\n * @param {string} title - 新标题\n */\nexport const updateSessionTitle = (sessionId, title) => {\n  return request({\n    url: `/api/ai-chat/session/${sessionId}`,\n    method: 'PUT',\n    data: { title }\n  })\n}\n/**\n * 获取快捷操作列表\n"}