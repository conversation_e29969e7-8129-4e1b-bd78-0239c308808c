{"chunk": 6, "numChunks": 9, "fileHash": "43tyNMTA9IQs33epLq7vkl5MUCocqW9TjhFCqLTPBOQ=", "filePath": "frontend-implementation/store/index.js", "content": "// 全局状态管理\nclass Store {\n  /**\n   * 用户登录\n   * @param {object} userInfo 用户信息\n   * @param {string} token 访问令牌\n   * @param {string} refreshToken 刷新令牌\n   */\n  login(userInfo, token, refreshToken) {\n    // 保存到缓存\n    setStorageSync('userInfo', userInfo)\n    setStorageSync('token', token)\n    setStorageSync('refreshToken', refreshToken)\n    \n    // 更新状态\n    this.setState('user', {\n      isLogin: true,\n      userInfo,\n      token,\n      refreshToken\n    })\n  }\n  /**\n   * 用户登出\n   */\n  logout() {\n    // 清除缓存\n    wx.removeStorageSync('userInfo')\n    wx.removeStorageSync('token')\n    wx.removeStorageSync('refreshToken')\n    \n    // 重置状态\n    this.setState('user', {\n      isLogin: false,\n      userInfo: null,\n      token: null,\n      refreshToken: null\n    })\n    \n    // 清除其他用户相关数据\n    this.setState('birthInfo', {\n      hasInfo: false,\n      data: null\n    })\n    \n    this.setState('chat', {\n      currentSessionId: null,\n      sessions: [],\n      quickActions: []\n    })\n  }\n  /**\n   * 更新用户信息\n   * @param {object} userInfo 用户信息\n   */\n  updateUserInfo(userInfo) {\n    const currentUser = this.getState('user')\n    const updatedUser = {\n      ...currentUser,\n      userInfo: {\n        ...currentUser.userInfo,\n        ...userInfo\n      }\n    }\n    \n    setStorageSync('userInfo', updatedUser.userInfo)\n    this.setState('user', updatedUser)\n  }\n  /**\n   * 设置出生信息\n   * @param {object} birthInfo 出生信息\n   */"}