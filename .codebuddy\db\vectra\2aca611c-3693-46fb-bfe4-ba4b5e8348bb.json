{"chunk": 5, "numChunks": 7, "fileHash": "nGMyzD5kIPI75b8ATQIO6k03owW27BytPf8s1C7ATWc=", "filePath": "pages/bazi/index.wxss", "content": ".jixiong-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 20rpx;\n}\n\n.jixiong-item {\n  background: #F8F9FD;\n  border-radius: 12rpx;\n  overflow: hidden;\n}\n\n.item-header {\n  padding: 16rpx;\n  text-align: center;\n  font-size: 28rpx;\n  color: #fff;\n}\n\n.item-header.ji {\n  background: #4CAF50;\n}\n\n.item-header.xiong {\n  background: #FF5252;\n}\n\n.item-content {\n  padding: 16rpx;\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.5;\n}\n\n/* 建议指导 */\n.advice-list {\n  margin-top: 16rpx;\n}\n\n.advice-item {\n  margin-bottom: 24rpx;\n}\n\n.advice-title {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: bold;\n  margin-bottom: 12rpx;\n}\n\n.advice-content {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.5;\n}\n\n/* 分享按钮 */\n.share-section {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  padding: 20rpx 30rpx;\n  background: #fff;\n  box-shadow: 0 -4rpx 16rpx rgba(0,0,0,0.05);\n}\n\n.share-btn {\n  width: 100%;\n  height: 88rpx;\n  background: #F8F9FD;\n  border-radius: 44rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12rpx;\n}\n\n"}