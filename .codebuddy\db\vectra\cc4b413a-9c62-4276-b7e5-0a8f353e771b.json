{"chunk": 4, "numChunks": 9, "fileHash": "43tyNMTA9IQs33epLq7vkl5MUCocqW9TjhFCqLTPBOQ=", "filePath": "frontend-implementation/store/index.js", "content": "// 全局状态管理\nclass Store {\n  watchNetworkStatus() {\n    wx.onNetworkStatusChange((res) => {\n      this.setState('app.networkStatus', res.isConnected ? 'online' : 'offline')\n    })\n  }\n  /**\n   * 获取系统信息\n   */\n  getSystemInfo() {\n    wx.getSystemInfo({\n      success: (res) => {\n        this.setState('app.systemInfo', res)\n      }\n    })\n  }\n  /**\n   * 设置状态\n   * @param {string} path 状态路径\n   * @param {any} value 状态值\n   */\n  setState(path, value) {\n    const keys = path.split('.')\n    let current = this.state\n    \n    // 导航到目标对象\n    for (let i = 0; i < keys.length - 1; i++) {\n      if (!current[keys[i]]) {\n        current[keys[i]] = {}\n      }\n      current = current[keys[i]]\n    }\n    \n    // 设置值\n    const lastKey = keys[keys.length - 1]\n    const oldValue = current[lastKey]\n    current[lastKey] = value\n    \n    // 通知监听器\n    this.notifyListeners(path, value, oldValue)\n  }\n  /**\n   * 获取状态\n   * @param {string} path 状态路径\n   */\n  getState(path) {\n    const keys = path.split('.')\n    let current = this.state\n    \n    for (const key of keys) {\n      if (current && typeof current === 'object' && key in current) {\n        current = current[key]\n      } else {\n        return undefined\n      }\n    }\n    \n    return current\n  }\n  /**\n   * 订阅状态变化\n   * @param {string} path 状态路径\n   * @param {function} callback 回调函数\n   */"}