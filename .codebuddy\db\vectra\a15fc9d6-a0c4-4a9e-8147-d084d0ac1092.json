{"chunk": 1, "numChunks": 3, "fileHash": "iCmaZ6jMvTDmQ81NioPpkFyGvlIKjeIU1BQv/3IrIRc=", "filePath": "pages/customer-service/index.wxml", "content": "        <image \n          class=\"content-image\" \n          src=\"{{item.content}}\" \n          mode=\"widthFix\" \n          wx:if=\"{{item.contentType === 'image'}}\"\n          bindtap=\"previewImage\"\n          data-url=\"{{item.content}}\"\n        />\n        <text class=\"time\">{{item.time}}</text>\n      </view>\n    </view>\n\n    <!-- 正在输入提示 -->\n    <view class=\"typing-indicator\" wx:if=\"{{isTyping}}\">\n      <view class=\"typing-dot\"></view>\n      <view class=\"typing-dot\"></view>\n      <view class=\"typing-dot\"></view>\n    </view>\n  </scroll-view>\n\n  <!-- 输入区域 -->\n  <view class=\"input-section\">\n    <!-- 输入框工具栏 -->\n    <view class=\"toolbar\">\n      <view class=\"tool-item\" bindtap=\"toggleEmoji\">\n        <image class=\"tool-icon\" src=\"/assets/icons/emoji.png\"/>\n      </view>\n      <view class=\"tool-item\" bindtap=\"handleChooseImage\">\n        <image class=\"tool-icon\" src=\"/assets/icons/image.png\"/>\n      </view>\n      <view class=\"tool-item\" bindtap=\"toggleMore\">\n        <image class=\"tool-icon\" src=\"/assets/icons/more.png\"/>\n      </view>\n    </view>\n\n    <!-- 输入框 -->\n    <view class=\"input-box\">\n      <input \n        class=\"input\"\n        value=\"{{inputContent}}\"\n        placeholder=\"请输入消息...\"\n        bindinput=\"handleInput\"\n        confirm-type=\"send\"\n        bindconfirm=\"handleSend\"\n      />\n      <view class=\"send-btn {{inputContent ? 'active' : ''}}\" bindtap=\"handleSend\">\n        发送\n      </view>\n    </view>\n\n    <!-- 表情面板 -->\n    <view class=\"emoji-panel\" wx:if=\"{{showEmoji}}\">\n      <!-- 表情列表 -->\n"}