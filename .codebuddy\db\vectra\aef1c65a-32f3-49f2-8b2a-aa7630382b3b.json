{"chunk": 2, "numChunks": 3, "fileHash": "h8VEoH7XgUTaD2cCx23MEZncAvUaCL5srdlMHNmVy4c=", "filePath": "pages/name/index.wxss", "content": ".element-name {\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 10rpx;\n  display: block;\n}\n\n.name-details {\n  margin-top: 20rpx;\n}\n\n.detail-item {\n  margin-bottom: 20rpx;\n}\n\n.aspect-name {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.aspect-desc {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n.fortune-analysis {\n  margin-top: 20rpx;\n}\n\n.fortune-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.stars {\n  color: #f0b90b;\n  font-size: 28rpx;\n}\n\n.name-suggestions {\n  margin-top: 20rpx;\n}\n\n.suggestion-item {\n  background: #f8f8f8;\n  padding: 20rpx;\n  border-radius: 8rpx;\n  margin-bottom: 20rpx;\n}\n\n.suggested-name {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.suggestion-desc {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.6;\n} \n"}