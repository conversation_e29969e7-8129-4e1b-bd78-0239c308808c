{"chunk": 6, "numChunks": 8, "fileHash": "+ydGTiHYiZCNR2Gpo16W6yPFn4Qk+H6hNDjgo1ILGOU=", "filePath": "pages/xingming/index.js", "content": "const app = getApp()\nPage({\n  async onAnalyze() {\n    if (!this.data.canSubmit) return\n    wx.showLoading({ title: '正在分析...' })\n    try {\n      // 计算八字\n      const baziCalculator = new BaziCalculator({\n        name: this.data.surname + this.data.givenName,\n        gender: this.data.gender,\n        birthDate: this.data.birthDate,\n        birthTime: this.data.birthTime\n      })\n\n      const baziResult = await baziCalculator.calculate()\n\n      // 姓名测算\n      const calculator = new XingmingCalculator({\n        surname: this.data.surname,\n        givenName: this.data.givenName,\n        gender: this.data.gender,\n        bazi: baziResult\n      })\n\n      const result = await calculator.calculate()\n\n      // 更新结果\n      this.setData({\n        showResult: true,\n        totalScore: result.score,\n        scoreDesc: result.description,\n        wugeList: result.wugeList,\n        sancaiAnalysis: result.sancaiAnalysis,\n        baziInfo: {\n          year: `${baziResult.year.gan}${baziResult.year.zhi}`,\n          month: `${baziResult.month.gan}${baziResult.month.zhi}`,\n          day: `${baziResult.day.gan}${baziResult.day.zhi}`,\n          time: `${baziResult.time.gan}${baziResult.time.zhi}`\n        },\n        baziAnalysis: result.baziAnalysis,\n        jixiongList: result.jixiongList,\n        adviceList: result.adviceList\n      })\n\n      // 绘制三才图表\n      this.drawSancaiChart(result.sancaiData)\n\n      wx.hideLoading()\n    } catch (error) {\n      console.error('分析失败:', error)\n      wx.hideLoading()\n      wx.showToast({\n        title: '分析失败，请"}