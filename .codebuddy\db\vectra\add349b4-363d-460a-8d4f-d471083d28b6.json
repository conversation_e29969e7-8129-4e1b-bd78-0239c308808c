{"chunk": 5, "numChunks": 6, "fileHash": "CxWKtdq0I3NaiNwiPJTyYstBQKlC237Mnt9cmUNUZpE=", "filePath": "pages/fengshui/index.js", "content": "const app = getApp()\nPage({\n  async analyzeFengshui() {\n    if (!this.validateInput()) {\n      return;\n    }\n\n    this.setData({ loading: true });\n\n    try {\n      const calculator = new FengshuiCalculator();\n      const result = await calculator.calculate({\n        direction: this.data.directions[this.data.directionIndex],\n        houseType: this.data.houseTypes[this.data.houseTypeIndex],\n        buildYear: this.data.buildYear,\n        area: parseFloat(this.data.area)\n      });\n\n      this.setData({\n        loading: false,\n        showResult: true,\n        baguaResult: result.bagua.map(item => ({\n          direction: item.direction,\n          name: item.name,\n          element: item.element,\n          active: item.isActive\n        })),\n        positionResult: {\n          auspicious: result.positions.auspicious.map(item => ({\n            direction: item.direction,\n            usage: item.recommendedUsage,\n            description: item.description\n          })),\n          inauspicious: result.positions.inauspicious.map(item => ({\n            direction: item.direction,\n            usage: item.avoidance,\n            description: item.description\n          }))\n        },\n        layoutAdvice: result.layout.map(item => ({\n          room: item.room,\n          suggestion: item.suggestion\n        })),\n        solutions: result.solutions.map(item => ({\n          type: item.type,\n          description: item.description\n        }))\n      });\n\n      // 绘制图表\n      this.drawCharts(result.baguaData, result.wuxingData)\n    } catch (error) {\n      console.error('风水分析失败:', error);\n      wx.showToast({\n        title: '分析失败，请重试',\n        icon: 'none'\n      });\n      this.setData({ loading: false });\n    }\n  },"}