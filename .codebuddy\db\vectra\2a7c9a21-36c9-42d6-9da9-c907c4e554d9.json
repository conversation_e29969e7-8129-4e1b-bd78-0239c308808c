{"chunk": 2, "numChunks": 8, "fileHash": "y2IL59YXs36741pVvCZKEDopMVE9nury7qdEmYs0pAw=", "filePath": "pages/marriage/marriage.wxss", "content": "  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);\n}\n\n.input-placeholder {\n  color: #bbb;\n}\n\n.input-icon {\n  position: absolute;\n  right: 20rpx;\n  font-size: 32rpx;\n  opacity: 0.5;\n}\n\n/* 选择器 */\n.picker-wrapper {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n  border: 2rpx solid #e8e8e8;\n  border-radius: 16rpx;\n  padding: 0 20rpx;\n  background: #fafafa;\n  transition: all 0.3s ease;\n}\n\n.picker-wrapper:active {\n  border-color: #667eea;\n  background: white;\n}\n\n.picker-text {\n  font-size: 32rpx;\n  flex: 1;\n}\n\n.picker-text.placeholder {\n  color: #bbb;\n}\n\n.picker-text.selected {\n  color: #333;\n}\n\n.picker-icon {\n  font-size: 32rpx;\n  opacity: 0.5;\n}\n\n/* 提示卡片 */\n.tips-card {\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border-radius: 20rpx;\n  padding: 24rpx;\n  margin-bottom: 32rpx;\n  border: 1rpx solid #f39c12;\n}\n\n.tips-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.tips-icon {\n  font-size: 32rpx;\n  margin-right: 12rpx;\n}\n\n.tips-title {\n  font-size: 32rpx;\n  font-weight: 600;\n"}