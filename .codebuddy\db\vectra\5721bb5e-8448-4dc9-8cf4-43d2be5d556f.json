{"chunk": 7, "numChunks": 8, "fileHash": "pWWTyJooIxy6mFbGJzYtyes3pCjDoqcgXSX9h6jKjqM=", "filePath": "utils/bazi/calculator.js", "content": "// 天干\nclass BaziCalculator {\n  calculateFortune(bazi, wuxing, gender) {\n    // 这里可以根据八字和五行分布计算各方面运势\n    // 这是一个简化版本的示例\n    const fortune = {\n      career: 0,\n      wealth: 0,\n      love: 0,\n      health: 0\n    };\n\n    // 计算事业运\n    const dayMaster = STEM_WUXING[bazi.dayStem];\n    const dayMasterStrength = wuxing[dayMaster];\n    fortune.career = Math.min(100, dayMasterStrength);\n\n    // 计算财运\n    const wealthElement = this.getWealthElement(dayMaster);\n    fortune.wealth = Math.min(100, wuxing[wealthElement]);\n\n    // 计算感情运\n    const loveElement = this.getLoveElement(dayMaster, gender);\n    fortune.love = Math.min(100, wuxing[loveElement]);\n\n    // 计算健康运\n    fortune.health = Math.min(100, (dayMasterStrength + wuxing[this.getHealthElement(dayMaster)]) / 2);\n\n    return fortune;\n  }\n  getWuxingName(element) {\n    const names = {\n      metal: '金',\n      wood: '木',\n      water: '水',\n      fire: '火',\n      earth: '土'\n    };\n    return names[element];\n  }\n  getWealthElement(dayMaster) {\n    const wealthMap = {\n      metal: 'water',\n      water: 'wood',\n      wood: 'fire',\n      fire: 'earth',\n      earth: 'metal'\n    };\n    return wealthMap[dayMaster];\n  }"}