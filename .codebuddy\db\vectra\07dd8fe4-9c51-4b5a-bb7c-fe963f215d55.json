{"chunk": 1, "numChunks": 6, "fileHash": "NZjhfJONEn33fTaL5WUzkjtuxbwRJIB1E7qQ/zXp6ww=", "filePath": "pages/profile/profile.wxml", "content": "        <view class=\"stat-divider\"></view>\n        <view class=\"stat-item\" bindtap=\"goToCollection\">\n          <text class=\"stat-value\">{{userStats.collections || 0}}</text>\n          <text class=\"stat-label\">收藏</text>\n        </view>\n        <view class=\"stat-divider\"></view>\n        <view class=\"stat-item\" bindtap=\"goToHistory\">\n          <text class=\"stat-value\">{{userStats.history || 0}}</text>\n          <text class=\"stat-label\">测算</text>\n        </view>\n      </view>\n    </view>\n  </view>\n\n  <!-- 签到卡片 -->\n  <view class=\"sign-card\" wx:if=\"{{!todaySignIn}}\" bindtap=\"handleSignIn\">\n    <view class=\"sign-icon\">✨</view>\n    <view class=\"sign-content\">\n      <text class=\"sign-title\">每日签到</text>\n      <text class=\"sign-desc\">已连续签到 {{signInDays}} 天</text>\n    </view>\n    <view class=\"sign-action\">签到领积分</view>\n  </view>\n\n  <!-- 常用功能 -->\n  <view class=\"quick-section\">\n    <view class=\"section-header\">\n      <text class=\"section-title\">常用功能</text>\n    </view>\n    <view class=\"quick-grid\">\n      <view class=\"quick-item\" bindtap=\"navigateTo\" data-url=\"/pages/my-posts/my-posts\">\n        <view class=\"quick-icon\" style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\">\n          📝\n        </view>\n        <text class=\"quick-text\">我的帖子</text>\n        <text class=\"quick-badge\" wx:if=\"{{userStats.posts}}\">{{userStats.posts}}</text>\n"}