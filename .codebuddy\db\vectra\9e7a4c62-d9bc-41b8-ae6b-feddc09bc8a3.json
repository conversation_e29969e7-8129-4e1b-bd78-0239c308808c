{"chunk": 0, "numChunks": 2, "fileHash": "Y5uVnPvuXHJ3jfWUrNeZJdYK9Vssmtd0fSEsDRLsVy4=", "filePath": "subpages/admin/announcement/announcement.wxss", "content": "/* subpages/admin/announcement/announcement.wxss */\n/* 此文件是为了解决编译器路径错误而创建的 */\n\n.announcement-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n  padding: 20rpx;\n}\n\n.announcement-header {\n  background: white;\n  border-radius: 16rpx;\n  padding: 40rpx;\n  text-align: center;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n  margin-bottom: 30rpx;\n}\n\n.announcement-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #8a2be2;\n}\n\n.announcement-content {\n  background: white;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n  margin-bottom: 30rpx;\n}\n\n.announcement-list {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.announcement-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 30rpx;\n  background: #f8f9fa;\n  border-radius: 12rpx;\n  transition: all 0.3s ease;\n}\n\n.announcement-item:active {\n  background: #e9ecef;\n  transform: scale(0.98);\n}\n\n.announcement-info {\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.announcement-subject {\n  font-size: 28rpx;\n  color: #333;\n"}