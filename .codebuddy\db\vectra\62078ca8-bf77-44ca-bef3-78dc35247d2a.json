{"chunk": 2, "numChunks": 6, "fileHash": "TRjhIujXrZdfOAnJdtSzbShY2tPK4Kr6sOiSYsXo4vg=", "filePath": "pages/daily-fortune/daily-fortune.wxml", "content": "            <view class=\"color-dot\" style=\"background: {{dailyFortune.luckyColor.hex}}\"></view>\n            <text>{{dailyFortune.luckyColor.name}}</text>\n          </view>\n        </view>\n        <view class=\"lucky-item\">\n          <view class=\"lucky-label\">幸运方位</view>\n          <view class=\"lucky-value direction\">{{dailyFortune.luckyDirection}}</view>\n        </view>\n        <view class=\"lucky-item\">\n          <view class=\"lucky-label\">幸运时间</view>\n          <view class=\"lucky-value time\">{{dailyFortune.luckyTime}}</view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 时辰运势 -->\n    <view class=\"hourly-fortune\">\n      <view class=\"section-title\">\n        <text>时辰运势</text>\n        <text class=\"section-desc\">点击查看详情</text>\n      </view>\n      <scroll-view class=\"hour-scroll\" scroll-x>\n        <view class=\"hour-list\">\n          <view \n            class=\"hour-item {{item.isNow ? 'current' : ''}} {{item.level}}\"\n            wx:for=\"{{dailyFortune.hourlyFortunes}}\" \n            wx:key=\"hour\"\n            bindtap=\"showHourDetail\"\n            data-hour=\"{{item}}\"\n          >\n            <view class=\"hour-time\">{{item.time}}</view>\n            <view class=\"hour-name\">{{item.name}}</view>\n            <view class=\"hour-level\">{{item.levelText}}</view>\n          </view>\n        </view>\n      </scroll-view>\n    </view>\n\n    <!-- 今日宜忌 -->\n    <view class=\"daily-taboos\">\n      <view class=\"section-title\">今日宜忌</view>\n      <view class=\"taboo-content\">\n"}