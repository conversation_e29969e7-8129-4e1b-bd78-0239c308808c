{"chunk": 5, "numChunks": 7, "fileHash": "YmBtAc19/HOz7eUBhWxGsON9KNrEblPnxcHiHsXvXv8=", "filePath": "pages/community/community.wxss", "content": "  background-color: #8a2be2;\n}\n\n.image-uploader {\n  margin-bottom: 12px;\n}\n\n.image-list {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 8px;\n}\n\n.image-item {\n  position: relative;\n  width: 100%;\n  padding-bottom: 100%;\n}\n\n.image-item image {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  border-radius: 8px;\n}\n\n.delete-btn {\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  width: 20px;\n  height: 20px;\n  background: rgba(0,0,0,0.5);\n  border-radius: 50%;\n  color: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n}\n\n.upload-btn {\n  width: 100%;\n  padding-bottom: 100%;\n  position: relative;\n  border: 1px dashed #ddd;\n  border-radius: 8px;\n}\n\n.upload-btn image {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 24px;\n  height: 24px;\n}\n\n.category-selector {\n  margin-bottom: 12px;\n}\n\n.picker {\n  width: 100%;\n  height: 40px;\n  border: 1px solid #f0f0f0;\n  border-radius: 8px;\n  padding: 0 12px;\n  display: flex;\n  align-items: center;\n  font-size: 14px;\n  color: #333;\n}\n\n.modal-footer {\n  display: flex;\n"}