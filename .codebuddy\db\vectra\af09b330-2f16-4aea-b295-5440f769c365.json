{"chunk": 8, "numChunks": 18, "fileHash": "/DUygIsVyFRM4gmsdi8uiMJN8RFlyElKDKTgRRrPFCI=", "filePath": "utils/hehun/calculator.js", "content": "// 五行生克关系\nclass HehunCalculator {\n  calculateGanHe(male<PERSON>an, femaleGan) {\n    let score = 0\n    // 天干五合\n    const ganHe = {\n      '甲己': 30, '乙庚': 30, '丙辛': 30, '丁壬': 30, '戊癸': 30\n    }\n\n    for (let i = 0; i < 4; i++) {\n      const pair = maleGan[i] + female<PERSON>an[i]\n      const reversePair = femaleGan[i] + maleGan[i]\n      if (ganHe[pair] || ganHe[reversePair]) {\n        score += (i === 2) ? 40 : 20 // 日柱合为40分，其他为20分\n      }\n    }\n\n    return score\n  }\n  calculateZhiHe(male<PERSON>hi, female<PERSON>hi) {\n    let score = 0\n    // 地支六合\n    const zhiHe = {\n      '子丑': 20, '寅亥': 20, '卯戌': 20, '辰酉': 20, '巳申': 20, '午未': 20\n    }\n\n    for (let i = 0; i < 4; i++) {\n      const pair = male<PERSON>hi[i] + female<PERSON>hi[i]\n      const reversePair = female<PERSON>hi[i] + male<PERSON>hi[i]\n      if (zhiHe[pair] || zhiHe[reversePair]) {\n        score += (i === 2) ? 30 : 15 // 日支合为30分，其他为15分\n      }\n    }\n\n    return score\n "}