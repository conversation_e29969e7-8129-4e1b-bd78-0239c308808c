{"chunk": 3, "numChunks": 6, "fileHash": "NZjhfJONEn33fTaL5WUzkjtuxbwRJIB1E7qQ/zXp6ww=", "filePath": "pages/profile/profile.wxml", "content": "        <text class=\"menu-value\">{{birthInfo.name || '未设置'}}</text>\n        <text class=\"menu-arrow\">›</text>\n      </view>\n      <view class=\"menu-item\" bindtap=\"editPersonalInfo\">\n        <text class=\"menu-icon\">👤</text>\n          <text class=\"menu-text\">个人资料</text>\n        <text class=\"menu-arrow\">›</text>\n        </view>\n      <view class=\"menu-item\" bindtap=\"goToMessages\">\n        <text class=\"menu-icon\">💬</text>\n        <text class=\"menu-text\">消息中心</text>\n        <text class=\"menu-badge\" wx:if=\"{{unreadCount}}\">{{unreadCount}}</text>\n        <text class=\"menu-arrow\">›</text>\n    </view>\n  </view>\n\n    <!-- 积分等级 -->\n    <view class=\"menu-group\">\n      <view class=\"group-title\">积分等级</view>\n      <view class=\"menu-item\" bindtap=\"goToPoints\">\n        <text class=\"menu-icon\">💎</text>\n          <text class=\"menu-text\">积分中心</text>\n        <text class=\"menu-value\">{{userPoints}}分</text>\n        <text class=\"menu-arrow\">›</text>\n      </view>\n      <view class=\"menu-item\" bindtap=\"goToLevel\">\n        <text class=\"menu-icon\">🏆</text>\n        <text class=\"menu-text\">等级特权</text>\n        <text class=\"menu-value\">{{userLevel.name}}</text>\n        <text class=\"menu-arrow\">›</text>\n      </view>\n      <view class=\"menu-item\" bindtap=\"navigateTo\" data-url=\"/pages/recharge/recharge\">\n        <text class=\"menu-icon\">💳</text>\n"}