{"chunk": 17, "numChunks": 18, "fileHash": "/DUygIsVyFRM4gmsdi8uiMJN8RFlyElKDKTgRRrPFCI=", "filePath": "utils/hehun/calculator.js", "content": "// 五行生克关系\nclass HehunCalculator {\n  getRelationshipAdvice(score) {\n    if (score >= 80) {\n      return '双方八字相合，婚后感情和睦。建议保持真诚沟通，互相理解，让感情升温。'\n    } else if (score >= 60) {\n      return '婚后生活平稳，需要双方互相包容。建议多创造共处时光，增进感情交流。'\n    } else {\n      return '婚后可能面临挑战，建议增进沟通理解，学会换位思考，共同面对困难。'\n    }\n  }\n  getCareerAdvice() {\n    const maleWuxing = this.getMainWuxing(this.maleBazi)\n    const femaleWuxing = this.getMainWuxing(this.femaleBazi)\n\n    if (this.isHelpful(maleWuxing, femaleWuxing)) {\n      return '双方在事业上能够互相扶持，建议可以考虑合作发展，共同创业。'\n    } else if (this.isConflict(maleWuxing, femaleWuxing)) {\n      return '建议在事业上保持适当独立，各自发展所长，互不干扰。'\n    } else {\n      return '可以在事业上互相支持，但要注意维护各自的发展空间。'\n    }\n  }"}