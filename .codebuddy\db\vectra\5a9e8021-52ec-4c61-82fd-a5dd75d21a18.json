{"chunk": 9, "numChunks": 11, "fileHash": "6cvegKFbV+qpODqHzHmmaj3L2MYUXg3Sv6AOEWns8A8=", "filePath": "frontend-implementation/mixins/basePage.js", "content": "// 页面基础混入\nexport const basePageMixin = {\n  /**\n   * 格式化日期\n   * @param {Date|string} date 日期\n   * @param {string} format 格式\n   */\n  formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {\n    const d = new Date(date)\n    \n    const year = d.getFullYear()\n    const month = String(d.getMonth() + 1).padStart(2, '0')\n    const day = String(d.getDate()).padStart(2, '0')\n    const hour = String(d.getHours()).padStart(2, '0')\n    const minute = String(d.getMinutes()).padStart(2, '0')\n    const second = String(d.getSeconds()).padStart(2, '0')\n    \n    return format\n      .replace('YYYY', year)\n      .replace('MM', month)\n      .replace('DD', day)\n      .replace('HH', hour)\n      .replace('mm', minute)\n      .replace('ss', second)\n  },\n  /**\n   * 相对时间格式化\n   * @param {Date|string} date 日期\n   */"}