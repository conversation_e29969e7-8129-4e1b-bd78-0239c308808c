{"chunk": 4, "numChunks": 6, "fileHash": "VToXv7ATbmi0am+Amh5z0qVhRJK7IjhEVx3t5LgFVgM=", "filePath": "pages/marriage/index.wxml", "content": "      </view>\n      <scroll-view class=\"analysis-tabs\" scroll-x=\"true\">\n        <view\n          class=\"analysis-tab {{currentType === item.id ? 'active' : ''}}\"\n          wx:for=\"{{analysisTypes}}\"\n          wx:key=\"id\"\n          bindtap=\"switchAnalysisType\"\n          data-type=\"{{item.id}}\"\n        >\n          {{item.name}}\n        </view>\n      </scroll-view>\n    </view>\n\n    <!-- 总体评分卡片 -->\n    <view class=\"info-card\" wx:if=\"{{currentType === 'overall'}}\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">总体匹配度</text>\n        <text class=\"card-icon\">💯</text>\n      </view>\n      <view class=\"score-section\">\n        <view class=\"score-ring\">\n          <view class=\"score-value\">{{analysis.overall.score}}分</view>\n          <view class=\"score-desc\">{{analysis.overall.level}}</view>\n        </view>\n        <view class=\"score-detail\">{{analysis.overall.description}}</view>\n      </view>\n    </view>\n\n    <!-- 各项分析卡片 -->\n    <view class=\"info-card\" wx:if=\"{{currentType !== 'overall'}}\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">{{analysisTypes[currentTypeIndex].name}}分析</text>\n        <text class=\"card-icon\">📋</text>\n      </view>\n\n      <view class=\"analysis-detail\">\n        <view class=\"detail-header\">\n          <view class=\"detail-score\">匹配度：{{analysis[currentType].score}}分</view>\n          <view class=\"detail-level\">{{analysis[currentType].level}}</view>\n        </view>\n\n        <view class=\"detail-content\">\n"}