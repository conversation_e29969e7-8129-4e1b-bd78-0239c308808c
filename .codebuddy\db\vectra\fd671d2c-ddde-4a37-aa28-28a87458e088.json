{"chunk": 1, "numChunks": 7, "fileHash": "YmBtAc19/HOz7eUBhWxGsON9KNrEblPnxcHiHsXvXv8=", "filePath": "pages/community/community.wxss", "content": "  transition: all 0.3s;\n}\n\n.category-tab.active {\n  color: #fff;\n  background: #8a2be2;\n}\n\n/* 帖子列表 */\n.post-list {\n  margin-top: 220rpx;\n  padding: 20rpx 30rpx;\n}\n\n.post-item {\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\n}\n\n.post-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 12px;\n  position: relative;\n}\n\n.avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  margin-right: 10px;\n}\n\n.post-info {\n  flex: 1;\n}\n\n.username {\n  font-size: 15px;\n  font-weight: 500;\n  color: #333;\n}\n\n.post-meta {\n  display: flex;\n  align-items: center;\n  margin-top: 4px;\n}\n\n.category {\n  position: absolute;\n  top: 0;\n  right: 0;\n  font-size: 12px;\n  color: #fff;\n  padding: 2px 8px;\n  border-radius: 4px;\n  background: #8a2be2;\n}\n\n.time {\n  font-size: 12px;\n  color: #999;\n}\n\n.post-time {\n  font-size: 12px;\n  color: #999;\n  margin-top: 2px;\n}\n\n.category-tag {\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 12px;\n  color: #fff;\n}\n\n.category-tag.ziwei {\n  background: #8a2be2;\n}\n\n"}