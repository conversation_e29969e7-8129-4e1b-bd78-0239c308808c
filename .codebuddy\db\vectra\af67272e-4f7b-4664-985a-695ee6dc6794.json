{"chunk": 4, "numChunks": 11, "fileHash": "i8jYYz/gAcuprKem1BBbiBaSat5ZFs+bjwiR2/ZQErE=", "filePath": "pages/birth-info/birth-info.js", "content": "const globalState = require('../../utils/global-state')\nPage({\n  initDateTimePickerData() {\n    const date = new Date();\n    const years = [];\n    const months = [];\n    const days = [];\n    const hours = [];\n    const minutes = [];\n\n    // 生成年份列表（从1900年到当前年份）\n    for (let i = 1900; i <= date.getFullYear(); i++) {\n      years.push(i + '年');\n    }\n\n    // 生成月份列表\n    for (let i = 1; i <= 12; i++) {\n      months.push(i + '月');\n    }\n\n    // 生成天数列表（默认31天）\n    for (let i = 1; i <= 31; i++) {\n      days.push(i + '日');\n    }\n\n    // 生成小时列表\n    for (let i = 0; i < 24; i++) {\n      hours.push(i + '时');\n    }\n\n    // 生成分钟列表\n    for (let i = 0; i < 60; i++) {\n      minutes.push(i + '分');\n    }\n\n    this.setData({\n      dateTime: [years, months, days, hours, minutes],\n      years,\n      months,\n      days,\n      hours,\n      minutes,\n      dateTimeArray: [0, 0, 0, 0, 0] // 默认选中当前时间\n    });\n  },\n  updateDays(year, month) {\n    const days = [];\n    const daysInMonth = new Date(year, month, 0).getDate();\n    for (let i = 1; i <= daysInMonth; i++) {\n      days.push(i + '日');\n    }\n    return days;\n  },"}