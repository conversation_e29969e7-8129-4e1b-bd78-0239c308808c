{"chunk": 3, "numChunks": 8, "fileHash": "pWWTyJooIxy6mFbGJzYtyes3pCjDoqcgXSX9h6jKjqM=", "filePath": "utils/bazi/calculator.js", "content": "// 天干\nclass BaziCalculator {\n  calculateYearBranch(date) {\n    const year = date.getFullYear();\n    const offset = (year - 4) % 12;\n    return this.earthlyBranches[offset];\n  }\n  calculateMonthStem(date) {\n    const yearStemIndex = this.heavenlyStems.indexOf(this.calculateYearStem(date));\n    const month = date.getMonth() + 1;\n    const offset = (yearStemIndex * 2 + month) % 10;\n    return this.heavenlyStems[offset];\n  }\n  calculateMonthBranch(date) {\n    const month = date.getMonth() + 1;\n    return this.earthlyBranches[month - 1];\n  }\n  calculateDayStem(date) {\n    const baseDate = new Date('1900-01-01');\n    const days = Math.floor((date - baseDate) / (24 * 60 * 60 * 1000));\n    const offset = (days + 10) % 10;\n    return this.heavenlyStems[offset];\n  }\n  calculateDayBranch(date) {\n    const baseDate = new Date('1900-01-01');\n    const days = Math.floor((date - baseDate) / (24 * 60 * 60 * 1000));\n    const offset = (days + 12) % 12;\n    return this.earthlyBranches[offset];\n  }\n  calculateHourStem(date) {\n    const dayStemIndex = this.heavenlyStems.indexOf(this.calculateDayStem(date));\n    const hour = date.getHours();\n    const offset = (dayStemIndex * 2 + Math.floor(hour / 2)) % 10;\n    return this.heavenlyStems[offset];\n  }"}