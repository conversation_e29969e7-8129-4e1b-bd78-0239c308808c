{"chunk": 5, "numChunks": 38, "fileHash": "DpyinnhZJO6jrs0/VY0BXwrNjORlEQ1BeRywBJybhxQ=", "filePath": "pages/ai-chat/ai-chat.js", "content": "const app = getApp()\nPage({\n  async initializeChat() {\n    this.setData({\n      isLoggedIn: apiService.isLoggedIn()\n    })\n\n    // 加载历史消息\n    this.loadMessageHistory()\n\n    // 如果没有历史消息，添加初始化数据\n    if (this.data.messages.length === 0) {\n      this.setData({ isTyping: true })\n\n      try {\n        const initialMessage = '欢迎使用AI命理分析助手！我可以为您提供专业的命理分析和玄学咨询。您可以选择下方的快捷功能，或直接告诉我您想了解什么，比如：八字分析、易经卦象、风水分析、五行分析等。我将根据您的需求为您提供详细的解答和指导。'\n\n        // 使用typeMessage函数来展示消息\n        await this.typeMessage(initialMessage)\n      } catch (error) {\n        console.error('展示初始消息失败:', error)\n        this.handleError(error)\n      } finally {\n        this.setData({ isTyping: false })\n      }\n    }\n  },\n  /**\n   * 获取欢迎消息\n   */"}