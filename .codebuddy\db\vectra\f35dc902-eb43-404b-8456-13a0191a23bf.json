{"chunk": 30, "numChunks": 76, "fileHash": "lPYWZE8QItgwSyEsMXHtl6y/HYBCI8BdGaV+1l8ICMA=", "filePath": "docs/md/API_INTERFACE_DOCUMENTATION.md", "content": "- user_level: string (用户等级)\n\nResponse:\n{\n  \"status\": \"success|error\",\n  \"message\": \"string\",\n  \"data\": {\n    \"recommendations\": [\n      {\n        \"id\": \"string\",\n        \"type\": \"string\",\n        \"title\": \"string\",\n        \"summary\": \"string\",\n        \"reason\": \"string\",           // 推荐理由\n        \"relevance_score\": \"number\",  // 相关性评分\n        \"category\": \"string\",\n        \"tags\": [\"string\"]\n      }\n    ],\n    \"recommendation_context\": {\n      \"based_on\": [\"string\"],         // 推荐依据\n      \"user_interests\": [\"string\"],   // 用户兴趣\n      \"trending_topics\": [\"string\"]   // 热门话题\n    }\n  }\n}\n```\n\n### 知识库管理接口 (管理员)\n\n#### 9. 文档管理 (管理员)\n\n##### 9.1 获取所有文档\n```\nGET /api/admin/knowledge/documents\nAuthorization: Bearer {admin_token}\nQuery Parameters:\n- page: number (页码)\n- limit: number (每页数量)\n- category: string (分类筛选)\n- status: string (状态筛选)\n- search: string (搜索关键词)\n- sort: string (排序字段)\n\nResponse:\n{\n  \"status\": \"success|error\",\n  \"message\": \"string\",\n  \"data\": {\n    \"documents\": [\n      {\n        \"id\": \"string\",\n        \"title\": \"string\",\n        \"summary\": \"string\",\n        \"category\": \"string\",\n        \"status\": \"string\",\n        \"author\": \"string\",\n        \"view_count\": \"number\",\n        \"like_count\": \"number\",\n        \"last_updated_by\": \"string\",\n        \"published_at\": \"datetime\",\n"}