{"chunk": 4, "numChunks": 10, "fileHash": "xkTYshMjb1fCus39fEPzOqyYq9soXCPLKD0cb2FPCl8=", "filePath": "pages/marriage/marriage.js", "content": "// marriage.js\nPage({\n  onMaleDateChange(e) {\n    this.setData({\n      'maleInfo.birthDate': e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 男方时间选择\n  onMaleTimeChange(e) {\n    this.setData({\n      'maleInfo.birthTime': e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 女方姓名输入\n  onFemaleNameInput(e) {\n    this.setData({\n      'femaleInfo.name': e.detail.value.trim()\n    })\n    this.checkCanSubmit()\n  },\n  // 女方日期选择\n  onFemaleDateChange(e) {\n    this.setData({\n      'femaleInfo.birthDate': e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 女方时间选择\n  onFemaleTimeChange(e) {\n    this.setData({\n      'femaleInfo.birthTime': e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 检查是否可以提交\n  checkCanSubmit() {\n    const { maleInfo, femaleInfo } = this.data\n    const canSubmit = \n      maleInfo.name && \n      maleInfo.birthDate && \n      maleInfo.birthTime &&\n      femaleInfo.name && \n      femaleInfo.birthDate && \n      femaleInfo.birthTime\n\n    this.setData({ canSubmit })\n    \n    // 如果数据有更新，保存到缓存\n    if (canSubmit) {\n      this.saveCachedData()\n    }\n  },\n  // 开始分析"}