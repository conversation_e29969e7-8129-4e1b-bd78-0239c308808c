{"chunk": 49, "numChunks": 55, "fileHash": "fCZ6nFoNZXQqhvADUzMwGWD6jOU+ajj61B8BD3CwDgs=", "filePath": "pages/name-test/name-test.js", "content": "// 姓名测试页面逻辑\nPage({\n  generateAnalysis(wuge, sancai, gender) {\n    return [\n      {\n        title: '性格特征',\n        content: this.getPersonalityAnalysis(wuge[1].number, gender),\n        score: this.getScore(wuge[1].level)\n      },\n      {\n        title: '事业财运',\n        content: this.getCareerAnalysis(wuge[4].number),\n        score: this.getScore(wuge[4].level)\n      },\n      {\n        title: '婚姻感情',\n        content: this.getMarriageAnalysis(wuge[2].number, gender),\n        score: this.getScore(wuge[2].level)\n      },\n      {\n        title: '健康状况',\n        content: this.getHealthAnalysis(wuge[0].number),\n        score: this.getScore(wuge[0].level)\n      }\n    ];\n  },\n  // 获取性格分析\n  getPersonalityAnalysis(number, gender) {\n    const genderText = gender === 'male' ? '他' : '她';\n    const analyses = {\n      1: `${genderText}具有领导才能，性格坚强独立`,\n      2: `${genderText}性格温和，善于与人合作`,\n      3: `${genderText}富有创造力，表达能力强`,\n      4: `${genderText}做事踏实，但可能过于保守`,\n      5: `${genderText}追求自由，喜欢变化和冒险`\n    };\n    return analyses[number % 5 + 1] || `${genderText}性格温和稳重，为"}