{"chunk": 7, "numChunks": 16, "fileHash": "9ZF2KXbrmWumjo5LxJnE/F14ePFZLBzZ6HxD55jx3zE=", "filePath": "pages/fengshui/fengshui.js", "content": "// pages/fengshui/fengshui.js\nPage({\n  loadCachedData() {\n    try {\n      const cachedData = wx.getStorageSync('fengshuiData')\n      if (cachedData) {\n        this.setData({\n          directionIndex: cachedData.directionIndex,\n          houseTypeIndex: cachedData.houseTypeIndex,\n          buildYear: cachedData.buildYear,\n          rooms: cachedData.rooms || 1,\n          area: cachedData.area,\n          currentFloor: cachedData.currentFloor,\n          totalFloor: cachedData.totalFloor,\n          surroundings: cachedData.surroundings || this.data.surroundings,\n          specialLocations: cachedData.specialLocations || this.data.specialLocations\n        })\n        this.checkCanSubmit()\n      }\n    } catch (error) {\n      console.error('加载缓存数据失败:', error)\n    }\n  },\n  // 保存数据到缓存\n  saveCachedData() {\n    try {\n      const {\n        directionIndex,\n        houseTypeIndex,\n        buildYear,\n        rooms,\n        area,\n        currentFloor,\n        totalFloor,\n        surroundings,\n        specialLocations\n      } = this.data\n      \n      wx.setStorageSync('fengshuiData', {\n        directionIndex,\n        houseTypeIndex,\n        buildYear,\n        rooms,\n        area,\n        currentFloor,\n        totalFloor,\n        surroundings,\n        specialLocations,\n        timestamp: new Date().getTime()\n      })\n    } catch (error) {\n      console.error('保存缓存数据失败:', error)\n    }\n  },\n  // 房屋朝向选择\n  onDirectionChange(e) {\n    this.setData({\n      directionIndex: parseInt(e.detail.value)\n    })\n    this.checkCanSubmit()\n  },\n  // 房屋类型选择\n  onHouseTypeChange(e) {\n    this.setData({\n      houseTypeIndex: parseInt(e.detail.value)\n    })\n    this.checkCanSubmit()\n"}