{"chunk": 10, "numChunks": 15, "fileHash": "O/nJs1XsxYT9PDo4XFlMEnsiqZRG/Zu4Jl5KoBF5Ci8=", "filePath": "pages/index/index.wxml", "content": "      </view>\n\n      <!-- 主要内容 -->\n      <view class=\"content\" wx:else>\n      <!-- 新增：时间问候头部 -->\n      <view class=\"time-greeting-header\">\n        <view class=\"greeting-section\">\n          <text class=\"greeting-text\">{{greeting}}</text>\n          <text class=\"user-name\" wx:if=\"{{userInfo.nickName}}\">{{userInfo.nickName}}</text>\n        </view>\n        \n        <view class=\"calendar-section\">\n          <view class=\"solar-date\">\n            <text class=\"date-number\">{{todayInfo.solar.day}}</text>\n            <view class=\"date-info\">\n              <text class=\"month-year\">{{todayInfo.solar.year}}年{{todayInfo.solar.month}}月</text>\n              <text class=\"weekday\">{{todayInfo.solar.weekday}}</text>\n            </view>\n          </view>\n          \n          <view class=\"lunar-info\">\n            <view class=\"lunar-date\">\n              <text class=\"lunar-text\">{{todayInfo.lunar.monthCn}}{{todayInfo.lunar.dayCn}}</text>\n              <text class=\"ganzhi-year\">{{todayInfo.lunar.gzYear}}年 [{{todayInfo.lunar.animal}}]</text>\n            </view>\n            \n            <view class=\"calendar-details\">\n              <view class=\"term-info\" wx:if=\"{{todayInfo.lunar.term}}\">\n                <text class=\"term-icon\">🌞</text>\n                <text class=\"term-text\">{{todayInfo.lunar.term}}</text>\n              </view>\n              \n              <view class=\"yiji-info\">\n                <view class=\"yi-items\" wx:if=\"{{todayInfo.lunar.yiji.yi.length}}\">\n                  <text class=\"yiji-label\">宜：</text>\n                  <view class=\"yiji-content\">\n"}