{"chunk": 6, "numChunks": 7, "fileHash": "RAGdlTZI62FtOLCwB83HuJtgEeQmh/OOiLgGD1MsfmA=", "filePath": "frontend-implementation/utils/request.js", "content": "// 网络请求工具\n// 创建请求实例\nconst requestInterceptor = new RequestInterceptor()\n/**\n * 通用请求方法\n * @param {Object} options 请求配置\n */\nexport const request = (options) => {\n  return requestInterceptor.request(options)\n}\n/**\n * GET请求\n * @param {string} url 请求地址\n * @param {Object} params 请求参数\n * @param {Object} config 请求配置\n */\nexport const get = (url, params = {}, config = {}) => {\n  return request({\n    url,\n    method: 'GET',\n    data: params,\n    ...config\n  })\n}\n/**\n * POST请求\n * @param {string} url 请求地址\n * @param {Object} data 请求数据\n * @param {Object} config 请求配置\n */\nexport const post = (url, data = {}, config = {}) => {\n  return request({\n    url,\n    method: 'POST',\n    data,\n    ...config\n  })\n}\n/**\n * PUT请求\n * @param {string} url 请求地址\n * @param {Object} data 请求数据\n * @param {Object} config 请求配置\n */\nexport const put = (url, data = {}, config = {}) => {\n  return request({\n    url,\n    method: 'PUT',\n    data,\n    ...config\n  })\n}\n/**\n * DELETE请求\n * @param {string} url 请求地址\n * @param {Object} config 请求配置\n */\nexport const del = (url, config = {}) => {\n  return request({\n    url,\n    method: 'DELETE',\n    ...config\n  })\n}"}