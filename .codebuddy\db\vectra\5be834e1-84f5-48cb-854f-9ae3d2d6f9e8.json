{"chunk": 15, "numChunks": 22, "fileHash": "jr30LZOMc1P5VIpMptyaGnan9xv44m/c09rHA5aMeTY=", "filePath": "pages/index/index.js", "content": "// index.js\nPage({\n  // 记录使用的功能\n  recordUsage(type) {\n    let recentUsed = wx.getStorageSync('recentUsed') || []\n    \n    // 移除已存在的相同功能\n    recentUsed = recentUsed.filter(item => item.type !== type)\n    \n    // 添加到开头\n    recentUsed.unshift({\n      type,\n      time: new Date().getTime()\n    })\n    \n    // 只保留最近5个\n    if (recentUsed.length > 5) {\n      recentUsed = recentUsed.slice(0, 5)\n    }\n    \n    wx.setStorageSync('recentUsed', recentUsed)\n    this.loadRecentUsed()\n  },\n  // 清空最近使用\n  clearRecentUsed() {\n    wx.showModal({\n      title: '提示',\n      content: '确定要清空最近使用记录吗？',\n      success: (res) => {\n        if (res.confirm) {\n          wx.removeStorageSync('recentUsed')\n          this.setData({ recentUsed: [] })\n          wx.showToast({\n            title: '已清空',\n            icon: 'success'\n          })\n        }\n      }\n    })\n  },\n  // 最近使用项目点击\n  onRecentItemTap(e) {\n    const type = e.currentTarget.dataset.type\n    const featureInfo = this.getFeatureInfo(type)\n    if (featureInfo) {\n      this.navigateTo(featureInfo.url)\n    }\n  },\n  // 获取热门文章"}