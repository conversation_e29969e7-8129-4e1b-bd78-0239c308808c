{"chunk": 2, "numChunks": 6, "fileHash": "LfPZZEUjbDaiOVOLCtwxAuZgKgXnDAGyjWMO0X626Ik=", "filePath": "pages/ai-chat/ai-chat.wxml", "content": "              wx:key=\"id\"\n              id=\"message-{{index}}\">\n\n          <!-- 消息时间分隔 -->\n          <view class=\"time-divider\" wx:if=\"{{item.showTime}}\">\n            <text class=\"time-text\">{{item.timestamp}}</text>\n          </view>\n\n          <!-- 用户消息 -->\n          <view class=\"message-bubble user-message\" wx:if=\"{{item.type === 'user'}}\">\n            \n            <view class=\"message-avatar user-avatar\">\n              <text class=\"avatar-text\">我</text>\n            </view>\n            <view class=\"message-content\">\n              <view class=\"message-text\">{{item.content}}</view>\n            </view>\n          </view>\n\n          <!-- AI消息 -->\n          <view class=\"message-bubble ai-message\" wx:if=\"{{item.type === 'ai' || item.type === 'system'}}\">\n            <view class=\"message-avatar ai-avatar\">\n              <view class=\"avatar-glow-small\"></view>\n              <image class=\"avatar-icon\" src=\"https://img.icons8.com/fluency-systems-filled/96/9575cd/artificial-intelligence.png\"></image>\n            </view>\n            <view class=\"message-content\">\n              <view class=\"message-text {{item.isTyping ? 'typing-effect' : ''}}\">{{item.content}}</view>\n\n              <!-- 消息操作按钮 -->\n              <view class=\"message-actions\" wx:if=\"{{item.actions && item.actions.length > 0}}\">\n                <view class=\"action-button\"\n                      wx:for=\"{{item.actions}}\"\n                      wx:for-item=\"action\"\n                      wx:key=\"type\"\n                      data-action=\"{{action.type}}\"\n                      data-url=\"{{action.url}}\"\n                      bindtap=\"onMessageAction\">\n                  <text class=\"action-text\">{{action.text}}</text>\n                  <text class=\"action-icon\">{{action.icon || '→'}}</text>\n                </view>\n              </view>\n\n"}