{"chunk": 3, "numChunks": 6, "fileHash": "8h8E6IyA2QKaLBRxCAXNE6RcQ2dvD/eV8dT/ZBdIuAw=", "filePath": "pages/marriage/marriage.wxml", "content": "        <text class=\"tip-item\">• 您的信息将被安全保护，仅用于合婚分析</text>\n      </view>\n    </view>\n\n    <!-- 提交按钮 -->\n    <view class=\"submit-section\">\n      <button\n        class=\"submit-btn {{canSubmit ? 'active' : 'disabled'}}\"\n        bindtap=\"startAnalysis\"\n        disabled=\"{{!canSubmit}}\"\n      >\n        <text class=\"btn-text\">开始合婚测算</text>\n        <text class=\"btn-icon\">💕</text>\n      </button>\n    </view>\n  </view>\n\n  <!-- 分析结果区域 -->\n  <view class=\"result-container\" wx:if=\"{{analysisResult}}\">\n    <!-- 总体评分 -->\n    <view class=\"info-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">匹配评分</text>\n        <text class=\"card-icon\">💯</text>\n      </view>\n      <view class=\"score-section\">\n        <view class=\"score-ring\">\n          <view class=\"score-value\">{{analysisResult.totalScore}}分</view>\n          <view class=\"score-desc\">{{analysisResult.compatibility}}</view>\n        </view>\n        <view class=\"score-detail\">{{analysisResult.description}}</view>\n      </view>\n    </view>\n\n    <!-- 八字对比 -->\n    <view class=\"info-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">八字对比</text>\n        <text class=\"card-icon\">🔮</text>\n      </view>\n      <view class=\"bazi-comparison\">\n        <view class=\"bazi-column\">\n          <view class=\"person-name\">{{maleInfo.name}}</view>\n          <view class=\"bazi-pillars\">\n"}