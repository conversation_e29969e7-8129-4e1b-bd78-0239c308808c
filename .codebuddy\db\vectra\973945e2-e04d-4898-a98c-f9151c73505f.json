{"chunk": 18, "numChunks": 22, "fileHash": "jr30LZOMc1P5VIpMptyaGnan9xv44m/c09rHA5aMeTY=", "filePath": "pages/index/index.js", "content": "// index.js\nPage({\n  onInputChange(e) {\n    const nickName = e.detail.value\n    const { avatarUrl } = this.data.userInfo\n    this.setData({\n      \"userInfo.nickName\": nickName,\n      hasUserInfo: nickName && avatarUrl && avatarUrl !== defaultAvatarUrl,\n    })\n  },\n  getUserProfile(e) {\n    // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认，开发者妥善保管用户快速填写的头像昵称，避免重复弹窗\n    wx.getUserProfile({\n      desc: '用于完善会员资料',\n      success: (res) => {\n        console.log(res)\n        app.globalData.userInfo = res.userInfo\n        this.setData({\n          userInfo: res.userInfo,\n          hasUserInfo: true\n        })\n      }\n    })\n  },\n  // 检查节气\n  checkSolarTerms() {\n    const now = new Date()\n    const year = now.getFullYear()\n    const month = now.getMonth() + 1\n    const day = now.getDate()\n    \n    // 模拟节气数据（实际应该调用云函数）\n    const solarTermsData = this.getSolarTermsData(year, month, day)\n    \n    this.setData({\n      solarTerms: solarTermsData\n    })\n    \n    // 如果今天是节气，显示提醒\n    if (solarTermsData && solarTermsData.isToday) {\n      wx.showToast({\n        title: `今天是${solarTermsData.name}`,\n        icon: 'none',\n        duration: 3000\n      })\n    }\n  },\n  // 获取节气数据（简化版本）"}