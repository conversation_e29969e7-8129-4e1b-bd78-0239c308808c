{"chunk": 2, "numChunks": 6, "fileHash": "VToXv7ATbmi0am+Amh5z0qVhRJK7IjhEVx3t5LgFVgM=", "filePath": "pages/marriage/index.wxml", "content": "      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">出生时间</text>\n          <text class=\"required\">*</text>\n        </view>\n        <picker mode=\"time\" value=\"{{female.time}}\" bindchange=\"bindFemaleTimeChange\">\n          <view class=\"picker-wrapper\">\n            <text class=\"picker-text {{female.time ? 'selected' : 'placeholder'}}\">\n              {{female.time || '请选择出生时间'}}\n            </text>\n            <text class=\"picker-icon\">⏰</text>\n          </view>\n        </picker>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">出生地点</text>\n          <text class=\"optional\">选填</text>\n        </view>\n        <view class=\"picker-wrapper\" bindtap=\"chooseFemaleLocation\">\n          <text class=\"picker-text {{female.location ? 'selected' : 'placeholder'}}\">\n            {{female.location || '点击选择出生地点'}}\n          </text>\n          <text class=\"picker-icon\">📍</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 提示信息 -->\n    <view class=\"tips-card\">\n      <view class=\"tips-header\">\n        <text class=\"tips-icon\">💡</text>\n        <text class=\"tips-title\">温馨提示</text>\n      </view>\n      <view class=\"tips-content\">\n        <text class=\"tip-item\">• 出生日期和时间为必填项</text>\n        <text class=\"tip-item\">• 出生时间越精确，合婚结果越准确</text>\n"}