{"chunk": 3, "numChunks": 9, "fileHash": "b5v2J+xqqZLB03zKt2qI4RvF0LmgPXZeserUsC/MOx0=", "filePath": "frontend-implementation/utils/wx.js", "content": "// 微信小程序API封装工具\nexport const navigateTo = (url, options = {}) => {\n  wx.navigateTo({\n    url,\n    ...options\n  })\n}\n/**\n * 关闭当前页面，跳转到应用内的某个页面\n * @param {string} url 页面路径\n * @param {Object} options 配置选项\n */\nexport const redirectTo = (url, options = {}) => {\n  wx.redirectTo({\n    url,\n    ...options\n  })\n}\n/**\n * 跳转到 tabBar 页面\n * @param {string} url 页面路径\n * @param {Object} options 配置选项\n */\nexport const switchTab = (url, options = {}) => {\n  wx.switchTab({\n    url,\n    ...options\n  })\n}\n/**\n * 关闭所有页面，打开到应用内的某个页面\n * @param {string} url 页面路径\n * @param {Object} options 配置选项\n */\nexport const reLaunch = (url, options = {}) => {\n  wx.reLaunch({\n    url,\n    ...options\n  })\n}\n/**\n * 关闭当前页面，返回上一页面或多级页面\n * @param {number} delta 返回的页面数\n */\nexport const navigateBack = (delta = 1) => {\n  wx.navigateBack({\n    delta\n  })\n}\n/**\n * 获取系统信息\n */\nexport const getSystemInfo = () => {\n  return new Promise((resolve, reject) => {\n    wx.getSystemInfo({\n      success: resolve,\n      fail: reject\n    })\n  })\n}\n/**\n * 获取用户信息\n * @param {Object} options 配置选项\n */\nexport const getUserInfo = (options = {}) => {\n  return new Promise((resolve, reject) => {\n    wx.getUserInfo({\n      ...options,\n      success: resolve"}