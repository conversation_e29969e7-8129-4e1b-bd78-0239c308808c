{"chunk": 0, "numChunks": 5, "fileHash": "tA9ixVr6Arfzki0CzquWOFo8c2xDC4cgMIk+7vjNaBk=", "filePath": "pages/ziwei/ziwei.wxml", "content": "<!--ziwei.wxml-->\n<view class=\"container\">\n  <!-- 使用出生信息组件 -->\n  <birth-info bind:change=\"onBirthInfoChange\"></birth-info>\n  \n  <!-- 加载中 -->\n  <view class=\"loading\" wx:if=\"{{loading}}\">\n    <text>正在计算紫微斗数...</text>\n  </view>\n\n  <!-- 错误提示 -->\n  <view class=\"error\" wx:if=\"{{error}}\">\n    <text>{{error}}</text>\n  </view>\n  \n  <!-- 紫微斗数命盘展示 -->\n  <view class=\"result-section\" wx:if=\"{{ziweiResult}}\">\n    <view class=\"section-title\">紫微斗数命盘</view>\n    \n    <!-- 命盘图 -->\n    <view class=\"chart-section\">\n      <view class=\"chart-title\">紫微斗数命盘</view>\n      <view class=\"ziwei-chart\">\n        <view class=\"chart-grid\">\n          <view class=\"grid-cell\" wx:for=\"{{ziweiResult.palaces}}\" wx:key=\"position\">\n            <view class=\"palace-name\">{{item.name}}</view>\n            <view class=\"main-star\" wx:if=\"{{item.mainStar}}\">{{item.mainStar}}</view>\n            <view class=\"minor-stars\">\n              <text class=\"star {{star.type === '主星' ? 'star-main' : star.type === '辅星' ? 'star-assistant' : star.type === '吉星' ? 'star-lucky' : 'star-evil'}}\" \n                    wx:for=\"{{item.stars}}\" \n                    wx:key=\"name\" \n                    wx:for-item=\"star\">{{star.name}}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n"}