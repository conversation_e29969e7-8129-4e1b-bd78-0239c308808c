{"chunk": 3, "numChunks": 7, "fileHash": "RAGdlTZI62FtOLCwB83HuJtgEeQmh/OOiLgGD1MsfmA=", "filePath": "frontend-implementation/utils/request.js", "content": "// 网络请求工具\nclass RequestInterceptor {\n  async handleTokenExpired(originalOptions) {\n    if (this.isRefreshing) {\n      // 如果正在刷新，将请求加入队列\n      return new Promise((resolve, reject) => {\n        this.refreshSubscribers.push({ resolve, reject, options: originalOptions })\n      })\n    }\n\n    this.isRefreshing = true\n\n    try {\n      const refreshTokenValue = getStorageSync('refreshToken')\n      if (!refreshTokenValue) {\n        throw new Error('无刷新Token')\n      }\n\n      // 刷新Token\n      const result = await refreshToken(refreshTokenValue)\n      \n      if (result.status === 'success') {\n        // 保存新Token\n        setStorageSync('token', result.data.token)\n        setStorageSync('refreshToken', result.data.refresh_token)\n\n        // 重试原请求\n        const retryResult = await this.request(originalOptions)\n\n        // 处理队列中的请求\n        this.refreshSubscribers.forEach(({ resolve, options }) => {\n          resolve(this.request(options))\n        })\n\n        return retryResult\n      } else {\n        throw new Error('刷新Token失败')\n      }\n    } catch (error) {\n      // 刷新失败，清除登录信息并跳转到登录页\n      this.clearAuthAndRedirect()\n      \n      // 拒绝队列中的请求\n      this.refreshSubscribers.forEach(({ reject }) => {\n        reject(error)\n      })\n      \n      throw error\n    } finally {\n      this.isRefreshing = false\n      this.refreshSubscribers = []\n    }\n  }\n  /**\n   * 清除认证信息并跳转登录\n   */"}