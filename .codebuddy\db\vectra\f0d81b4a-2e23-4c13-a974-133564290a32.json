{"chunk": 4, "numChunks": 5, "fileHash": "TJfinHLkoiG7M+2rWC5PAMDH2NvSIYiEljTKpOkIrmA=", "filePath": "pages/wuxing/wuxing.js", "content": "// wuxing.js\nPage({\n  calculateWuxing() {\n    const { birthDate, birthTime } = this.data;\n    \n    if (!birthDate || !birthTime) {\n      wx.showToast({\n        title: '请选择出生日期和时间',\n        icon: 'none'\n      });\n      return;\n    }\n\n    this.setData({ loading: true });\n\n    try {\n      const calculator = new WuxingCalculator();\n      const date = new Date(birthDate);\n      \n      // 计算五行强度\n      const strengths = calculator.calculateStrength(date, birthTime);\n      \n      // 获取分析结果\n      const analysis = calculator.getAnalysis(strengths);\n      \n      // 获取五行关系\n      const relationships = calculator.getRelationships(strengths);\n      \n      // 处理分布数据\n      const distribution = Object.entries(strengths).map(([element, strength]) => ({\n        element: calculator.wuxing[element].name,\n        strength: strength\n      })).sort((a, b) => b.strength - a.strength);\n\n      this.setData({\n        wuxingResult: {\n          distribution,\n          analysis,\n          relationships\n        },\n        loading: false\n      });\n    } catch (error) {\n      console.error('五行计算错误:', error);\n      wx.showToast({\n        title: '计算失败，请重试',\n        icon: 'none'\n      });\n      this.setData({ loading: false });\n    }\n  },\n  // 获取相生关系\n  getShengRelations(wuxing) {\n    const relations = {\n      JIN: '金生水',\n      SHUI: '水生木',\n      MU: '木生火',\n      HUO: '火生土',\n      TU: '土生金'\n    }\n    return relations[wuxing]\n  },\n  // 获取相克关系"}