{"chunk": 2, "numChunks": 3, "fileHash": "KbYz8RPaMNaNsrATEC40xrxWBStbsbXj6opL+PU1VIA=", "filePath": "subpages/admin/logs/logs.js", "content": "Page({\n  /**\n   * 页面的初始数据\n   */\n  data: {\n    logs: [],\n    filteredLogs: [],\n    activeFilter: 'all',\n    filters: [\n      { key: 'all', name: '全部' },\n      { key: 'error', name: '错误' },\n      { key: 'warning', name: '警告' },\n      { key: 'info', name: '信息' }\n    ]\n  },\n  /**\n   * 生命周期函数--监听页面加载\n   */\n  onLoad(options) {\n    this.loadLogs()\n  },\n  /**\n   * 生命周期函数--监听页面显示\n   */\n  onShow() {\n    \n  },\n  /**\n   * 加载日志数据\n   */\n  loadLogs() {\n    // 模拟日志数据\n    const mockLogs = [\n      {\n        id: 1,\n        time: '2024-01-15 14:30:25',\n        level: 'error',\n        message: '数据库连接失败',\n        source: 'system.db'\n      },\n      {\n        id: 2,\n        time: '2024-01-15 14:25:10',\n        level: 'warning',\n        message: '内存使用率过高 (85%)',\n        source: 'system.monitor'\n      },\n      {\n        id: 3,\n        time: '2024-01-15 14:20:05',\n        level: 'info',\n        message: '用户登录成功',\n        source: 'auth.service'\n      },\n      {\n        id: 4,\n        time: '2024-01-15 14:15:30',\n        level: 'info',\n        message: '系统启动完成',\n        source: 'system.boot'\n      }\n    ]\n\n    this.setData({\n      logs: mockLogs,\n      filteredLogs: mockLogs\n    })\n  },"}