{"chunk": 1, "numChunks": 3, "fileHash": "gBZ6rOH7FJiiaL1bL4AO7Rv3z4sWp7hh/CVaiX5DB/k=", "filePath": "pages/name/index.wxml", "content": "      <view class=\"card-title\">五格数理</view>\n      <view class=\"wuge-grid\">\n        <view class=\"grid-item\" wx:for=\"{{wugeResult}}\" wx:key=\"name\">\n          <text class=\"grid-name\">{{item.name}}</text>\n          <text class=\"grid-number\">{{item.number}}</text>\n          <text class=\"grid-meaning\">{{item.meaning}}</text>\n        </view>\n      </view>\n    </view>\n\n    <view class=\"result-card\">\n      <view class=\"card-title\">姓名评分</view>\n      <view class=\"score-section\">\n        <text class=\"total-score\">{{nameScore}}</text>\n        <text class=\"max-score\">/100</text>\n        <text class=\"score-desc\">{{scoreDescription}}</text>\n      </view>\n    </view>\n\n    <view class=\"result-card\">\n      <view class=\"card-title\">五行分析</view>\n      <view class=\"wuxing-analysis\">\n        <view class=\"wuxing-item\" wx:for=\"{{wuxingResult}}\" wx:key=\"element\">\n          <text class=\"element-name\">{{item.element}}</text>\n          <progress percent=\"{{item.percentage}}\" stroke-width=\"12\" color=\"{{item.color}}\"/>\n        </view>\n      </view>\n    </view>\n\n    <view class=\"result-card\">\n      <view class=\"card-title\">姓名详解</view>\n      <view class=\"name-details\">\n        <view class=\"detail-item\" wx:for=\"{{nameDetails}}\" wx:key=\"aspect\">\n          <text class=\"aspect-name\">{{item.aspect}}</text>\n          <text class=\"aspect-desc\">{{item.description}}</text>\n        </view>\n      </view>\n    </view>\n\n    <view class=\"result-card\">\n      <view class=\"card-title\">运势分析</view>\n      <view class=\"fortune-analysis\">\n"}