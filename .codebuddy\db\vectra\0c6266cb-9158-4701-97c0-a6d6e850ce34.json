{"chunk": 11, "numChunks": 12, "fileHash": "5X5f8yfnxquoBxYzBGhnUPxPUBZeZKQywMAReox6cBk=", "filePath": "pages/bazi/index.js", "content": "const app = getApp()\nPage({\n  },\n  validateInput() {\n    if (!this.data.birthDate) {\n      wx.showToast({\n        title: '请选择出生日期',\n        icon: 'none'\n      });\n      return false;\n    }\n    if (!this.data.birthTime) {\n      wx.showToast({\n        title: '请选择出生时间',\n        icon: 'none'\n      });\n      return false;\n    }\n    return true;\n  },"}