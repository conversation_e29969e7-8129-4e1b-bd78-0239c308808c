{"chunk": 0, "numChunks": 6, "fileHash": "3s34je0qL5+ULluXZdiBSih2mwyLkPtuCPPtZ9V/C7o=", "filePath": "pages/community/community.wxml", "content": "<!--pages/community/community.wxml-->\n<view class=\"container\">\n  <!-- 顶部搜索栏 -->\n  <view class=\"search-bar\">\n    <view class=\"search-input {{searchFocus ? 'focus' : ''}}\">\n      <icon type=\"search\" size=\"14\"></icon>\n      <input \n        type=\"text\" \n        placeholder=\"搜索帖子、用户、话题\" \n        bindinput=\"onSearchInput\" \n        bindconfirm=\"onSearchConfirm\"\n        bindfocus=\"onSearchFocus\"\n        bindblur=\"onSearchBlur\"\n        value=\"{{searchKeyword}}\"\n      />\n      <view class=\"clear-btn\" wx:if=\"{{searchKeyword}}\" bindtap=\"clearSearch\">×</view>\n    </view>\n  </view>\n\n  <!-- 搜索历史 -->\n  <view class=\"search-history\" wx:if=\"{{searchFocus && searchHistory.length > 0}}\">\n    <view class=\"history-header\">\n      <text>搜索历史</text>\n      <text class=\"clear-history\" bindtap=\"clearSearchHistory\">清空</text>\n    </view>\n    <view class=\"history-list\">\n      <view \n        class=\"history-item\" \n        wx:for=\"{{searchHistory}}\" \n        wx:key=\"*this\"\n        bindtap=\"selectSearchHistory\"\n        data-keyword=\"{{item}}\"\n      >\n        <icon type=\"search\" size=\"12\"></icon>\n        <text>{{item}}</text>\n      </view>\n    </view>\n  </view>\n\n  <!-- 分类和排序栏 -->\n  <view class=\"filter-bar\">\n    <!-- 分类标签 -->\n    <scroll-view class=\"category-tabs\" scroll-x>\n      <view \n        wx:for=\"{{categories}}\" \n        wx:key=\"value\" \n        class=\"category-tab {{currentCategory === item.value ? 'active' : ''}}\"\n        data-category=\"{{item.value}}\"\n"}