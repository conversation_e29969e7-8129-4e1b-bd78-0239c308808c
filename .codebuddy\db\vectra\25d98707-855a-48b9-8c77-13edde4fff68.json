{"chunk": 0, "numChunks": 2, "fileHash": "rGYNB01cIDbBthG6i33zJhi+Gbc0TXNToORhXNRBRV4=", "filePath": "pages/settings/index.wxss", "content": "/* pages/settings/index.wxss */\n.container {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  padding-bottom: 40rpx;\n}\n\n/* 设置区块 */\n.section {\n  background-color: #fff;\n  margin: 20rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n}\n\n.section-title {\n  font-size: 32rpx;\n  color: #333;\n  font-weight: bold;\n  padding: 30rpx;\n  border-bottom: 2rpx solid #f5f5f5;\n}\n\n/* 设置列表 */\n.setting-list {\n  padding: 0 30rpx;\n}\n\n.setting-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 30rpx 0;\n  border-bottom: 2rpx solid #f5f5f5;\n}\n\n.setting-item:last-child {\n  border-bottom: none;\n}\n\n.setting-left {\n  display: flex;\n  align-items: center;\n}\n\n.setting-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-right: 20rpx;\n}\n\n.setting-name {\n  font-size: 28rpx;\n  color: #333;\n}\n\n/* 开关样式 */\nswitch {\n  transform: scale(0.8);\n}\n\n/* 选择器样式 */\n.picker-text {\n  font-size: 28rpx;\n  color: #666;\n  padding-right: 30rpx;\n  position: relative;\n}\n\n.picker-text::after {\n  content: '';\n  position: absolute;\n  right: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 16rpx;\n  height: 16rpx;\n  border-right: 4rpx solid #999;\n"}