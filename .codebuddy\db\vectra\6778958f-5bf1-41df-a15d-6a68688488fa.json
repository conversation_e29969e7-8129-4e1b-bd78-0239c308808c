{"chunk": 1, "numChunks": 3, "fileHash": "yBfSiux97Av5YAlaSefOy5NSKkeIfX1ubntqZcbY4Hg=", "filePath": "subpages/admin/logs/logs.wxss", "content": "  flex-direction: column;\n  gap: 16rpx;\n}\n\n.log-item {\n  padding: 24rpx;\n  background: #f8f9fa;\n  border-radius: 12rpx;\n  border-left: 6rpx solid #ddd;\n  transition: all 0.3s ease;\n}\n\n.log-item.error {\n  border-left-color: #f44336;\n  background: #fef5f5;\n}\n\n.log-item.warning {\n  border-left-color: #ff9800;\n  background: #fff8f0;\n}\n\n.log-item.info {\n  border-left-color: #2196F3;\n  background: #f0f8ff;\n}\n\n.log-item:active {\n  transform: translateY(1rpx);\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.log-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12rpx;\n}\n\n.log-time {\n  font-size: 22rpx;\n  color: #666;\n  font-family: monospace;\n}\n\n.log-level {\n  font-size: 20rpx;\n  font-weight: bold;\n  padding: 4rpx 12rpx;\n  border-radius: 12rpx;\n  background: #ddd;\n  color: #666;\n}\n\n.log-item.error .log-level {\n  background: #f44336;\n  color: white;\n}\n\n.log-item.warning .log-level {\n  background: #ff9800;\n  color: white;\n}\n\n.log-item.info .log-level {\n  background: #2196F3;\n  color: white;\n}\n\n.log-message {\n  font-size: 26rpx;\n  color: #333;\n  margin-bottom: 8rpx;\n  line-height: 1.4;\n"}