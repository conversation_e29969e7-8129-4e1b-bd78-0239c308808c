{"chunk": 3, "numChunks": 14, "fileHash": "3g+f4UKfyPQOeFOGstuRr+TvNVx6NqSI5ZfRnXdVunU=", "filePath": "pages/profile/profile.js", "content": "// pages/profile/profile.js\nPage({\n  /**\n   * 页面的初始数据\n   */\n  data: {\n    userInfo: null,\n    hasUserInfo: false,\n    canIUseGetUserProfile: false,\n    points: 0,\n    level: 1,\n    favorites: [],\n    records: [],\n    settings: {\n      theme: 'light',\n      notification: true\n    },\n    activeTab: 'records',\n    birthInfo: null,\n    signInStatus: false,\n    isWxWork: false,\n    menuItems: [\n      {\n        id: 'birth-info',\n        title: '出生信息',\n        icon: '/assets/icons/profile/birth-info.png',\n        url: '/pages/birth-info/birth-info',\n        desc: '管理您的出生信息'\n      },\n      {\n        id: 'sign-in',\n        title: '签到中心',\n        icon: '/assets/icons/profile/sign-in.png',\n        url: '/pages/sign-in/sign-in',\n        desc: '每日签到获取积分'\n      },\n      {\n        id: 'recharge',\n        title: '充值中心',\n        icon: '/assets/icons/profile/recharge.png',\n        url: '/pages/recharge/recharge',\n        desc: '充值获取更多服务'\n      },\n      {\n        id: 'feedback',\n        title: '意见反馈',\n        icon: '/assets/icons/profile/feedback.png',\n        url: '/pages/feedback/feedback',\n        desc: '帮助我们改进产品'\n      },\n      {\n        id: 'settings',\n        title: '设置',\n        icon: '/assets/icons/profile/settings.png',\n        url: '/pages/settings/settings',\n        desc: '个性化设置'\n      }\n    ]\n  },\n  /**\n   * 生命周期函数--监听页面加载\n   */"}