{"chunk": 5, "numChunks": 6, "fileHash": "IwGBwJ25Blubhn4hCt83o/e9MDZEM6l3SvRIm1A0vrE=", "filePath": "pages/fengshui/fengshui.wxml", "content": "      <view class=\"analysis-details\">\n        <view class=\"analysis-item\" wx:for=\"{{analysisResult.details}}\" wx:key=\"type\">\n          <view class=\"analysis-header\">\n            <view class=\"analysis-title\">\n              <text class=\"analysis-icon\">{{item.icon}}</text>\n              <text>{{item.title}}</text>\n            </view>\n            <view class=\"analysis-score {{item.level}}\">{{item.score}}分</view>\n          </view>\n          <view class=\"analysis-content\">{{item.content}}</view>\n          <view class=\"score-bar\">\n            <view class=\"score-fill\" style=\"width: {{item.score}}%; background: {{item.color}}\"></view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 改善建议 -->\n    <view class=\"info-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">改善建议</text>\n        <text class=\"card-icon\">💡</text>\n      </view>\n      <view class=\"suggestions-list\">\n        <view class=\"suggestion-item\" wx:for=\"{{analysisResult.suggestions}}\" wx:key=\"index\">\n          <text class=\"suggestion-dot\">•</text>\n          <text class=\"suggestion-text\">{{item}}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 操作按钮 -->\n    <view class=\"action-buttons\">\n      <button class=\"reset-btn\" bindtap=\"resetAnalysis\">\n        <text class=\"btn-text\">重新分析</text>\n        <text class=\"btn-icon\">🔄</text>\n      </button>\n      <button class=\"share-btn\" open-type=\"share\">\n        <text class=\"btn-text\">分享结果</text>\n        <text class=\"btn-icon\">📤</text>\n      </button>\n    </view>\n  </view>\n</view>\n"}