{"chunk": 3, "numChunks": 5, "fileHash": "TJfinHLkoiG7M+2rWC5PAMDH2NvSIYiEljTKpOkIrmA=", "filePath": "pages/wuxing/wuxing.js", "content": "// wuxing.js\nPage({\n  data: {\n    birthDate: '',\n    birthTime: '',\n    gender: '男',\n    isLoading: false,\n    wuxingResult: null,\n    loading: false\n  },\n  onLoad() {\n    // 检查是否有出生信息\n    if (!checkBirthInfo()) {\n      navigateToBirthInfo('/pages/wuxing/wuxing')\n      return\n    }\n    try {\n      // 加载已保存的出生信息\n      const birthInfo = wx.getStorageSync('birthInfo')\n      if (birthInfo) {\n        this.setData({\n          birthDate: birthInfo.birthDate,\n          birthTime: birthInfo.birthTime,\n          gender: birthInfo.gender\n        })\n        // 自动计算五行\n        this.calculateWuxing()\n      }\n    } catch (error) {\n      console.error('加载出生信息失败:', error)\n    }\n  },\n  onDateChange(e) {\n    this.setData({\n      birthDate: e.detail.value\n    })\n  },\n  onTimeChange(e) {\n    this.setData({\n      birthTime: e.detail.value\n    })\n  },\n  onGenderChange(e) {\n    this.setData({\n      gender: e.detail.value\n    })\n  },"}