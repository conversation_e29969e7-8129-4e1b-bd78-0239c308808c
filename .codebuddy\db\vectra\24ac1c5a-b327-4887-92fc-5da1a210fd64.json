{"chunk": 3, "numChunks": 7, "fileHash": "PQSUOKQzQ1nTJojwaszEOU93ZW43HSbpVKnnb8k/pfY=", "filePath": "pages/marriage/index.wxss", "content": "  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);\n}\n\n.submit-btn.disabled {\n  background: #e8e8e8;\n  color: #bbb;\n  box-shadow: none;\n}\n\n.submit-btn.active:active {\n  transform: translateY(2rpx);\n  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);\n}\n\n.btn-text {\n  font-size: 36rpx;\n}\n\n.btn-icon {\n  font-size: 32rpx;\n}\n\n/* 重新测算按钮 */\n.reset-section {\n  margin-bottom: 24rpx;\n}\n\n.reset-btn {\n  width: 100%;\n  height: 80rpx;\n  border-radius: 20rpx;\n  font-size: 32rpx;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8rpx;\n  background: linear-gradient(135deg, #ff7043 0%, #ff8a65 100%);\n  color: white;\n  box-shadow: 0 4rpx 16rpx rgba(255, 112, 67, 0.3);\n  border: none;\n  transition: all 0.3s ease;\n}\n\n.reset-btn:active {\n  transform: translateY(2rpx);\n  box-shadow: 0 2rpx 8rpx rgba(255, 112, 67, 0.4);\n}\n\n/* 分析类型选择卡片 */\n.analysis-tabs-card {\n  background: white;\n  border-radius: 24rpx;\n  padding: 24rpx;\n  margin-bottom: 24rpx;\n"}