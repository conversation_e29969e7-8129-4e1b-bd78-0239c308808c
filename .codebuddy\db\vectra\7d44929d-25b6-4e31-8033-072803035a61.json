{"chunk": 0, "numChunks": 3, "fileHash": "uGRdgU84yQtnSxCsNHminVUo8YlmVO1T09FtgqT2HJ4=", "filePath": "pages/feedback/feedback.wxml", "content": "<!--问题反馈页面-->\n<view class=\"page-container\">\n  <view class=\"header\">\n    <view class=\"title\">意见反馈</view>\n    <view class=\"subtitle\">您的建议是我们前进的动力</view>\n  </view>\n\n  <form bindsubmit=\"submitFeedback\">\n    <!-- 反馈类型 -->\n    <view class=\"form-section\">\n      <view class=\"section-title\">反馈类型</view>\n      <view class=\"type-selector\">\n        <view \n          class=\"type-item {{feedbackType === item.value ? 'active' : ''}}\"\n          wx:for=\"{{feedbackTypes}}\"\n          wx:key=\"value\"\n          bindtap=\"selectFeedbackType\"\n          data-type=\"{{item.value}}\"\n        >\n          <view class=\"type-icon\">{{item.icon}}</view>\n          <text class=\"type-text\">{{item.label}}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 问题描述 -->\n    <view class=\"form-section\">\n      <view class=\"section-title\">\n        <text>问题描述</text>\n        <text class=\"required\">*</text>\n      </view>\n      <textarea\n        class=\"feedback-textarea\"\n        placeholder=\"请详细描述您遇到的问题或建议，我们会认真处理...\"\n        value=\"{{feedbackContent}}\"\n        bindinput=\"onContentInput\"\n        maxlength=\"500\"\n        show-confirm-bar=\"{{false}}\"\n      />\n      <view class=\"char-count\">{{feedbackContent.length}}/500</view>\n    </view>\n\n    <!-- 联系方式 -->\n    <view class=\"form-section\">\n      <view class=\"section-title\">联系方式（选填）</view>\n      <view class=\"contact-inputs\">\n        <view class=\"input-group\">\n          <view class=\"input-label\">微信号/手机号</view>\n          <input\n"}