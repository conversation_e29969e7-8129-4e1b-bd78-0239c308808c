{"chunk": 1, "numChunks": 2, "fileHash": "rGYNB01cIDbBthG6i33zJhi+Gbc0TXNToORhXNRBRV4=", "filePath": "pages/settings/index.wxss", "content": "  border-bottom: 4rpx solid #999;\n  transform: translateY(-50%) rotate(-45deg);\n}\n\n/* 缓存大小 */\n.cache-size {\n  font-size: 28rpx;\n  color: #999;\n}\n\n/* 清除缓存按钮 */\n.clear-btn {\n  width: 100%;\n  height: 88rpx;\n  line-height: 88rpx;\n  text-align: center;\n  font-size: 28rpx;\n  color: #ff4d4f;\n  background-color: #fff;\n  border: none;\n  border-radius: 0;\n  margin: 20rpx 0;\n}\n\n.clear-btn::after {\n  display: none;\n}\n\n/* 箭头图标 */\n.arrow-icon {\n  width: 32rpx;\n  height: 32rpx;\n  opacity: 0.3;\n}\n\n/* 深色模式样式 */\n@media (prefers-color-scheme: dark) {\n  .container {\n    background-color: #1f1f1f;\n  }\n\n  .section {\n    background-color: #2c2c2c;\n  }\n\n  .section-title {\n    color: #fff;\n    border-bottom-color: #3d3d3d;\n  }\n\n  .setting-item {\n    border-bottom-color: #3d3d3d;\n  }\n\n  .setting-name {\n    color: #fff;\n  }\n\n  .picker-text {\n    color: #999;\n  }\n\n  .clear-btn {\n    background-color: #2c2c2c;\n    color: #ff4d4f;\n  }\n} \n"}