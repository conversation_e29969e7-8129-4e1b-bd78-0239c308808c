{"chunk": 3, "numChunks": 6, "fileHash": "3s34je0qL5+ULluXZdiBSih2mwyLkPtuCPPtZ9V/C7o=", "filePath": "pages/community/community.wxml", "content": "              <text>收藏</text>\n            </view>\n            <view class=\"action-btn\" bindtap=\"sharePost\" data-id=\"{{item.id}}\" catchtap=\"true\">\n              <icon type=\"info_circle\" size=\"16\"></icon>\n              <text>分享</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </block>\n    \n    <!-- 加载更多 -->\n    <view class=\"loading-more\" wx:if=\"{{isLoading}}\">\n      <view class=\"loading-icon\"></view>\n      <text>加载中...</text>\n    </view>\n    \n    <!-- 没有更多数据 -->\n    <view class=\"no-more\" wx:if=\"{{!hasMore && posts && posts.length > 0}}\">\n      <text>没有更多内容了</text>\n    </view>\n    \n    <!-- 空状态 -->\n    <view class=\"empty-state\" wx:if=\"{{!isLoading && (!posts || posts.length === 0)}}\">\n      <image src=\"/assets/icons/community/empty.png\" mode=\"aspectFit\"></image>\n      <text>暂无相关帖子</text>\n      <button class=\"empty-action-btn\" bindtap=\"showPublishModal\">发布第一篇帖子</button>\n    </view>\n  </view>\n\n  <!-- 发布按钮 -->\n  <view class=\"publish-btn\" bindtap=\"showPublishModal\">\n    <image src=\"/assets/icons/community/publish.png\"></image>\n  </view>\n\n  <!-- 发布弹窗 -->\n  <view class=\"modal\" wx:if=\"{{showPublishModal}}\">\n    <view class=\"modal-content\">\n      <view class=\"modal-header\">\n        <text>发布帖子</text>\n        <image class=\"close-icon\" src=\"/assets/icons/community/close.png\" bindtap=\"hidePublishModal\"></image>\n      </view>\n"}