{"chunk": 3, "numChunks": 5, "fileHash": "xeaCZ1EVBwibSEkFupn/pXs0/tot8OOYCLYhDZrm9J0=", "filePath": "pages/bazi/bazi.js", "content": "const BaziCalculator = require('../../utils/bazi.js');\nPage({\n  data: {\n    birthInfo: null,\n    baziResult: null,\n    loading: false,\n    error: null\n  },\n  onLoad: function() {\n    // 检查是否有出生信息\n    if (!checkBirthInfo()) {\n      navigateToBirthInfo('/pages/bazi/bazi');\n      return;\n    }\n    this.loadBirthInfo();\n  },\n  onShow: function() {\n    this.loadBirthInfo();\n  },\n  // 加载出生信息\n  loadBirthInfo: function() {\n    const birthInfo = wx.getStorageSync('birthInfo');\n    if (birthInfo) {\n      this.setData({ birthInfo });\n      this.calculateBazi(birthInfo);\n    }\n  },\n  // 计算八字"}