{"chunk": 0, "numChunks": 2, "fileHash": "AH8/+PTdZD/LysWntz/OyypYgyPuRjgoNyu7QMMSnnY=", "filePath": "pages/yijing/yijing.wxml", "content": "<!--yijing.wxml-->\n<view class=\"container\">\n  <!-- 使用出生信息组件 -->\n  <birth-info bind:save=\"onBirthInfoSave\"></birth-info>\n  \n  <view class=\"card\">\n    <view class=\"input-section\">\n      <!-- 显示出生信息 -->\n      <view class=\"birth-info\" wx:if=\"{{birthInfo}}\">\n        <view class=\"info-title\">出生信息</view>\n        <view class=\"info-content\">\n          <text class=\"info-item\">姓名：{{birthInfo.name}}</text>\n          <text class=\"info-item\">性别：{{birthInfo.gender}}</text>\n          <text class=\"info-item\">出生日期：{{birthInfo.birthDate}}</text>\n          <text class=\"info-item\">出生时间：{{birthInfo.birthTime}}</text>\n        </view>\n      </view>\n\n      <view class=\"input-group\">\n        <text class=\"label\">问题描述</text>\n        <textarea class=\"input\" placeholder=\"请输入您想问的问题\" bindinput=\"onQuestionInput\" value=\"{{question}}\"></textarea>\n      </view>\n      \n      <button class=\"submit-btn\" bindtap=\"onSubmit\" loading=\"{{loading}}\">开始占卜</button>\n    </view>\n  </view>\n\n  <view class=\"result-section\" wx:if=\"{{result}}\">\n    <view class=\"section-title\">卦象结果</view>\n    \n    <view class=\"hexagram\">\n      <view class=\"hexagram-name\">{{result.name}}</view>\n      <view class=\"hexagram-lines\">\n        <view class=\"line {{line === 1 ? 'yang' : 'yin'}}\" wx:for=\"{{result.lines}}\" wx:key=\"index\" wx:for-item=\"line\"></view>\n      </view>\n    </view>\n    \n    <view class=\"interpretation\">\n"}