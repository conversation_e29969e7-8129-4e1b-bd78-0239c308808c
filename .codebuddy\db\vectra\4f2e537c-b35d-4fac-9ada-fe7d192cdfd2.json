{"chunk": 0, "numChunks": 2, "fileHash": "uvJr2v7VjlYvc/uubWutyFkp31KM9o5uVQoAqVIwcF8=", "filePath": "pages/bazi/index.wxml", "content": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<view class=\"container\">\n  <view class=\"header\">\n    <text class=\"title\">八字分析</text>\n    <text class=\"subtitle\">精准解读人生命理</text>\n  </view>\n\n  <view class=\"birth-info\">\n    <view class=\"form-item\">\n      <text class=\"label\">出生日期</text>\n      <picker mode=\"date\" value=\"{{birthDate}}\" bindchange=\"onBirthDateChange\">\n        <view class=\"picker\">{{birthDate || '请选择出生日期'}}</view>\n      </picker>\n    </view>\n    \n    <view class=\"form-item\">\n      <text class=\"label\">出生时间</text>\n      <picker mode=\"time\" value=\"{{birthTime}}\" bindchange=\"onBirthTimeChange\">\n        <view class=\"picker\">{{birthTime || '请选择出生时间'}}</view>\n      </picker>\n    </view>\n\n    <view class=\"form-item\">\n      <text class=\"label\">性别</text>\n      <radio-group bindchange=\"onGenderChange\">\n        <label class=\"radio\">\n          <radio value=\"male\" checked=\"{{gender === 'male'}}\"/>男\n        </label>\n        <label class=\"radio\">\n          <radio value=\"female\" checked=\"{{gender === 'female'}}\"/>女\n        </label>\n      </radio-group>\n    </view>\n  </view>\n\n  <button class=\"analyze-btn\" bindtap=\"analyzeBazi\" loading=\"{{loading}}\">\n    开始分析\n  </button>\n\n  <view class=\"result-section\" wx:if=\"{{showResult}}\">\n    <view class=\"result-card\">\n      <view class=\"card-title\">八字命盘</view>\n      <view class=\"bazi-grid\">\n"}