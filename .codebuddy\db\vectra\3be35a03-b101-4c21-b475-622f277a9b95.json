{"chunk": 1, "numChunks": 3, "fileHash": "uGRdgU84yQtnSxCsNHminVUo8YlmVO1T09FtgqT2HJ4=", "filePath": "pages/feedback/feedback.wxml", "content": "            class=\"contact-input\"\n            placeholder=\"方便我们联系您\"\n            value=\"{{contactInfo}}\"\n            bindinput=\"onContactInput\"\n          />\n        </view>\n      </view>\n    </view>\n\n    <!-- 图片上传 -->\n    <view class=\"form-section\">\n      <view class=\"section-title\">\n        <text>截图上传</text>\n        <text class=\"section-desc\">（最多3张）</text>\n      </view>\n      <view class=\"image-upload\">\n        <view class=\"image-list\">\n          <view \n            class=\"image-item\"\n            wx:for=\"{{uploadedImages}}\"\n            wx:key=\"index\"\n          >\n            <image \n              class=\"uploaded-image\"\n              src=\"{{item}}\"\n              mode=\"aspectFill\"\n              bindtap=\"previewImage\"\n              data-url=\"{{item}}\"\n            />\n            <view \n              class=\"image-delete\"\n              bindtap=\"deleteImage\"\n              data-index=\"{{index}}\"\n            >×</view>\n          </view>\n          <view \n            class=\"image-upload-btn\"\n            wx:if=\"{{uploadedImages.length < 3}}\"\n            bindtap=\"chooseImage\"\n          >\n            <view class=\"upload-icon\">📷</view>\n            <text class=\"upload-text\">添加图片</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 历史反馈 -->\n    <view class=\"form-section\" wx:if=\"{{historyFeedbacks.length > 0}}\">\n      <view class=\"section-title\">\n        <text>历史反馈</text>\n        <text class=\"section-more\" bindtap=\"viewAllHistory\">查看全部</text>\n      </view>\n      <view class=\"history-list\">\n        <view \n          class=\"history-item\"\n          wx:for=\"{{historyFeedbacks}}\"\n          wx:key=\"id\"\n"}