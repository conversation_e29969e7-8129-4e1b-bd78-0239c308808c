{"chunk": 8, "numChunks": 14, "fileHash": "3g+f4UKfyPQOeFOGstuRr+TvNVx6NqSI5ZfRnXdVunU=", "filePath": "pages/profile/profile.js", "content": "// pages/profile/profile.js\nPage({\n  navigateTo(e) {\n    const url = e.currentTarget.dataset.url\n    const requireLogin = e.currentTarget.dataset.requireLogin\n\n    // 检查是否需要登录\n    if (requireLogin && !globalState.isUserLoggedIn()) {\n      wx.showModal({\n        title: '需要登录',\n        content: '该功能需要登录后才能使用，是否前往登录？',\n        confirmText: '去登录',\n        cancelText: '取消',\n        success: (res) => {\n          if (res.confirm) {\n            this.login()\n          }\n        }\n      })\n      return\n    }\n\n    // 使用统一导航管理器\n    navigationManager.navigateTo(url)\n  },\n  // 菜单项点击\n  onMenuItemTap(e) {\n    const { url, id } = e.currentTarget.dataset\n\n    // 特殊处理某些菜单项\n    switch (id) {\n      case 'sign-in':\n        // 如果已签到，显示签到状态\n        if (this.data.signInStatus) {\n          wx.showToast({\n            title: '今日已签到',\n            icon: 'success'\n          })\n          return\n        }\n        break\n      case 'birth-info':\n        // 出生信息页面不需要特殊检查\n        break\n    }\n\n    navigationManager.navigateTo(url)\n  },\n  // 退出登录"}