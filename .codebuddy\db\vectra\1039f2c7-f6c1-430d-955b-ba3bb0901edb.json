{"chunk": 3, "numChunks": 8, "fileHash": "+ydGTiHYiZCNR2Gpo16W6yPFn4Qk+H6hNDjgo1ILGOU=", "filePath": "pages/xingming/index.js", "content": "const app = getApp()\nPage({\n  data: {\n    // 基本信息\n    surname: '',\n    givenName: '',\n    gender: '',\n    birthDate: '',\n    birthTime: '',\n    canSubmit: false,\n    price: 28,\n\n    // 结果展示\n    showResult: false,\n    totalScore: 0,\n    scoreDesc: '',\n    wugeList: [],\n    sancaiAnalysis: '',\n    baziInfo: {\n      year: '',\n      month: '',\n      day: '',\n      time: ''\n    },\n    baziAnalysis: '',\n    jixiongList: [],\n    adviceList: []\n  },\n  onLoad() {\n    // 初始化三才图表\n    this.sancaiChart = new SancaiChart()\n  },\n  // 姓氏输入\n  onSurnameInput(e) {\n    this.setData({\n      surname: e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 名字输入\n  onGivenNameInput(e) {\n    this.setData({\n      givenName: e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 性别选择\n  onGenderSelect(e) {\n    const { gender } = e.currentTarget.dataset\n    this.setData({ gender })\n    this.checkCanSubmit()\n  },\n  // 出生日期选择\n  onBirthDateChange(e) {\n    this.setData({\n      birthDate: e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 出生时间选择\n  onBirthTimeChange(e) {\n    this.setData({\n      birthTime: e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 检查是否可以提交"}