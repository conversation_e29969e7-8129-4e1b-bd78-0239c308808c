{"chunk": 2, "numChunks": 9, "fileHash": "u79DhtWBcqv2jyfo/kouN6jRcunleYVqTQDoY+P3gFo=", "filePath": "pages/bazi/bazi.wxss", "content": "  text-shadow: 0 2rpx 8rpx rgba(149, 117, 205, 0.2);\n}\n\n.hidden-stem {\n  font-size: 24rpx;\n  color: var(--text-secondary);\n  margin-top: 8rpx;\n}\n\n.nayin {\n  font-size: 24rpx;\n  color: var(--text-light);\n  margin-top: 4rpx;\n}\n\n/* 五行分析样式 */\n.wuxing-analysis {\n  background: white;\n  border-radius: 25rpx;\n  padding: 30rpx;\n  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.1);\n  margin-bottom: 30rpx;\n}\n\n.wuxing-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 25rpx;\n}\n\n.wuxing-item:last-child {\n  margin-bottom: 0;\n}\n\n.wuxing-label {\n  width: 80rpx;\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n.wuxing-bar {\n  flex: 1;\n  height: 20rpx;\n  background: #f5f0ff;\n  border-radius: 10rpx;\n  margin: 0 20rpx;\n  overflow: hidden;\n}\n\n.wuxing-progress {\n  height: 100%;\n  background: linear-gradient(90deg, #b39ddb 0%, #9575cd 100%);\n  border-radius: 10rpx;\n  transition: width 0.6s ease;\n}\n\n.wuxing-value {\n  width: 80rpx;\n  text-align: right;\n  font-size: 26rpx;\n  color: #666;\n}\n\n/* 十神格局样式 */\n"}