{"chunk": 4, "numChunks": 5, "fileHash": "l8atZPeX8jkot2mARj8XbI3v0y6T6d3LZJDLwbL7UTg=", "filePath": "pages/settings/index.js", "content": "const app = getApp()\nPage({\n  saveSettings() {\n    try {\n      const settings = {\n        general: this.data.generalSettings,\n        privacy: this.data.privacySettings,\n        notification: this.data.notificationSettings\n      }\n      wx.setStorageSync('appSettings', settings)\n      wx.showToast({\n        title: '设置已保存',\n        icon: 'success'\n      })\n    } catch (err) {\n      console.error('保存设置失败:', err)\n      wx.showToast({\n        title: '保存失败',\n        icon: 'none'\n      })\n    }\n  },\n  // 切换开关设置\n  toggleSetting(e) {\n    const { type, key } = e.currentTarget.dataset\n    const settingKey = `${type}Settings.${key}`\n    this.setData({\n      [settingKey]: !this.data.generalSettings[key]\n    }, () => {\n      this.saveSettings()\n    })\n  },\n  // 选择语言\n  changeLanguage(e) {\n    const language = e.detail.value\n    this.setData({\n      'generalSettings.language': language\n    }, () => {\n      this.saveSettings()\n      // 重启小程序以应用语言设置\n      wx.showModal({\n        title: '提示',\n        content: '切换语言需要重启小程序，是否立即重启？',\n        success: (res) => {\n          if (res.confirm) {\n            wx.reLaunch({\n              url: '/pages/index/index'\n            })\n          }\n        }\n      })\n    })\n  },\n  // 选择字体大小\n  changeFontSize(e) {\n    const fontSize = e.detail.value\n    this.setData({\n      'generalSettings.fontSize': fontSize\n    }, () => {\n      this.saveSettings()\n      // 应用字体大小设置\n      wx.setStorageSync('fontSize', fontSize)\n    })\n  },\n  // 计算缓存大小"}