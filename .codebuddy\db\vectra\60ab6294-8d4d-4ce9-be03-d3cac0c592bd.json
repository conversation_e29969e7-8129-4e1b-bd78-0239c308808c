{"chunk": 3, "numChunks": 12, "fileHash": "5X5f8yfnxquoBxYzBGhnUPxPUBZeZKQywMAReox6cBk=", "filePath": "pages/bazi/index.js", "content": "const app = getApp()\nPage({\n  data: {\n    // 基本信息\n    name: '',\n    gender: 'male',\n    birthDate: '',\n    birthTime: '',\n    isLunar: false,\n    canSubmit: false,\n    price: 28,\n\n    // 结果展示\n    showResult: false,\n    currentTab: 'yearly',\n    \n    // 分析结果数据\n    wuxingAnalysis: [\n      { name: '金', percent: 0, value: 0, color: '#FFD700' },\n      { name: '木', percent: 0, value: 0, color: '#90EE90' },\n      { name: '水', percent: 0, value: 0, color: '#87CEEB' },\n      { name: '火', percent: 0, value: 0, color: '#FF6B6B' },\n      { name: '土', percent: 0, value: 0, color: '#DEB887' }\n    ],\n    minggeAnalysis: '',\n    yunshiDetails: '',\n    jixiongAnalysis: [],\n    adviceList: [],\n    loading: false,\n    baziResult: {\n      pillars: [],\n      wuxing: [],\n      interpretation: '',\n      fortune: []\n    }\n  },\n  onLoad() {\n    // 初始化图表\n    this.initCharts()\n  },\n  // 输入处理函数\n  onNameInput(e) {\n    this.setData({ name: e.detail.value })\n    this.checkCanSubmit()\n  },\n  onGenderSelect(e) {\n    this.setData({ gender: e.currentTarget.dataset.gender })\n    this.checkCanSubmit()\n  },\n  onDateChange(e) {\n    this.setData({ birthDate: e.detail.value })\n    this.checkCanSubmit()\n  },"}