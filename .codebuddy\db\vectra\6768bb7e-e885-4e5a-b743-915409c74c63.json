{"chunk": 4, "numChunks": 5, "fileHash": "SwGl0Osw+Il/Fk4wMhJRhmGve8PVm+UDhwwtYP7bA2g=", "filePath": "pages/community/index.js", "content": "const app = getApp()\nPage({\n  async loadPosts() {\n    if (this.data.loading || !this.data.hasMore) return\n\n    this.setData({ loading: true })\n\n    try {\n      const res = await wx.cloud.callFunction({\n        name: 'getPosts',\n        data: {\n          category: this.data.currentCategory,\n          page: this.data.page,\n          pageSize: this.data.pageSize\n        }\n      })\n\n      const { posts, hasMore } = res.result\n      \n      this.setData({\n        posts: [...this.data.posts, ...posts],\n        hasMore,\n        page: this.data.page + 1,\n        loading: false\n      })\n    } catch (err) {\n      console.error('加载帖子失败:', err)\n      this.setData({ loading: false })\n      wx.showToast({\n        title: '加载失败',\n        icon: 'none'\n      })\n    }\n  },\n  // 点赞\n  async handleLike(e) {\n    const { postId, index } = e.currentTarget.dataset\n    \n    try {\n      await wx.cloud.callFunction({\n        name: 'likePost',\n        data: { postId }\n      })\n\n      // 更新本地点赞状态\n      const posts = [...this.data.posts]\n      posts[index].liked = !posts[index].liked\n      posts[index].likes = posts[index].liked ? \n        posts[index].likes + 1 : posts[index].likes - 1\n\n      this.setData({ posts })\n    } catch (err) {\n      console.error('点赞失败:', err)\n      wx.showToast({\n        title: '操作失败',\n        icon: 'none'\n      })\n    }\n  },\n  // 评论\n  handleComment(e) {\n    const { postId } = e.currentTarget.dataset\n    wx.navigateTo({\n      url: `/pages/post-detail/index?id=${postId}`\n    })\n  },\n  // 分享"}