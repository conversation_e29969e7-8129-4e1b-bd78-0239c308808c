{"chunk": 1, "numChunks": 9, "fileHash": "u79DhtWBcqv2jyfo/kouN6jRcunleYVqTQDoY+P3gFo=", "filePath": "pages/bazi/bazi.wxss", "content": "  margin-right: 30rpx;\n  font-size: 28rpx;\n  color: var(--text-primary);\n}\n\n.calculate-btn {\n  margin-top: 30rpx;\n  background-color: var(--primary-color);\n  color: #FFFFFF;\n  font-size: 32rpx;\n  border-radius: 8rpx;\n  box-shadow: 0 4rpx 8rpx var(--shadow-color);\n}\n\n/* 结果区域样式 */\n.result-section {\n  background-color: var(--card-background);\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 4rpx 12rpx var(--shadow-color);\n  border: 2rpx solid var(--border-color);\n}\n\n/* 八字图表样式 */\n.bazi-chart {\n  background: white;\n  border-radius: 25rpx;\n  padding: 30rpx;\n  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.1);\n  margin-bottom: 30rpx;\n}\n\n.pillar-row {\n  display: flex;\n  justify-content: space-around;\n}\n\n.pillar {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  flex: 1;\n}\n\n.pillar-label {\n  font-size: 26rpx;\n  color: #999;\n  margin-bottom: 15rpx;\n}\n\n.pillar-content {\n  font-size: 48rpx;\n  font-weight: bold;\n  background: linear-gradient(135deg, #9575cd 0%, #7e57c2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n"}