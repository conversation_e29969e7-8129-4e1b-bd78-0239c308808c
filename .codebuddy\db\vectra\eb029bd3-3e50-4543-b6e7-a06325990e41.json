{"chunk": 0, "numChunks": 2, "fileHash": "QKSL5n0UDVTG6X2sgWRJT9hKu5diilVhDJOLe9HL7j4=", "filePath": "pages/yijing/index.wxss", "content": ".container {\n  padding: 30rpx;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 40rpx;\n}\n\n.title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.subtitle {\n  font-size: 28rpx;\n  color: #666;\n  display: block;\n}\n\n.question-section {\n  background: #fff;\n  padding: 30rpx;\n  border-radius: 16rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n  margin-bottom: 30rpx;\n}\n\n.question-input {\n  width: 100%;\n  height: 200rpx;\n  padding: 20rpx;\n  box-sizing: border-box;\n  border: 2rpx solid #e0e0e0;\n  border-radius: 8rpx;\n  font-size: 28rpx;\n  margin-bottom: 20rpx;\n}\n\n.divine-button {\n  width: 100%;\n  height: 88rpx;\n  line-height: 88rpx;\n  background: #4a5568;\n  color: #fff;\n  font-size: 32rpx;\n  border-radius: 44rpx;\n  transition: all 0.3s ease;\n}\n\n.divine-button.loading {\n  background: #718096;\n  opacity: 0.8;\n}\n\n.result-section {\n  margin-top: 40rpx;\n}\n\n.hexagram-card {\n  background: #fff;\n  border-radius: 16rpx;\n  padding: 30rpx;\n"}