{"chunk": 1, "numChunks": 6, "fileHash": "3s34je0qL5+ULluXZdiBSih2mwyLkPtuCPPtZ9V/C7o=", "filePath": "pages/community/community.wxml", "content": "        bindtap=\"switchCategory\"\n      >\n        {{item.name}}\n      </view>\n    </scroll-view>\n    \n    <!-- 排序选项 -->\n    <view class=\"sort-options\">\n      <picker \n        bindchange=\"onSortChange\" \n        value=\"{{sortIndex}}\" \n        range=\"{{sortOptions}}\" \n        range-key=\"name\"\n      >\n        <view class=\"sort-picker\">\n          <text>{{sortOptions[sortIndex].name}}</text>\n          <icon type=\"clear\" size=\"12\" style=\"transform: rotate(90deg);\"></icon>\n        </view>\n      </picker>\n    </view>\n  </view>\n\n  <!-- 帖子列表 -->\n  <view class=\"post-list\">\n    <block wx:if=\"{{posts && posts.length > 0}}\">\n      <view class=\"post-item hover-card\" wx:for=\"{{posts}}\" wx:key=\"id\" bindtap=\"viewPost\" data-id=\"{{item.id}}\">\n        <view class=\"post-header\">\n          <image class=\"avatar\" src=\"{{item.avatar || '/assets/images/default-avatar.png'}}\" mode=\"aspectFill\"></image>\n          <view class=\"post-info\">\n            <view class=\"username\">{{item.username || '匿名用户'}}</view>\n            <view class=\"post-meta\">\n              <text class=\"category\">{{item.categoryName}}</text>\n              <text class=\"time\">{{item.createTime || ''}}</text>\n              <view class=\"hot-badge\" wx:if=\"{{item.isHot}}\">🔥</view>\n            </view>\n          </view>\n          <view class=\"follow-btn\" wx:if=\"{{!item.isFollowing && item.userId !== userInfo.id}}\" bindtap=\"followUser\" data-id=\"{{item.userId}}\" catchtap=\"true\">\n            关注\n          </view>\n        </view>\n        <view class=\"post-content\">\n"}