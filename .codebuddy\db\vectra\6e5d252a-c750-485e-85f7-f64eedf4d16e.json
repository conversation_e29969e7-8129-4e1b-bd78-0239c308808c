{"chunk": 14, "numChunks": 15, "fileHash": "O/nJs1XsxYT9PDo4XFlMEnsiqZRG/Zu4Jl5KoBF5Ci8=", "filePath": "pages/index/index.wxml", "content": "            <text class=\"section-more\" bindtap=\"goToArticles\">查看更多</text>\n          </view>\n        <view class=\"article-list\">\n            <view class=\"article-item\" wx:for=\"{{articles}}\" wx:key=\"id\" bindtap=\"onArticleTap\" data-id=\"{{item.id}}\">\n              <image class=\"article-image\" src=\"{{item.coverUrl}}\" mode=\"aspectFill\" />\n            <view class=\"article-info\">\n              <text class=\"article-title\">{{item.title}}</text>\n              <view class=\"article-meta\">\n                <text class=\"article-author\">{{item.author}}</text>\n                  <text class=\"article-views\">{{item.viewCount}}阅读</text>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n  </scroll-view>\n</view>\n"}