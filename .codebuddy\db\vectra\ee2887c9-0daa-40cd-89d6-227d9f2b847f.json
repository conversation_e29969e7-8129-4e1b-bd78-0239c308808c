{"chunk": 5, "numChunks": 7, "fileHash": "PQSUOKQzQ1nTJojwaszEOU93ZW43HSbpVKnnb8k/pfY=", "filePath": "pages/marriage/index.wxss", "content": "  content: '';\n  position: absolute;\n  width: 180rpx;\n  height: 180rpx;\n  background: white;\n  border-radius: 50%;\n  box-shadow: inset 0 4rpx 12rpx rgba(102, 126, 234, 0.1);\n}\n\n.score-value {\n  position: relative;\n  z-index: 2;\n  font-size: 56rpx;\n  font-weight: bold;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  margin-bottom: 8rpx;\n}\n\n.score-desc {\n  position: relative;\n  z-index: 2;\n  font-size: 24rpx;\n  color: #666;\n  font-weight: 500;\n}\n\n.score-detail {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n  text-align: center;\n  padding: 24rpx;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e8e8e8 100%);\n  border-radius: 16rpx;\n  margin-top: 16rpx;\n}\n\n/* 分析详情 */\n.analysis-detail {\n  margin-top: 16rpx;\n}\n\n.detail-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24rpx;\n  padding-bottom: 16rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.detail-score {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.detail-level {\n"}