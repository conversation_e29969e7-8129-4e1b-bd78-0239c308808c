{"chunk": 1, "numChunks": 4, "fileHash": "AzClV7baUsatVdHoLC/V2zxViVlpnMYViBcaI7+/JbU=", "filePath": "pages/customer-service/index.wxss", "content": "  flex-direction: row-reverse;\n}\n\n.avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 40rpx;\n  margin: 0 20rpx;\n}\n\n.message-content {\n  max-width: 60%;\n}\n\n.message-item.service .message-content {\n  margin-right: 100rpx;\n}\n\n.message-item.user .message-content {\n  margin-left: 100rpx;\n  align-items: flex-end;\n}\n\n.name {\n  font-size: 24rpx;\n  color: #999;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.content {\n  background-color: #fff;\n  padding: 20rpx;\n  border-radius: 12rpx;\n  font-size: 28rpx;\n  color: #333;\n  word-break: break-all;\n}\n\n.message-item.user .content {\n  background-color: #07c160;\n  color: #fff;\n}\n\n.content-image {\n  max-width: 400rpx;\n  border-radius: 12rpx;\n}\n\n.time {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 8rpx;\n  display: block;\n}\n\n/* 正在输入提示 */\n.typing-indicator {\n  display: flex;\n  align-items: center;\n  padding: 20rpx;\n  gap: 8rpx;\n}\n\n.typing-dot {\n  width: 12rpx;\n  height: 12rpx;\n  background-color: #999;\n  border-radius: 50%;\n  animation: typing 1s infinite;\n}\n\n.typing-dot:nth-child(2) {\n  animation-delay: 0.2s;\n}\n\n.typing-dot:nth-child(3) {\n  animation-delay: 0.4s;\n}\n\n@keyframes typing {\n  0%, 60%, 100% {\n"}