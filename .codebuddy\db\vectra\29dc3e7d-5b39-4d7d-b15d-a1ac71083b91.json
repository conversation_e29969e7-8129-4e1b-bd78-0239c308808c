{"chunk": 1, "numChunks": 5, "fileHash": "D8EtU/lQxzCzbNO7f7/rdYUVcSFrgRLNb6jQJPgoQwc=", "filePath": "subpages/divination/bazi/bazi.wxml", "content": "      </view>\n\n      <view class=\"form-group\">\n        <text class=\"form-label\">出生日期</text>\n        <picker mode=\"date\" value=\"{{birthDate}}\" bindchange=\"onDateChange\">\n          <view class=\"picker-display\">\n            <text class=\"picker-value\">{{birthDate || '请选择出生日期'}}</text>\n            <text class=\"picker-arrow\">▼</text>\n          </view>\n        </picker>\n      </view>\n\n      <view class=\"form-group\">\n        <text class=\"form-label\">出生时间</text>\n        <picker mode=\"time\" value=\"{{birthTime}}\" bindchange=\"onTimeChange\">\n          <view class=\"picker-display\">\n            <text class=\"picker-value\">{{birthTime || '请选择出生时间'}}</text>\n            <text class=\"picker-arrow\">▼</text>\n          </view>\n        </picker>\n      </view>\n\n      <view class=\"form-group\">\n        <text class=\"form-label\">出生地点</text>\n        <input class=\"form-input\" placeholder=\"请输入出生地点（选填）\" bindinput=\"onLocationInput\" value=\"{{location}}\" />\n      </view>\n\n      <button class=\"analyze-btn\" bindtap=\"analyzeBazi\" loading=\"{{analyzing}}\" disabled=\"{{analyzing}}\">\n        {{analyzing ? '分析中...' : '开始分析'}}\n      </button>\n    </view>\n  </view>\n\n  <!-- 分析结果 -->\n  <view class=\"bazi-result\" wx:if=\"{{showResult}}\">\n    <view class=\"result-card\">\n      <view class=\"result-header\">\n        <text class=\"result-title\">八字命盘</text>\n        <text class=\"result-name\">{{resultData.name}} {{resultData.gender === 'male' ? '先生' : '女士'}}</text>\n      </view>\n\n"}