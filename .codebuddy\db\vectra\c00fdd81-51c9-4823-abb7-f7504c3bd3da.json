{"chunk": 11, "numChunks": 12, "fileHash": "HkVDVZ9giUC0NxXwGhnv0R1SvQ+r07w6mr3mR/NNt+s=", "filePath": "subpages/divination/bazi/bazi.js", "content": "Page({\n  },\n  /**\n   * 保存结果\n   */\n  saveResult() {\n    wx.showToast({\n      title: '保存成功',\n      icon: 'success'\n    })\n  },\n  /**\n   * 分享结果\n   */\n  shareResult() {\n    wx.showToast({\n      title: '请长按保存图片',\n      icon: 'none'\n    })\n  },\n  /**\n   * 保存到历史记录\n   */"}