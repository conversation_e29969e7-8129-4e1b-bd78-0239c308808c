{"chunk": 1, "numChunks": 6, "fileHash": "TRjhIujXrZdfOAnJdtSzbShY2tPK4Kr6sOiSYsXo4vg=", "filePath": "pages/daily-fortune/daily-fortune.wxml", "content": "    <view class=\"main-fortunes\">\n      <view class=\"fortune-item\" wx:for=\"{{dailyFortune.mainFortunes}}\" wx:key=\"type\">\n        <view class=\"fortune-header\">\n          <view class=\"fortune-icon\">{{item.icon}}</view>\n          <view class=\"fortune-info\">\n            <text class=\"fortune-name\">{{item.name}}</text>\n            <view class=\"fortune-rating\">\n              <view class=\"stars\">\n                <text \n                  class=\"star {{index < item.rating ? 'active' : ''}}\"\n                  wx:for=\"{{5}}\" \n                  wx:key=\"index\"\n                  wx:for-index=\"index\"\n                >★</text>\n              </view>\n              <text class=\"rating-text\">{{item.ratingText}}</text>\n            </view>\n          </view>\n          <view class=\"fortune-score\">{{item.score}}分</view>\n        </view>\n        <view class=\"fortune-content\">\n          <text class=\"fortune-desc\">{{item.description}}</text>\n        </view>\n        <view class=\"fortune-suggestion\" wx:if=\"{{item.suggestion}}\">\n          <text class=\"suggestion-label\">建议：</text>\n          <text class=\"suggestion-text\">{{item.suggestion}}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 幸运信息 -->\n    <view class=\"lucky-info\">\n      <view class=\"section-title\">今日幸运</view>\n      <view class=\"lucky-grid\">\n        <view class=\"lucky-item\">\n          <view class=\"lucky-label\">幸运数字</view>\n          <view class=\"lucky-value number\">{{dailyFortune.luckyNumber}}</view>\n        </view>\n        <view class=\"lucky-item\">\n          <view class=\"lucky-label\">幸运颜色</view>\n          <view class=\"lucky-value color\">\n"}