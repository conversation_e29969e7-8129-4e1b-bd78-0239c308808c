{"chunk": 4, "numChunks": 8, "fileHash": "pWWTyJooIxy6mFbGJzYtyes3pCjDoqcgXSX9h6jKjqM=", "filePath": "utils/bazi/calculator.js", "content": "// 天干\nclass BaziCalculator {\n  calculateHourBranch(date) {\n    const hour = date.getHours();\n    const offset = Math.floor((hour + 1) / 2) % 12;\n    return this.earthlyBranches[offset];\n  }\n  calculateWuxing(bazi) {\n    const wuxing = { ...WUXING };\n    \n    // 计算天干五行\n    [bazi.yearStem, bazi.monthStem, bazi.dayStem, bazi.hourStem].forEach(stem => {\n      wuxing[STEM_WUXING[stem]].value += 25;\n    });\n\n    // 计算地支五行\n    [bazi.yearBranch, bazi.monthBranch, bazi.dayBranch, bazi.hourBranch].forEach(branch => {\n      wuxing[BRANCH_WUXING[branch]].value += 25;\n    });\n\n    return {\n      metal: wuxing.metal.value,\n      wood: wuxing.wood.value,\n      water: wuxing.water.value,\n      fire: wuxing.fire.value,\n      earth: wuxing.earth.value\n    };\n  }"}