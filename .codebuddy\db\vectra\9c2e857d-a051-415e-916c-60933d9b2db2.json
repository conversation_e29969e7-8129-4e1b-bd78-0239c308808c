{"chunk": 46, "numChunks": 55, "fileHash": "fCZ6nFoNZXQqhvADUzMwGWD6jOU+ajj61B8BD3CwDgs=", "filePath": "pages/name-test/name-test.js", "content": "// 姓名测试页面逻辑\nPage({\n  calculateSancai(wuge) {\n    const wuxing = ['木', '火', '土', '金', '水'];\n    const tiange = wuge[0].number % 10;\n    const renge = wuge[1].number % 10;\n    const dige = wuge[2].number % 10;\n\n    const tian = wuxing[Math.floor((tiange - 1) / 2) % 5];\n    const ren = wuxing[Math.floor((renge - 1) / 2) % 5];\n    const di = wuxing[Math.floor((dige - 1) / 2) % 5];\n\n    return {\n      tian,\n      ren,\n      di,\n      analysis: this.getSancaiAnalysis(tian, ren, di)\n    };\n  },\n  // 获取数字等级"}