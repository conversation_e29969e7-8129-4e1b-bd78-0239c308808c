{"chunk": 0, "numChunks": 4, "fileHash": "JxYmunO+MuN/nw3IhbaSBWIwBZxgEQi9Pk/YphlGNzQ=", "filePath": "pages/post-detail/post-detail.wxml", "content": "<!--pages/post-detail/post-detail.wxml-->\n<view class=\"page-container\">\n  <!-- 加载组件 -->\n  <loading show=\"{{isLoading}}\" text=\"加载中...\" />\n  \n  <scroll-view class=\"content-scroll\" scroll-y wx:if=\"{{!isLoading}}\">\n    <!-- 文章头图 -->\n    <view class=\"article-header\" wx:if=\"{{article.coverImage}}\">\n      <image class=\"cover-image\" src=\"{{article.coverImage}}\" mode=\"aspectFill\" />\n      <view class=\"header-mask\"></view>\n      <view class=\"header-info\">\n        <text class=\"article-category\">{{article.category || '命理知识'}}</text>\n        <text class=\"article-title\">{{article.title}}</text>\n      </view>\n    </view>\n    \n    <!-- 作者信息 -->\n    <view class=\"author-section\">\n      <view class=\"author-info\">\n        <image class=\"author-avatar\" src=\"{{article.author.avatar || '/assets/images/default-avatar.png'}}\" />\n        <view class=\"author-details\">\n          <text class=\"author-name\">{{article.author.name}}</text>\n          <text class=\"author-desc\">{{article.author.description || '资深命理师'}}</text>\n        </view>\n      </view>\n      <view class=\"article-stats\">\n        <text class=\"stat-item\">{{article.readCount || 0}} 阅读</text>\n        <text class=\"stat-item\">{{article.publishTime}}</text>\n      </view>\n    </view>\n    \n    <!-- 文章内容 -->\n    <view class=\"article-content\">\n      <!-- 文章摘要 -->\n      <view class=\"article-summary\" wx:if=\"{{article.summary}}\">\n        <text>{{article.summary}}</text>\n      </view>\n      \n      <!-- 正文内容 -->\n      <view class=\"article-body\">\n        <rich-text nodes=\"{{article.content}}\" />\n      </view>\n      \n"}