{"chunk": 4, "numChunks": 11, "fileHash": "6cvegKFbV+qpODqHzHmmaj3L2MYUXg3Sv6AOEWns8A8=", "filePath": "frontend-implementation/mixins/basePage.js", "content": "// 页面基础混入\nexport const basePageMixin = {\n  data: {\n    // 基础状态\n    loading: false,\n    error: null,\n    \n    // 用户状态\n    isLogin: false,\n    userInfo: null,\n    \n    // 系统信息\n    systemInfo: null,\n    \n    // 网络状态\n    networkStatus: 'online'\n  },\n  onLoad(options) {\n    // 初始化页面状态\n    this.initPageState()\n    \n    // 订阅状态变化\n    this.subscribeStoreChanges()\n    \n    // 检查登录状态\n    this.checkLoginStatus()\n    \n    // 调用子页面的onLoad\n    if (this.pageOnLoad) {\n      this.pageOnLoad(options)\n    }\n  },\n  onShow() {\n    // 更新页面状态\n    this.updatePageState()\n    \n    // 调用子页面的onShow\n    if (this.pageOnShow) {\n      this.pageOnShow()\n    }\n  },\n  onHide() {\n    // 调用子页面的onHide\n    if (this.pageOnHide) {\n      this.pageOnHide()\n    }\n  },\n  onUnload() {\n    // 取消状态订阅\n    this.unsubscribeStoreChanges()\n    \n    // 调用子页面的onUnload\n    if (this.pageOnUnload) {\n      this.pageOnUnload()\n    }\n  },\n  /**\n   * 初始化页面状态\n   */\n  initPageState() {\n    const userState = store.getState('user')\n    const appState = store.getState('app')\n    \n    this.setData({\n      isLogin: userState.isLogin,\n      userInfo: userState.userInfo,\n      systemInfo: appState.systemInfo,\n      networkStatus: appState.networkStatus\n    })\n  },\n  /**\n   * 更新页面状态\n   */"}