{"chunk": 1, "numChunks": 3, "fileHash": "AnVYNl2DM83q7XUZ+V1o51We51jIzl4iPzBkuLj4U0g=", "filePath": "pages/divination/divination.wxss", "content": "  background-color: #fff;\n  padding: 30rpx;\n  border-radius: 10rpx;\n}\n\n/* 卦象展示样式 */\n.gua-display {\n  margin: 20rpx 0;\n  padding: 20rpx;\n  background-color: #f8f8f8;\n  border-radius: 6rpx;\n}\n\n.gua-title {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: bold;\n  text-align: center;\n  margin-bottom: 20rpx;\n}\n\n.gua-image {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 10rpx;\n}\n\n.yao-line {\n  width: 200rpx;\n  height: 20rpx;\n  display: flex;\n  justify-content: center;\n}\n\n.yao-content {\n  height: 100%;\n  background-color: #333;\n}\n\n.yao-content.yang {\n  width: 100%;\n}\n\n.yao-content.yin {\n  width: 45%;\n  margin: 0 2.5%;\n}\n\n/* 塔罗牌展示样式 */\n.tarot-display {\n  margin: 20rpx 0;\n}\n\n.card-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 20rpx;\n}\n\n.card-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.card-image {\n  width: 160rpx;\n  height: 280rpx;\n  border-radius: 10rpx;\n  margin-bottom: 10rpx;\n}\n\n.card-name {\n  font-size: 24rpx;\n  color: #333;\n}\n\n/* 解释区域样式 */\n.interpretation-section {\n  margin-top: 30rpx;\n}\n\n.interpretation-item {\n"}