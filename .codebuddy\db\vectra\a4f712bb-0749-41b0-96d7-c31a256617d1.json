{"chunk": 10, "numChunks": 16, "fileHash": "9ZF2KXbrmWumjo5LxJnE/F14ePFZLBzZ6HxD55jx3zE=", "filePath": "pages/fengshui/fengshui.js", "content": "// pages/fengshui/fengshui.js\nPage({\n  checkCanSubmit() {\n    const {\n      directionIndex,\n      houseTypeIndex,\n      buildYear,\n      area,\n      currentFloor,\n      totalFloor\n    } = this.data\n    \n    const canSubmit = \n      directionIndex !== null &&\n      houseTypeIndex !== null &&\n      buildYear &&\n      area &&\n      currentFloor &&\n      totalFloor &&\n      !isNaN(currentFloor) &&\n      !isNaN(totalFloor) &&\n      parseInt(currentFloor) <= parseInt(totalFloor)\n\n    this.setData({ canSubmit })\n    \n    // 如果数据有更新，保存到缓存\n    if (canSubmit) {\n      this.saveCachedData()\n    }\n  },\n  // 开始分析"}