{"chunk": 2, "numChunks": 6, "fileHash": "YXf85xx1jxmB9as0Va5OH90PaK79Xnt82sfEgjKj2bE=", "filePath": "pages/post-detail/post-detail.wxss", "content": "}\n\n.article-body rich-text {\n  display: block;\n}\n\n.article-tags {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 15rpx;\n  margin-bottom: 40rpx;\n}\n\n.tag-item {\n  padding: 10rpx 25rpx;\n  background: #f5f0ff;\n  color: #9575cd;\n  border-radius: 30rpx;\n  font-size: 26rpx;\n  border: 2rpx solid #e8dff5;\n  transition: all 0.3s ease;\n}\n\n/* 互动区域 */\n.interaction-section {\n  margin: 40rpx 30rpx;\n}\n\n.interaction-bar {\n  display: flex;\n  justify-content: space-around;\n  background: white;\n  padding: 30rpx;\n  border-radius: 25rpx;\n  box-shadow: 0 4rpx 12rpx rgba(149, 117, 205, 0.08);\n}\n\n.interaction-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 10rpx;\n  padding: 10rpx 30rpx;\n  border-radius: 20rpx;\n  transition: all 0.3s ease;\n}\n\n.interaction-item.active {\n  background: #f5f0ff;\n  color: #9575cd;\n}\n\n.interaction-item text:first-child {\n  font-size: 40rpx;\n}\n\n.interaction-item text:last-child {\n  font-size: 26rpx;\n  color: #666;\n}\n\n/* 相关推荐 */\n.related-section {\n  margin: 40rpx 30rpx;\n}\n\n.section-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 25rpx;\n"}