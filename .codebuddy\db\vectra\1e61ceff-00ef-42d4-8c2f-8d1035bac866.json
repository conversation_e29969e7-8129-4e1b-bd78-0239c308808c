{"chunk": 3, "numChunks": 15, "fileHash": "An9WOmGw20FYVB4+SrCnlnnpOBHN6cohdD4SvY7rXL8=", "filePath": "pages/ziwei/ziwei.js", "content": "// ziwei.js\nPage({\n  data: {\n    birthInfo: null,\n    loading: false,\n    error: '',\n    ziweiResult: null\n  },\n  onLoad() {\n    // 检查是否有出生信息\n    if (!checkBirthInfo()) {\n      navigateToBirthInfo('/pages/ziwei/ziwei')\n      return\n    }\n    \n    // 加载保存的出生信息\n    const birthInfo = getBirthInfo()\n    if (birthInfo && birthInfo.birthDate !== '未设置' && birthInfo.birthTime !== '未设置') {\n      this.setData({ \n        birthInfo: {\n          date: birthInfo.birthDate,\n          time: birthInfo.birthTime,\n          gender: birthInfo.gender\n        }\n      })\n      this.calculateZiwei({\n        date: birthInfo.birthDate,\n        time: birthInfo.birthTime,\n        gender: birthInfo.gender\n      })\n    }\n  },\n  onDateChange(e) {\n    this.setData({\n      birthDate: e.detail.value\n    })\n  },\n  onTimeChange(e) {\n    this.setData({\n      birthTime: e.detail.value\n    })\n  },\n  onGenderChange(e) {\n    this.setData({\n      gender: e.detail.value\n    })\n  },\n  /**\n   * 处理出生信息保存事件\n   */\n  onBirthInfoSave(e) {\n    try {\n      const { birthDate, birthTime, gender } = e.detail\n      this.setData({\n        birthDate,\n        birthTime,\n        gender\n      })\n      // 自动计算紫微斗数\n      this.calculateZiwei({ date: birthDate, time: birthTime, gender })\n    } catch (error) {\n      console.error('保存出生信息失败:', error)\n      wx.showToast({\n        title: '保存信息失败',\n        icon: 'none'\n      })\n    }\n  },"}