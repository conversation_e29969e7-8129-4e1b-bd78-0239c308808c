{"chunk": 8, "numChunks": 11, "fileHash": "i8jYYz/gAcuprKem1BBbiBaSat5ZFs+bjwiR2/ZQErE=", "filePath": "pages/birth-info/birth-info.js", "content": "const globalState = require('../../utils/global-state')\nPage({\n  saveInfo() {\n    const {\n      name,\n      gender,\n      zodiacIndex,\n      numberIndex,\n      zodiacList,\n      numberList,\n      selectedDateTime,\n      dateTime,\n      dateTimeArray,\n      canSubmit\n    } = this.data;\n    // 检查是否可以提交\n    if (!canSubmit) {\n      wx.showToast({\n        title: '请完善必填信息',\n        icon: 'none'\n      });\n      return;\n    }\n    if (!name.trim()) {\n      wx.showToast({\n        title: '请输入姓名',\n        icon: 'none'\n      });\n      return;\n    }\n    if (!selectedDateTime) {\n      wx.showToast({\n        title: '请选择出生日期时间',\n        icon: 'none'\n      });\n      return;\n    }"}