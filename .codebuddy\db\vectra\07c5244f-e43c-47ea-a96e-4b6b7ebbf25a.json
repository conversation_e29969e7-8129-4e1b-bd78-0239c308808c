{"chunk": 3, "numChunks": 8, "fileHash": "2gwHiceF78ljJhRnHSk/QhbjXumu/H9/2egtjP1psSk=", "filePath": "pages/fortune/fortune.js", "content": "const app = getApp()\nPage({\n  data: {\n    birthInfo: {\n      birthDate: '',\n      birthTime: '',\n      gender: ''\n    },\n    fortuneResult: null,\n    monthNodes: []\n  },\n  onLoad() {\n    // 页面加载时获取本地存储的出生信息\n    this.loadBirthInfo()\n  },\n  onShow() {\n    // 每次页面显示时重新获取出生信息，以便在用户修改后更新\n    this.loadBirthInfo()\n    \n    // 设置TabBar选中状态\n    try {\n      if (typeof this.getTabBar === 'function' && this.getTabBar()) {\n        this.getTabBar().setData({\n          selected: 1\n        })\n      }\n    } catch (error) {\n      console.error('设置 TabBar 选中状态失败:', error)\n    }\n  },\n  /**\n   * 加载本地存储的出生信息\n   */\n  loadBirthInfo() {\n    const birthInfo = wx.getStorageSync('birthInfo') || {}\n    this.setData({\n      birthInfo\n    })\n    \n    // 如果有出生信息，自动计算运势\n    if (birthInfo.birthDate && birthInfo.birthTime) {\n      this.calculateFortune()\n    }\n  },\n  /**\n   * 跳转到出生信息页面\n   */\n  navigateToBirthInfo() {\n    wx.navigateTo({\n      url: '/pages/birth-info/birth-info'\n    })\n  },\n  /**\n   * 计算运势\n   */"}