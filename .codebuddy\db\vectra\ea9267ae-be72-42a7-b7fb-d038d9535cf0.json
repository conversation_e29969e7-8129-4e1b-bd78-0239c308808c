{"chunk": 3, "numChunks": 6, "fileHash": "zijKJpj+VeHL/vTTYDoWlsbYNW+DrvZWabqBQj2IYDU=", "filePath": "pages/marriage/index.js", "content": "const app = getApp()\nPage({\n  data: {\n    // 男方信息\n    male: {\n      date: '',\n      time: '',\n      location: ''\n    },\n    // 女方信息\n    female: {\n      date: '',\n      time: '',\n      location: ''\n    },\n    // 分析结果\n    analysis: null,\n    // 是否显示结果\n    showResult: false,\n    // 加载状态\n    loading: false,\n    // 是否可以提交\n    canSubmit: false,\n    // 当前选择的分析类型\n    currentType: 'overall',\n    // 当前类型索引\n    currentTypeIndex: 0,\n    // 分析类型列表\n    analysisTypes: [\n      { id: 'overall', name: '总体评分' },\n      { id: 'personality', name: '性格相合' },\n      { id: 'career', name: '事业互助' },\n      { id: 'wealth', name: '财运匹配' },\n      { id: 'family', name: '家庭和谐' }\n    ]\n  },\n  onLoad() {\n    // 检查是否可以提交\n    this.checkCanSubmit()\n  },\n  // 男方日期选择器\n  bindMaleDateChange(e) {\n    this.setData({\n      'male.date': e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 男方时间选择器\n  bindMaleTimeChange(e) {\n    this.setData({\n      'male.time': e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 男方地点选择\n  chooseMaleLocation() {\n    wx.chooseLocation({\n      success: (res) => {\n        this.setData({\n          'male.location': res.address\n        })\n      }\n    })\n  },\n  // 女方"}