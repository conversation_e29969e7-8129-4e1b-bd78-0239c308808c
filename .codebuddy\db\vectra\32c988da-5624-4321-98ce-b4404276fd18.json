{"chunk": 34, "numChunks": 38, "fileHash": "DpyinnhZJO6jrs0/VY0BXwrNjORlEQ1BeRywBJybhxQ=", "filePath": "pages/ai-chat/ai-chat.js", "content": "const app = getApp()\nPage({\n  loadMessageHistory() {\n    try {\n      const messages = wx.getStorageSync('chat_history') || []\n      this.setData({ messages })\n    } catch (error) {\n      console.warn('加载聊天记录失败:', error)\n    }\n  },\n  /**\n   * 清空聊天记录\n   */\n  clearChatHistory() {\n    wx.showModal({\n      title: '清空聊天记录',\n      content: '确定要清空所有聊天记录吗？此操作无法撤销。',\n      success: (res) => {\n        if (res.confirm) {\n          this.setData({ \n            messages: [],\n            showQuickActions: true \n          })\n          wx.removeStorageSync('chat_history')\n          \n          // 重新添加欢迎消息\n          this.addMessage({\n            type: 'ai',\n            content: this.getWelcomeMessage(),\n            messageType: 'welcome'\n          })\n          \n          wx.showToast({\n            title: '聊天记录已清空',\n            icon: 'success'\n          })\n        }\n      }\n    })\n  },\n  /**\n   * 处理消息操作按钮点击\n   */"}