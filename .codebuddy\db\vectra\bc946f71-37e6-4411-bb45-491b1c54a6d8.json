{"chunk": 0, "numChunks": 6, "fileHash": "QLx6GhwgSxPofuK/CUpTrLuKBgVetRumHHMileilK58=", "filePath": "pages/ziwei/ziwei.wxss", "content": "/* ziwei.wxss */\n.container {\n  padding: 20rpx;\n  background-color: var(--background-color) !important;\n  min-height: 100vh;\n}\n\n/* 输入区域样式 */\n.input-section {\n  background-color: var(--card-background);\n  padding: 30rpx;\n  border-radius: 16rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 12rpx var(--shadow-color);\n  border: 2rpx solid var(--border-color);\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: var(--primary-color);\n  margin-bottom: 20rpx;\n  padding-left: 20rpx;\n  border-left: 8rpx solid var(--primary-color);\n}\n\n.input-group {\n  margin-bottom: 20rpx;\n}\n\n.picker {\n  padding: 20rpx;\n  background-color: var(--primary-lightest);\n  border-radius: 12rpx;\n  color: var(--text-primary);\n  border: 2rpx solid var(--border-color);\n}\n\n.radio {\n  margin-right: 30rpx;\n  color: var(--text-primary);\n}\n\n.calculate-btn {\n  background-color: var(--primary-color);\n  color: #FFFFFF;\n  font-size: 32rpx;\n  margin-top: 20rpx;\n  box-shadow: 0 4rpx 8rpx var(--shadow-color);\n}\n\n/* 结果区域样式 */\n.result-section {\n  margin-top: 20rpx;\n  background-color: var(--card-background);\n  border-radius: 16rpx;\n  padding: 20rpx;\n  box-shadow: 0 4rpx 12rpx var(--shadow-color);\n  border: 2rpx solid var(--border-color);\n}\n\n"}