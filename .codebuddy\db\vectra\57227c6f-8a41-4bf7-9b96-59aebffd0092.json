{"chunk": 16, "numChunks": 18, "fileHash": "yi4gCVr62AiqkHDrZGxGnPy2D7On+5yxIebQ7IGvQ04=", "filePath": "pages/community/community.js", "content": "// pages/community/community.js\nPage({\n  async loadUserFavorites() {\n    try {\n      const db = wx.cloud.database()\n      const res = await db.collection('user_favorites').where({\n        _openid: app.globalData.openid\n      }).get()\n      \n      this.setData({\n        userFavorites: res.data.map(item => item.postId)\n      })\n    } catch (error) {\n      console.error('加载收藏失败:', error)\n    }\n  },\n  // 收藏/取消收藏帖子\n  async toggleFavorite(e) {\n    const { id } = e.currentTarget.dataset\n    const db = wx.cloud.database()\n    \n    try {\n      if (this.data.userFavorites.includes(id)) {\n        // 取消收藏\n        await db.collection('user_favorites').where({\n          postId: id,\n          _openid: app.globalData.openid\n        }).remove()\n        \n        this.setData({\n          userFavorites: this.data.userFavorites.filter(item => item !== id)\n        })\n        \n        wx.showToast({\n          title: '已取消收藏',\n          icon: 'success'\n        })\n      } else {\n        // 添加收藏\n        await db.collection('user_favorites').add({\n          data: {\n            postId: id,\n            createTime: new Date()\n          }\n        })\n        \n        this.setData({\n          userFavorites: [...this.data.userFavorites, id]\n        })\n        \n        wx.showToast({\n          title: '收藏成功',\n          icon: 'success'\n        })\n      }\n    } catch (error) {\n      console.error('操作收藏失败:', error)\n      wx.showToast({\n        title: '操作失败，请重试',\n        icon: 'none'\n      })\n    }\n  },\n  // 显示评论回复框\n  showReplyModal(e) {\n    const { comment } = e.currentTarget.dataset\n    this.setData({\n      showReplyModal: true,\n      currentComment: comment\n   "}