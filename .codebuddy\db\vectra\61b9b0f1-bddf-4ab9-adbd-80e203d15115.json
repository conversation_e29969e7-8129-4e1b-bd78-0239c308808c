{"chunk": 32, "numChunks": 38, "fileHash": "DpyinnhZJO6jrs0/VY0BXwrNjORlEQ1BeRywBJybhxQ=", "filePath": "pages/ai-chat/ai-chat.js", "content": "const app = getApp()\nPage({\n  /**\n   * 展示AI回复（单条消息）\n   * @param {string} response AI回复内容\n   */\n  async typeMessage(response) {\n    await this.simulateTyping(800) // 思考时间\n\n    // 创建AI消息对象，初始内容为空\n    const message = {\n      type: 'ai',\n      content: '', // 初始为空，通过打字机效果逐渐显示\n      timestamp: this.formatTime(new Date()),\n      isTyping: true\n    }\n\n    // 添加消息到列表\n    this.addMessage(message)\n    const messageIndex = this.data.messages.length - 1\n\n    // 执行打字机效果\n    await this.typewriterEffect(response, messageIndex, 30)\n\n    await this.scrollToBottom()\n  },\n  /**\n   * 延迟函数\n   * @param {number} ms 延迟毫秒数\n   */\n  sleep(ms) {\n    return new Promise(resolve => setTimeout(resolve, ms))\n  },\n  /**\n   * 模拟打字延迟\n   */\n  simulateTyping(ms) {\n    return new Promise(resolve => setTimeout(resolve, ms))\n  },\n  /**\n   * 处理错误\n   */\n  handleError(error) {\n    this.addMessage({\n      type: 'ai',\n      content: '抱歉，我现在遇到了一些问题。请稍后再试。',\n      timestamp: this.formatTime(new Date())\n    })\n  },\n  /**\n   * 滚动到底部\n   */"}