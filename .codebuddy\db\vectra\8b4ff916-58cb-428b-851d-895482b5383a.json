{"chunk": 2, "numChunks": 6, "fileHash": "IwGBwJ25Blubhn4hCt83o/e9MDZEM6l3SvRIm1A0vrE=", "filePath": "pages/fengshui/fengshui.wxml", "content": "          <view class=\"counter-btn\" bindtap=\"increaseRooms\">+</view>\n        </view>\n  </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">房屋面积</text>\n          <text class=\"required\">*</text>\n        </view>\n        <view class=\"input-wrapper\">\n          <input\n            class=\"input\"\n            type=\"digit\"\n            placeholder=\"请输入房屋面积\"\n            bindinput=\"onAreaInput\"\n            value=\"{{area}}\"\n            maxlength=\"10\"\n            placeholder-class=\"input-placeholder\"\n          />\n          <text class=\"input-unit\">㎡</text>\n        </view>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">所在楼层</text>\n          <text class=\"required\">*</text>\n        </view>\n        <view class=\"floor-inputs\">\n          <view class=\"input-wrapper floor-input\">\n            <input\n              class=\"input\"\n              type=\"number\"\n              placeholder=\"当前楼层\"\n              bindinput=\"onCurrentFloorInput\"\n              value=\"{{currentFloor}}\"\n              maxlength=\"3\"\n              placeholder-class=\"input-placeholder\"\n            />\n          </view>\n          <text class=\"floor-separator\">/</text>\n          <view class=\"input-wrapper floor-input\">\n            <input\n              class=\"input\"\n              type=\"number\"\n              placeholder=\"总楼层\"\n              bindinput=\"onTotalFloorInput\"\n              value=\"{{totalFloor}}\"\n              maxlength=\"3\"\n              placeholder-class=\"input-placeholder\"\n            />\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 环境信息卡片 -->\n    <view class=\"info-card\">\n      <view class=\"card-header\">\n"}