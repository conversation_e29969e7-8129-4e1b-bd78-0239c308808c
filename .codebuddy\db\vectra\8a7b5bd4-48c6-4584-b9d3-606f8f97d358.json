{"chunk": 14, "numChunks": 19, "fileHash": "PEPeuJm140RAD7VBd85WlnsfcWUdoorqVSQj8ISByl0=", "filePath": "pages/ai-chat/ai-chat.wxss", "content": "  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  margin-bottom: 16rpx;\n  letter-spacing: 1rpx;\n}\n\n.welcome-desc {\n  display: block;\n  font-size: 28rpx;\n  color: var(--text-secondary);\n  line-height: 1.6;\n}\n\n/* 快捷操作 */\n.quick-actions {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 24rpx;\n  margin: 24rpx 0;\n}\n\n.quick-item {\n  background: var(--card-background);\n  border-radius: 20rpx;\n  padding: 24rpx;\n  display: flex;\n  align-items: center;\n  gap: 16rpx;\n  box-shadow: 0 6rpx 20rpx var(--shadow-color);\n  transition: all 0.3s ease;\n}\n\n.quick-item:active {\n  transform: scale(0.98);\n  background: var(--primary-lightest);\n}\n\n.quick-icon {\n  font-size: 36rpx;\n}\n\n.quick-text {\n  font-size: 28rpx;\n  color: var(--text-primary);\n  font-weight: 500;\n}\n\n/* 消息列表 */\n.message-list {\n  flex: 1;\n  overflow-y: auto;\n  padding-bottom: 240rpx;\n  width: 100%;\n  box-sizing: border-box;\n}\n\n/* 消息项样式 */\n.message-item {\n  margin-bottom: 36rpx;\n  animation: message-slide-in 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  width: 100%;\n  box-sizing: border-box;\n  padding: 0 12rpx;\n}\n\n/* 消息时间包装器 */\n.message-time-wrapper {\n  display: flex;\n"}