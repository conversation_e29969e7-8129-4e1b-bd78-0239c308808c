{"chunk": 4, "numChunks": 5, "fileHash": "xeaCZ1EVBwibSEkFupn/pXs0/tot8OOYCLYhDZrm9J0=", "filePath": "pages/bazi/bazi.js", "content": "const BaziCalculator = require('../../utils/bazi.js');\nPage({\n  calculateBazi: function(birthInfo) {\n    if (!birthInfo) return;\n\n    this.setData({ loading: true, error: null });\n\n    try {\n      const calculator = new BaziCalculator();\n      const result = calculator.calculateBazi(\n        birthInfo.birthDate,\n        birthInfo.birthTime,\n        birthInfo.gender\n      );\n\n      // 处理五行分析数据\n      const wuxingData = this.processWuxingData(result.wuxing);\n      \n      // 处理十神格局\n      const shishenData = this.processShishenData(result.shishen);\n      \n      // 处理大运流年数据\n      const fortuneData = this.processFortuneData(result.fortune);\n\n      // 处理喜用神分析\n      const xiyongData = this.processXiyongData(result.xiyong);\n\n      this.setData({\n        baziResult: {\n          ...result,\n          wuxing: wuxingData,\n          shishen: shishenData,\n          fortune: fortuneData,\n          xiyong: xiyongData\n        },\n        loading: false\n      });\n    } catch (error) {\n      console.error('八字计算错误:', error);\n      this.setData({\n        error: '计算八字时出现错误，请检查输入信息是否正确',\n        loading: false\n      });\n    }\n  },\n  // 处理五行分析数据\n  processWuxingData: function(wuxing) {\n    if (!wuxing) return null;\n    \n    // 直接返回五行数据，因为 bazi.js 中已经处理好了格式\n    return wuxing;\n  },\n  // 处理十神格局数据"}