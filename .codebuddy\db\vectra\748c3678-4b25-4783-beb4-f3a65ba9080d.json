{"chunk": 2, "numChunks": 8, "fileHash": "pWWTyJooIxy6mFbGJzYtyes3pCjDoqcgXSX9h6jKjqM=", "filePath": "utils/bazi/calculator.js", "content": "// 天干\nclass BaziCalculator {\n  constructor() {\n    this.heavenlyStems = HEAVENLY_STEMS;\n    this.earthlyBranches = EARTHLY_BRANCHES;\n  }\n  async calculate(params) {\n    const { birthDate, birthTime, gender } = params;\n\n    try {\n      // 解析出生日期\n      const date = new Date(birthDate + ' ' + birthTime);\n      \n      // 计算八字\n      const result = {\n        yearStem: this.calculateYearStem(date),\n        yearBranch: this.calculateYearBranch(date),\n        monthStem: this.calculateMonthStem(date),\n        monthBranch: this.calculateMonthBranch(date),\n        dayStem: this.calculateDayStem(date),\n        dayBranch: this.calculateDayBranch(date),\n        hourStem: this.calculateHourStem(date),\n        hourBranch: this.calculateHourBranch(date)\n      };\n\n      // 计算五行分布\n      const wuxing = this.calculateWuxing(result);\n\n      // 生成运势解读\n      const interpretation = this.generateInterpretation(result, wuxing, gender);\n\n      // 计算运势评分\n      const fortune = this.calculateFortune(result, wuxing, gender);\n\n      return {\n        ...result,\n        wuxing,\n        interpretation,\n        fortune\n      };\n    } catch (error) {\n      console.error('八字计算错误:', error);\n      throw new Error('八字计算失败');\n    }\n  }\n  calculateYearStem(date) {\n    const year = date.getFullYear();\n    const offset = (year - 4) % 10;\n    return this.heavenlyStems[offset];\n  }"}