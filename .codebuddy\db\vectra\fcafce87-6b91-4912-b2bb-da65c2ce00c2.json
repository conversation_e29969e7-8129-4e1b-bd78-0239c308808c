{"chunk": 63, "numChunks": 74, "fileHash": "UWh+hTmku0OFKrrOI8FY7Ob/w0LwUBFFaP/YfXcJBpg=", "filePath": "utils/xingming/calculator.js", "content": "// 汉字笔画数据库\nclass XingmingCalculator {\n  // 计算三才配置\n  calculateSancai() {\n    // 获取三才属性\n    const tianCai = this.getWuxingAttribute(this.calculateTianGe())\n    const renCai = this.getWuxingAttribute(this.calculateRenGe())\n    const diCai = this.getWuxingAttribute(this.calculateDiGe())\n\n    // 组合三才\n    const sancaiKey = `${tianCai}${renCai}${diCai}`\n    const sancaiInfo = SANCAI_DATA[sancaiKey] || {\n      score: 70,\n      description: '普通配置，平稳发展'\n    }\n\n    // 生成图表数据\n    const data = [\n      { name: '天才', value: this.calculateAttributeStrength(tianCai), element: tianCai },\n      { name: '人才', value: this.calculateAttributeStrength(renCai), element: renCai },\n      { name: '地才', value: this.calculateAttributeStrength(diCai), element: diCai }\n    ]\n\n    // 生成分析文字\n    const analysis = `姓名三才配置为：天才${tianCai}，人才${renCai}，地才${diCai}。${sancaiInfo.description}`\n\n    return {\n      score: sancaiInfo.score,\n      analysis,\n      data\n    }\n  }\n  // 计算八字配合"}