{"chunk": 1, "numChunks": 6, "fileHash": "Hr0NLKfsiy0m55e08Mw/WBiaaXrb6byHBEKkofIFTxk=", "filePath": "pages/fengshui/index.wxss", "content": "  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n  padding-bottom: 20rpx;\n  border-bottom: 2rpx solid #eee;\n}\n\n.bagua-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 20rpx;\n  margin-top: 20rpx;\n}\n\n.grid-item {\n  background: #f8f8f8;\n  padding: 20rpx;\n  border-radius: 8rpx;\n  text-align: center;\n  transition: all 0.3s ease;\n}\n\n.grid-item.active {\n  background: #4a5568;\n  color: #fff;\n}\n\n.direction {\n  font-size: 28rpx;\n  font-weight: bold;\n  display: block;\n  margin-bottom: 8rpx;\n}\n\n.gua-name {\n  font-size: 24rpx;\n  display: block;\n  margin-bottom: 8rpx;\n}\n\n.element {\n  font-size: 24rpx;\n  color: #666;\n}\n\n.position-analysis {\n  margin-top: 20rpx;\n}\n\n.position-item {\n  background: #f8f8f8;\n  border-radius: 8rpx;\n  padding: 20rpx;\n  margin-bottom: 20rpx;\n}\n\n.position-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.position-header.auspicious {\n  color: #38a169;\n}\n\n.position-header.inauspicious {\n  color: #e53e3e;\n}\n\n.usage {\n  font-size: 26rpx;\n}\n\n"}