{"chunk": 64, "numChunks": 74, "fileHash": "UWh+hTmku0OFKrrOI8FY7Ob/w0LwUBFFaP/YfXcJBpg=", "filePath": "utils/xingming/calculator.js", "content": "// 汉字笔画数据库\nclass XingmingCalculator {\n  calculateBaziMatch() {\n    const { bazi } = this.options\n    const nameWuxing = this.getMainWuxing()\n    \n    let score = 60 // 基础分\n    let analysis = ''\n\n    // 判断是否与日主相生\n    const riZhu = this.getRiZhuWuxing(bazi)\n    if (this.isWuxingHelpful(nameWuxing, riZhu)) {\n      score += 20\n      analysis += '姓名五行与八字日主相生，'\n    } else if (this.isWuxingConflict(nameWuxing, riZhu)) {\n      score -= 10\n      analysis += '姓名五行与八字日主相克，'\n    }\n\n    // 判断是否与大运相合\n    const daYun = this.getDaYunWuxing(bazi)\n    if (this.isWuxingHelpful(nameWuxing, daYun)) {\n      score += 10\n      analysis += '与大运五行相生，有助于人生发展。'\n    } else if (this.isWuxingConflict(nameWuxing, daYun)) {\n      score -= 5\n      analysis += '与大运五行相克，需要注意趋吉避凶。'\n    } else {\n      analysis += '与大运五行中和，发展平稳。'\n    }\n\n    return {\n      score: Math.max(0, Math.min(100, score)),\n      analysis\n    }\n  }\n  // 生成吉凶分析"}