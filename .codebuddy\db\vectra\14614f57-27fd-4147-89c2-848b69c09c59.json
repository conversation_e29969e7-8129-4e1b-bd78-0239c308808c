{"chunk": 2, "numChunks": 6, "fileHash": "Hr0NLKfsiy0m55e08Mw/WBiaaXrb6byHBEKkofIFTxk=", "filePath": "pages/fengshui/index.wxss", "content": ".description {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n.layout-advice {\n  margin-top: 20rpx;\n}\n\n.advice-item {\n  margin-bottom: 20rpx;\n}\n\n.room-name {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.suggestion {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n.solutions {\n  margin-top: 20rpx;\n}\n\n.solution-item {\n  margin-bottom: 20rpx;\n}\n\n.solution-type {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.solution-desc {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n/* 顶部banner */\n.banner {\n  position: relative;\n  width: 100%;\n  height: 360rpx;\n  overflow: hidden;\n}\n\n.banner-bg {\n  width: 100%;\n  height: 100%;\n}\n\n.banner-title {\n  position: absolute;\n  left: 40rpx;\n  bottom: 40rpx;\n  color: #fff;\n  z-index: 1;\n}\n\n.main-title {\n  font-size: 48rpx;\n  font-weight: bold;\n  display: block;\n  margin-bottom: 12rpx;\n  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);\n}\n\n.sub-title {\n  font-size: 28rpx;\n  opacity: 0.9;\n}\n\n/* 户型选择器 */\n.room-picker {\n  display: flex;\n"}