{"chunk": 3, "numChunks": 76, "fileHash": "lPYWZE8QItgwSyEsMXHtl6y/HYBCI8BdGaV+1l8ICMA=", "filePath": "docs/md/API_INTERFACE_DOCUMENTATION.md", "content": "  \"id\": \"string\",                   // 记录ID\n  \"user_id\": \"string\",              // 用户ID\n  \"name\": \"string\",                 // 姓名\n  \"gender\": \"string\",               // 性别 (男/女)\n  \"birth_year\": \"number\",           // 出生年份\n  \"birth_month\": \"number\",          // 出生月份\n  \"birth_day\": \"number\",            // 出生日期\n  \"birth_hour\": \"number\",           // 出生小时\n  \"birth_minute\": \"number\",         // 出生分钟\n  \"birth_timezone\": \"string\",       // 时区 (默认: Asia/Shanghai)\n  \"lunar_year\": \"number\",           // 农历年份\n  \"lunar_month\": \"number\",          // 农历月份\n  \"lunar_day\": \"number\",            // 农历日期\n  \"lunar_leap_month\": \"boolean\",    // 是否闰月\n  \"zodiac\": \"string\",               // 生肖\n  \"constellation\": \"string\",        // 星座\n  \"lucky_number\": \"number\",         // 幸运数字 (1-62)\n  \"bazi\": {                         // 八字信息\n    \"year_gan\": \"string\",           // 年干\n    \"year_zhi\": \"string\",           // 年支\n    \"month_gan\": \"string\",          // 月干\n    \"month_zhi\": \"string\",          // 月支\n    \"day_gan\": \"string\",            // 日干\n    \"day_zhi\": \"string\",            // 日支\n    \"hour_gan\": \"string\",           // 时干\n    \"hour_zhi\": \"string\"            // 时支\n  },\n  \"wuxing\": {                       // 五行信息\n"}