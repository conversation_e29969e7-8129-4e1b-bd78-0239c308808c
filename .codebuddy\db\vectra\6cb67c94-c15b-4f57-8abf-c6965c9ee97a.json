{"chunk": 0, "numChunks": 6, "fileHash": "NZjhfJONEn33fTaL5WUzkjtuxbwRJIB1E7qQ/zXp6ww=", "filePath": "pages/profile/profile.wxml", "content": "<!--pages/profile/profile.wxml-->\n<view class=\"page-container\">\n  <!-- 加载组件 -->\n  <loading show=\"{{isLoading}}\" text=\"加载中...\" />\n  \n  <!-- 顶部背景 -->\n  <view class=\"profile-header\">\n    <image class=\"header-bg\" src=\"https://img.freepik.com/free-vector/gradient-purple-aesthetic-background_23-2149123739.jpg\" mode=\"aspectFill\" />\n    <view class=\"header-overlay\"></view>\n    \n    <!-- 用户信息卡片 -->\n    <view class=\"user-card\">\n      <view class=\"user-main\">\n        <image \n          class=\"user-avatar\" \n          src=\"{{userInfo.avatarUrl || 'https://img.icons8.com/fluency/96/user-male-circle.png'}}\" \n          mode=\"aspectFill\"\n          bindtap=\"editAvatar\"\n        />\n      <view class=\"user-info\">\n          <view class=\"user-name\" bindtap=\"editNickname\">\n            {{userInfo.nickName || '点击登录'}}\n            <text class=\"edit-icon\" wx:if=\"{{userInfo.userId}}\">✏️</text>\n          </view>\n          <view class=\"user-desc\" wx:if=\"{{userInfo.userId}}\">\n            <text class=\"user-id\">ID: {{userInfo.userId}}</text>\n            <text class=\"user-level\">{{userLevel.name}}</text>\n          </view>\n          <button class=\"login-btn gradient-btn\" wx:if=\"{{!userInfo.userId}}\" bindtap=\"login\">\n            立即登录\n          </button>\n        </view>\n      </view>\n      \n      <!-- 用户统计 -->\n      <view class=\"user-stats\">\n        <view class=\"stat-item\" bindtap=\"goToPoints\">\n          <text class=\"stat-value\">{{userPoints || 0}}</text>\n          <text class=\"stat-label\">积分</text>\n        </view>\n"}