{"chunk": 7, "numChunks": 15, "fileHash": "An9WOmGw20FYVB4+SrCnlnnpOBHN6cohdD4SvY7rXL8=", "filePath": "pages/ziwei/ziwei.js", "content": "// ziwei.js\nPage({\n  calculateZiwei(birthInfo) {\n    try {\n      // 计算紫微斗数\n      const result = ziweiCalculator.calculate({\n        birthDate: birthInfo.date,\n        birthTime: birthInfo.time,\n        gender: birthInfo.gender\n      })\n      \n      // 分析十四主星组合\n      const starCombinations = ziweiCalculator.analyzeStarCombinations(result.stars)\n      \n      // 预测人生轨迹\n      const lifeTrajectory = ziweiCalculator.predictLifeTrajectory(result.mingGong, result.shenGong, result.stars)\n      \n      // 生成大限流年\n      const fortuneYears = this.generateFortuneYears(birthInfo.date, result.mingGong)\n\n      // 转换数据结构以匹配模板\n      const formattedResult = {\n        ...result,\n        starCombinations,\n        lifeTrajectory: {\n          ...lifeTrajectory,\n          fortuneYears,\n          // 添加详细预测内容\n          careerDetails: this.generateCareerDetails(result.mingGong, result.stars),\n          marriageDetails: this.generateMarriageDetails(result.shenGong, result.stars),\n          wealthDetails: this.generateWealthDetails(result.stars),\n          healthDetails: this.generateHealthDetails(result.stars)\n        },\n        palaces: result.palaces.map(palace => ({\n          ...palace,\n          mainStar: result.stars.find(s => s.position === palace.position && s.type === '主星')?.name || '',\n          stars: result.stars.filter(s => s.position === palace.position)\n        }))\n      }\n\n      this.setData({\n        loading: false,\n        ziweiResult: formattedResult\n      })\n    } catch (error) {"}