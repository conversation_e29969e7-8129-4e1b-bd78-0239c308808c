{"chunk": 4, "numChunks": 55, "fileHash": "fCZ6nFoNZXQqhvADUzMwGWD6jOU+ajj61B8BD3CwDgs=", "filePath": "pages/name-test/name-test.js", "content": "// 姓名测试页面逻辑\nPage({\n  async startNameTest() {\n    const { surname, givenName, gender } = this.data;\n    \n    if (!surname.trim() || !givenName.trim()) {\n      wx.showToast({\n        title: '请输入完整姓名',\n        icon: 'none'\n      });\n      return;\n    }\n\n    this.setData({ isLoading: true });\n\n    try {\n      // 调用姓名分析API\n      const result = await this.analyzeNameFortune(surname, givenName, gender);\n      \n      this.setData({\n        testResult: result,\n        isLoading: false\n      });\n\n      // 保存到历史记录\n      this.saveToHistory(result);\n\n    } catch (error) {\n      console.error('姓名测试失败:', error);\n      this.setData({ isLoading: false });\n      \n      wx.showToast({\n        title: '测试失败，请重试',\n        icon: 'none'\n      });\n    }\n  },\n  // 姓名分析算法"}