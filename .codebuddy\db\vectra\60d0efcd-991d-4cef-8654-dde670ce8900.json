{"chunk": 0, "numChunks": 5, "fileHash": "4lpNnoqGg1eEPtIgkdLgszOng5dzR4kuUtSZ8HRwytk=", "filePath": "pages/yijing/yijing.wxss", "content": "/* yijing.wxss */\n.container {\n  padding: 30rpx;\n  background-color: var(--background-color) !important;\n  min-height: 100vh;\n  box-sizing: border-box;\n}\n\n/* 页面标题 */\n.page-header {\n  text-align: center;\n  margin-bottom: 40rpx;\n  padding: 20rpx 0;\n}\n\n.page-title {\n  font-size: 48rpx;\n  font-weight: bold;\n  color: var(--primary-color);\n  margin-bottom: 10rpx;\n}\n\n.page-subtitle {\n  font-size: 28rpx;\n  color: var(--text-light);\n}\n\n/* 卡片通用样式 */\n.card {\n  background-color: var(--card-background);\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 4rpx 20rpx var(--shadow-color);\n  border: 2rpx solid var(--border-color);\n}\n\n.card-title {\n  display: flex;\n  align-items: center;\n  font-size: 32rpx;\n  font-weight: bold;\n  color: var(--primary-color);\n  margin-bottom: 30rpx;\n  padding-bottom: 20rpx;\n  border-bottom: 2rpx solid var(--border-color);\n}\n\n.icon {\n  margin-right: 10rpx;\n  font-size: 36rpx;\n  color: var(--primary-color);\n}\n\n/* 输入区域样式 */\n.input-section {\n  margin-bottom: 30rpx;\n}\n\n.input-group {\n  margin-bottom: 25rpx;\n}\n\n.picker {\n  display: flex;\n  align-items: center;\n  padding: 20rpx 0;\n  border-bottom: 2rpx solid var(--border-color);\n}\n\n.label {\n  width: 160rpx;\n"}