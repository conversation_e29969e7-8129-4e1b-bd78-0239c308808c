{"chunk": 3, "numChunks": 6, "fileHash": "VToXv7ATbmi0am+Amh5z0qVhRJK7IjhEVx3t5LgFVgM=", "filePath": "pages/marriage/index.wxml", "content": "        <text class=\"tip-item\">• 您的信息将被安全保护，仅用于合婚分析</text>\n      </view>\n    </view>\n\n    <!-- 提交按钮 -->\n    <view class=\"submit-section\">\n      <button\n        class=\"submit-btn {{canSubmit ? 'active' : 'disabled'}}\"\n        bindtap=\"submitAnalysis\"\n        disabled=\"{{!canSubmit}}\"\n        loading=\"{{loading}}\"\n      >\n        <text class=\"btn-text\">开始合婚测算</text>\n        <text class=\"btn-icon\">💕</text>\n      </button>\n    </view>\n  </view>\n\n  <!-- 结果页面顶部标题区域 -->\n  <view class=\"header\" wx:if=\"{{showResult}}\">\n    <view class=\"header-content\">\n      <view class=\"title\">合婚结果</view>\n      <view class=\"subtitle\">根据双方八字分析得出的匹配度</view>\n    </view>\n    <view class=\"header-decoration\">\n      <text class=\"icon\">✨</text>\n    </view>\n  </view>\n\n  <!-- 结果区域 -->\n  <view class=\"form-container\" wx:if=\"{{showResult}}\">\n    <!-- 重新测算按钮 -->\n    <view class=\"reset-section\">\n      <button class=\"reset-btn\" bindtap=\"resetAnalysis\">\n        <text class=\"btn-text\">重新测算</text>\n        <text class=\"btn-icon\">🔄</text>\n      </button>\n    </view>\n\n    <!-- 分析类型选择 -->\n    <view class=\"analysis-tabs-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">分析维度</text>\n        <text class=\"card-icon\">📊</text>\n"}