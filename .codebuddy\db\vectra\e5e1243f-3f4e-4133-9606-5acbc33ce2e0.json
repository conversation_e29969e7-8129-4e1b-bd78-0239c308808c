{"chunk": 12, "numChunks": 76, "fileHash": "lPYWZE8QItgwSyEsMXHtl6y/HYBCI8BdGaV+1l8ICMA=", "filePath": "docs/md/API_INTERFACE_DOCUMENTATION.md", "content": "Query Parameters:\n- session_id: string (可选，特定会话)\n- page: number (页码，默认1)\n- limit: number (每页数量，默认20)\n- start_date: date (开始日期，可选)\n- end_date: date (结束日期，可选)\n\nResponse:\n{\n  \"status\": \"success|error\",\n  \"message\": \"string\",\n  \"data\": {\n    \"messages\": [\n      {\n        \"id\": \"string\",\n        \"session_id\": \"string\",\n        \"message_type\": \"string\",\n        \"content\": \"string\",\n        \"intent\": \"string\",\n        \"created_at\": \"datetime\"\n      }\n    ],\n    \"pagination\": {\n      \"current_page\": \"number\",\n      \"total_pages\": \"number\",\n      \"total_count\": \"number\",\n      \"has_next\": \"boolean\",\n      \"has_prev\": \"boolean\"\n    }\n  }\n}\n```\n\n#### 3. 删除聊天记录\n```\nDELETE /api/ai-chat/session/{session_id}\nAuthorization: Bearer {token}\n\nResponse:\n{\n  \"status\": \"success|error\",\n  \"message\": \"string\",\n  \"data\": {\n    \"deleted_count\": \"number\"\n  }\n}\n```\n\n#### 4. 快捷操作询问\n```\nPOST /api/ai-chat/quick-action\nAuthorization: Bearer {token}\nContent-Type: application/json\n\nRequest:\n{\n  \"action_id\": \"string\",            // 快捷操作ID (bazi/yijing/fengshui/wuxing)\n  \"session_id\": \"string\",           // 会话ID (可选)\n  \"additional_info\": \"string\"       // 额外信息 (可选)\n}\n\nResponse:\n{\n  \"status\": \"success|error\",\n  \"message\": \"string\",\n  \"data\": {\n    \"session_id\": \"string\",\n"}