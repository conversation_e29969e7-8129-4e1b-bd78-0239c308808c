{"chunk": 0, "numChunks": 6, "fileHash": "TRjhIujXrZdfOAnJdtSzbShY2tPK4Kr6sOiSYsXo4vg=", "filePath": "pages/daily-fortune/daily-fortune.wxml", "content": "<!--每日运势页面-->\n<view class=\"page-container\">\n  <!-- 头部日期选择 -->\n  <view class=\"date-header\">\n    <view class=\"date-selector\">\n      <view class=\"date-nav\" bindtap=\"prevDate\">\n        <text class=\"nav-icon\">‹</text>\n      </view>\n      <picker \n        mode=\"date\"\n        value=\"{{selectedDate}}\"\n        bindchange=\"onDateChange\"\n      >\n        <view class=\"current-date\">\n          <text class=\"date-text\">{{formattedDate}}</text>\n          <text class=\"lunar-date\">{{lunarDate}}</text>\n        </view>\n      </picker>\n      <view class=\"date-nav\" bindtap=\"nextDate\">\n        <text class=\"nav-icon\">›</text>\n      </view>\n    </view>\n    <view class=\"today-btn\" bindtap=\"goToToday\" wx:if=\"{{!isToday}}\">今天</view>\n  </view>\n\n  <!-- 运势概览 -->\n  <view class=\"fortune-overview\">\n    <view class=\"overview-header\">\n      <view class=\"fortune-title\">今日运势</view>\n      <view class=\"overall-score\">\n        <text class=\"score-number\">{{dailyFortune.overallScore}}</text>\n        <text class=\"score-unit\">分</text>\n      </view>\n    </view>\n    <view class=\"fortune-summary\">\n      <text class=\"summary-text\">{{dailyFortune.summary}}</text>\n    </view>\n    <view class=\"fortune-keyword\">\n      <text class=\"keyword-label\">今日关键词：</text>\n      <text class=\"keyword-text\">{{dailyFortune.keyword}}</text>\n    </view>\n  </view>\n\n  <!-- 运势详情 -->\n  <view class=\"fortune-details\">\n    <!-- 四大运势 -->\n"}