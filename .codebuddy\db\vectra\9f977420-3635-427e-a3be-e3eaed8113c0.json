{"chunk": 2, "numChunks": 7, "fileHash": "RAGdlTZI62FtOLCwB83HuJtgEeQmh/OOiLgGD1MsfmA=", "filePath": "frontend-implementation/utils/request.js", "content": "// 网络请求工具\nclass RequestInterceptor {\n  constructor() {\n    this.requestQueue = new Map()\n    this.isRefreshing = false\n    this.refreshSubscribers = []\n  }\n  /**\n   * 添加认证头\n   */\n  addAuthHeader(options) {\n    const token = getStorageSync('token')\n    if (token) {\n      options.header = {\n        ...options.header,\n        'Authorization': `Bearer ${token}`\n      }\n    }\n    return options\n  }\n  /**\n   * 处理响应\n   */\n  async handleResponse(response, originalOptions) {\n    const { statusCode, data } = response\n\n    // HTTP状态码检查\n    if (statusCode >= 200 && statusCode < 300) {\n      // 业务状态码检查\n      if (data.status === 'success') {\n        return data\n      } else if (data.status === 'error') {\n        // 处理业务错误\n        if (data.error_code === '1001' || data.error_code === '1002') {\n          // Token过期或无效，尝试刷新\n          return this.handleTokenExpired(originalOptions)\n        } else {\n          throw new Error(data.message || '请求失败')\n        }\n      } else {\n        throw new Error('响应格式错误')\n      }\n    } else if (statusCode === 401) {\n      // 未授权，尝试刷新Token\n      return this.handleTokenExpired(originalOptions)\n    } else {\n      throw new Error(`网络错误: ${statusCode}`)\n    }\n  }\n  /**\n   * 处理Token过期\n   */"}