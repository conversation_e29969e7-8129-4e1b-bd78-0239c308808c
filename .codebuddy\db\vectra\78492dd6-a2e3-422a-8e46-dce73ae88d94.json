{"chunk": 11, "numChunks": 15, "fileHash": "O/nJs1XsxYT9PDo4XFlMEnsiqZRG/Zu4Jl5KoBF5Ci8=", "filePath": "pages/index/index.wxml", "content": "                    <view class=\"yiji-tag\" wx:for=\"{{todayInfo.lunar.yiji.yi}}\" wx:key=\"name\" bindtap=\"showYiJiDetail\" data-type=\"yi\" data-item=\"{{item}}\">\n                      <text class=\"tag-text\">{{item.name}}</text>\n                    </view>\n                  </view>\n                </view>\n                <view class=\"ji-items\" wx:if=\"{{todayInfo.lunar.yiji.ji.length}}\">\n                  <text class=\"yiji-label\">忌：</text>\n                  <view class=\"yiji-content\">\n                    <view class=\"yiji-tag\" wx:for=\"{{todayInfo.lunar.yiji.ji}}\" wx:key=\"name\" bindtap=\"showYiJiDetail\" data-type=\"ji\" data-item=\"{{item}}\">\n                      <text class=\"tag-text\">{{item.name}}</text>\n                    </view>\n                  </view>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 节气提醒和签到区域 -->\n      <view class=\"header-section\" wx:if=\"{{solarTerms.isToday || !signInStatus}}\">\n        <view class=\"solar-terms-notice\" wx:if=\"{{solarTerms.isToday}}\">\n          <image src=\"/assets/icons/solar-terms.png\" class=\"notice-icon\" mode=\"aspectFit\" />\n          <text class=\"notice-text\">今日{{solarTerms.name}}，万物更新</text>\n        </view>\n        <view class=\"sign-in-section\" wx:if=\"{{!signInStatus}}\">\n          <view class=\"sign-in-card\" bindtap=\"handleSignIn\">\n            <view class=\"sign-in-icon\">📅</view>\n            <view class=\"sign-in-text\">\n              <text class=\"sign-title\">今日签到</text>\n"}