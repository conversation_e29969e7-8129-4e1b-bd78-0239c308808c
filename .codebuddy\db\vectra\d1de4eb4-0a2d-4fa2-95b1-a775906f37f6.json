{"chunk": 6, "numChunks": 8, "fileHash": "pWWTyJooIxy6mFbGJzYtyes3pCjDoqcgXSX9h6jKjqM=", "filePath": "utils/bazi/calculator.js", "content": "// 天干\nclass BaziCalculator {\n  generateInterpretation(bazi, wuxing, gender) {\n    // 这里可以根据八字和五行分布生成详细的命理解读\n    // 这是一个简化版本的示例\n    let interpretation = '根据您的八字命盘分析：\\n\\n';\n    // 分析日主强弱\n    const dayMaster = STEM_WUXING[bazi.dayStem];\n    const dayMasterStrength = wuxing[dayMaster];\n    if (dayMasterStrength > 150) {\n      interpretation += '您的日主偏强，显示出较强的个性和领导能力。建议在事业上可以选择自主创业或管理岗位。\\n\\n';\n    } else if (dayMasterStrength < 100) {\n      interpretation += '您的日主偏弱，性格较为温和，善于协调人际关系。建议在事业上可以选择服务业或辅助性工作。\\n\\n';\n    } else {\n      interpretation += '您的日主中和，性格平衡，适应能力强。可以在多个领域都有不错的发展。\\n\\n';\n    }\n    // 分析五行特点\n    const strongestElement = Object.entries(wuxing).reduce((a, b) => b[1] > a[1] ? b : a);\n    interpretation += `您的命盘中${this.getWuxingName(strongestElement[0])}最旺，`;"}