{"chunk": 10, "numChunks": 18, "fileHash": "yi4gCVr62AiqkHDrZGxGnPy2D7On+5yxIebQ7IGvQ04=", "filePath": "pages/community/community.js", "content": "// pages/community/community.js\nPage({\n  switchCategory(e) {\n    const category = e.currentTarget.dataset.category\n    this.setData({\n      currentCategory: category,\n      posts: [],\n      pageNum: 1,\n      hasMore: true\n    }, () => {\n      this.loadPosts()\n    })\n  },\n  /**\n   * 显示发布模态框\n   */\n  showPublishModal() {\n    if (!this.data.isCloudInited) {\n      wx.showToast({\n        title: '系统未初始化，请稍后再试',\n        icon: 'none'\n      })\n      return\n    }\n    this.setData({ showPublishModal: true })\n  },\n  /**\n   * 隐藏发布模态框\n   */\n  hidePublishModal() {\n    this.setData({ showPublishModal: false })\n  },\n  /**\n   * 输入标题\n   */\n  onTitleInput(e) {\n    this.setData({\n      'newPost.title': e.detail.value\n    })\n  },\n  /**\n   * 输入内容\n   */\n  onContentInput(e) {\n    this.setData({\n      'newPost.content': e.detail.value\n    })\n  },\n  /**\n   * 选择分类\n   */\n  onCategoryChange(e) {\n    this.setData({\n      'newPost.categoryIndex': parseInt(e.detail.value)\n    })\n  },\n  /**\n   * 选择图片\n   */\n  async chooseImage() {\n    try {\n      const res = await wx.chooseImage({\n        count: 9 - this.data.newPost.images.length,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera']\n      })\n      \n      this.setData({\n        'newPost.images': [...this.data.newPost.images, ...res.tempFilePaths]\n      })\n    } catch (error) {\n      console.error('选择图片失败:', error)\n    }\n  },\n  /**\n   * 删除图片\n   */"}