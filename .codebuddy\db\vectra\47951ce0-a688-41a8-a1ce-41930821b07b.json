{"chunk": 14, "numChunks": 22, "fileHash": "jr30LZOMc1P5VIpMptyaGnan9xv44m/c09rHA5aMeTY=", "filePath": "pages/index/index.js", "content": "// index.js\nPage({\n  getFeatureInfo(type) {\n    const featureMap = {\n      'bazi': { name: '八字排盘', icon: '/assets/icons/home/<USER>', url: '/pages/bazi/bazi' },\n      'ziwei': { name: '紫微斗数', icon: '/assets/icons/home/<USER>', url: '/pages/ziwei/ziwei' },\n      'wuxing': { name: '五行八卦', icon: '/assets/icons/home/<USER>', url: '/pages/wuxing/wuxing' },\n      'yijing': { name: '易经卦象', icon: '/assets/icons/home/<USER>', url: '/pages/yijing/yijing' },\n      'fengshui': { name: '风水布局', icon: '/assets/icons/home/<USER>', url: '/pages/fengshui/fengshui' },\n      'divination': { name: '事件占卜', icon: '/assets/icons/home/<USER>', url: '/pages/divination/divination' },\n      'fortune': { name: '流年运势', icon: '/assets/icons/home/<USER>', url: '/pages/fortune/fortune' }\n    }\n    return featureMap[type] || null\n  },\n  // 加载最近使用的功能\n  loadRecentUsed() {\n    const recentUsed = wx.getStorageSync('recentUsed') || []\n    const recentUsedWithInfo = recentUsed.map(item => {\n      const featureInfo = this.getFeatureInfo(item.type)\n      return {\n        ...item,\n        ...featureInfo\n      }\n    }).filter(item => item.name) // 过滤掉无效的类型\n    \n    this.setData({ recentUsed: recentUsedWithInfo })\n  },"}