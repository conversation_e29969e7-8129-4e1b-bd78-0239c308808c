{"chunk": 10, "numChunks": 19, "fileHash": "PEPeuJm140RAD7VBd85WlnsfcWUdoorqVSQj8ISByl0=", "filePath": "pages/ai-chat/ai-chat.wxss", "content": "    0 4rpx 8rpx rgba(0, 0, 0, 0.06);\n  transition: all 0.3s ease;\n  position: relative;\n  z-index: 1;\n}\n\n.ball-content:active {\n  transform: scale(0.9);\n  box-shadow:\n    0 4rpx 12rpx rgba(123, 40, 202, 0.28),\n    0 2rpx 6rpx rgba(0, 0, 0, 0.06);\n}\n\n.ball-icon {\n  font-size: 56rpx;\n  color: white;\n  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\n}\n\n.ball-pulse {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 4rpx solid rgba(121, 40, 202, 0.6);\n  border-radius: 50%;\n  animation: pulse-ring 2s ease-out infinite;\n}\n\n/* 遮罩层 */\n.overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  backdrop-filter: blur(10rpx);\n  z-index: 999;\n  transition: all 0.3s ease;\n}\n\n.overlay.show {\n  opacity: 1;\n  pointer-events: auto;\n}\n\n.overlay.hide {\n  opacity: 0;\n  pointer-events: none;\n}\n\n/* 底部占位 */\n.bottom-spacer {\n  height: 120rpx;\n}\n\n/* 底部安全区域 */\n.safe-bottom {\n"}