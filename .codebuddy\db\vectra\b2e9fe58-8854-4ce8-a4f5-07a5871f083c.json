{"chunk": 8, "numChunks": 19, "fileHash": "PEPeuJm140RAD7VBd85WlnsfcWUdoorqVSQj8ISByl0=", "filePath": "pages/ai-chat/ai-chat.wxss", "content": "  border-radius: 16rpx;\n  padding: 20rpx;\n  font-size: 32rpx;\n  line-height: 1.5;\n  color: #1f2937;\n  resize: none;\n  transition: all 0.3s ease;\n}\n\n.message-input:focus {\n  border-color: #667eea;\n  background: white;\n  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);\n}\n\n.input-placeholder {\n  color: #9ca3af;\n}\n\n.input-counter {\n  position: absolute;\n  bottom: 12rpx;\n  right: 16rpx;\n  font-size: 24rpx;\n  color: #9ca3af;\n}\n\n/* 按钮区域 */\n.button-section {\n  padding: 24rpx 32rpx;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 16rpx;\n}\n\n.cancel-btn {\n  flex: 1;\n  height: 88rpx;\n  background: #f3f4f6;\n  color: #6b7280;\n  border: none;\n  border-radius: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8rpx;\n  font-size: 30rpx;\n  transition: all 0.3s ease;\n}\n\n.cancel-btn:active {\n  background: #e5e7eb;\n  transform: scale(0.98);\n}\n\n.send-btn {\n  flex: 2;\n  height: 88rpx;\n  border: none;\n  border-radius: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8rpx;\n  font-size: 30rpx;\n  font-weight: 600;\n"}