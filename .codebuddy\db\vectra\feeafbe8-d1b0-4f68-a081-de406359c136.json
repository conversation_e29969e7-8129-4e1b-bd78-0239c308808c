{"chunk": 0, "numChunks": 19, "fileHash": "PEPeuJm140RAD7VBd85WlnsfcWUdoorqVSQj8ISByl0=", "filePath": "pages/ai-chat/ai-chat.wxss", "content": "/* AI聊天界面 - 现代化设计 */\npage {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  height: 100vh;\n  overflow: hidden;\n}\n\n/* 页面容器 */\n.page-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  flex-direction: column;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n/* 顶部导航栏 */\n.header-bar {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20rpx);\n  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);\n  padding: 20rpx 32rpx;\n  padding-top: calc(20rpx + env(safe-area-inset-top));\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.header-left {\n  flex: 1;\n}\n\n.ai-status {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n}\n\n.status-dot {\n  width: 16rpx;\n  height: 16rpx;\n  border-radius: 50%;\n  background: #4ade80;\n  animation: pulse-dot 2s infinite;\n}\n\n.status-dot.typing {\n  background: #f59e0b;\n  animation: pulse-typing 1s infinite;\n}\n\n.status-text {\n  font-size: 28rpx;\n  color: #374151;\n  font-weight: 500;\n}\n\n.header-actions {\n  display: flex;\n  gap: 16rpx;\n}\n\n.action-btn {\n"}