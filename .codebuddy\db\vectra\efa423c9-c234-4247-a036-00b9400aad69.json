{"chunk": 0, "numChunks": 8, "fileHash": "pWWTyJooIxy6mFbGJzYtyes3pCjDoqcgXSX9h6jKjqM=", "filePath": "utils/bazi/calculator.js", "content": "// 天干\nconst HEAVENLY_STEMS = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];\n// 地支\nconst EARTHLY_BRANCHES = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];\n// 五行属性\nconst WUXING = {\n  metal: { name: '金', value: 0 },\n  wood: { name: '木', value: 0 },\n  water: { name: '水', value: 0 },\n  fire: { name: '火', value: 0 },\n  earth: { name: '土', value: 0 }\n};\n// 天干五行对应\nconst STEM_WUXING = {\n  '甲': 'wood', '乙': 'wood',\n  '丙': 'fire', '丁': 'fire',\n  '戊': 'earth', '己': 'earth',\n  '庚': 'metal', '辛': 'metal',\n  '壬': 'water', '癸': 'water'\n};\n// 地支五行对应\nconst BRANCH_WUXING = {\n  '子': 'water', '丑': 'earth',\n  '寅': 'wood', '卯': 'wood',\n  '辰': 'earth', '巳': 'fire',\n  '午': 'fire', '未': 'earth',\n  '申': 'metal', '酉': 'metal',\n  '戌': 'earth', '亥': 'water'\n};"}