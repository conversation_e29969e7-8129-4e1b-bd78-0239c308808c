{"chunk": 1, "numChunks": 7, "fileHash": "nGMyzD5kIPI75b8ATQIO6k03owW27BytPf8s1C7ATWc=", "filePath": "pages/bazi/index.wxss", "content": "  padding: 30rpx;\n  margin-bottom: 30rpx;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n  padding-bottom: 20rpx;\n  border-bottom: 2rpx solid #eee;\n}\n\n.bazi-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 20rpx;\n}\n\n.grid-item {\n  text-align: center;\n}\n\n.pillar-name {\n  font-size: 28rpx;\n  color: #666;\n  margin-bottom: 10rpx;\n  display: block;\n}\n\n.pillar-content {\n  background: #f8f8f8;\n  padding: 16rpx;\n  border-radius: 8rpx;\n}\n\n.pillar-content text {\n  display: block;\n  font-size: 26rpx;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.wuxing-analysis {\n  margin-top: 20rpx;\n}\n\n.wuxing-item {\n  margin-bottom: 20rpx;\n}\n\n.element-name {\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 10rpx;\n  display: block;\n}\n\n.interpretation {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n.fortune-analysis {\n  margin-top: 20rpx;\n}\n\n.fortune-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.aspect-name {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.stars {\n  color: #f0b90b;\n  font-size: 28rpx;\n"}