{"chunk": 5, "numChunks": 7, "fileHash": "Cqx4nGobpVxA67G0CO//E9qs4sv6sw0mA2CIPhYqZ4g=", "filePath": "subpages/divination/bazi/bazi.wxss", "content": "  gap: 20rpx;\n  margin-top: 40rpx;\n}\n\n.action-btn {\n  flex: 1;\n  height: 80rpx;\n  border-radius: 40rpx;\n  font-size: 28rpx;\n  border: none;\n  transition: all 0.3s ease;\n}\n\n.action-btn.primary {\n  background: linear-gradient(135deg, #8a2be2, #9932cc);\n  color: white;\n}\n\n.action-btn.secondary {\n  background: #f8f9fa;\n  color: #666;\n  border: 1rpx solid #e8e8e8;\n}\n\n.action-btn:active {\n  opacity: 0.8;\n  transform: scale(0.98);\n}\n\n/* 历史记录 */\n.history-section {\n  padding: 0 30rpx;\n  margin-top: 40rpx;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.section-more {\n  font-size: 26rpx;\n  color: #8a2be2;\n}\n\n.history-list {\n  background: white;\n  border-radius: 16rpx;\n  overflow: hidden;\n}\n\n.history-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 30rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n  transition: background 0.2s ease;\n}\n\n.history-item:last-child {\n  border-bottom: none;\n}\n\n.history-item:active {\n  background: #f8f9fa;\n}\n\n.history-info {\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.history-name {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.history-date {\n"}