{"chunk": 2, "numChunks": 5, "fileHash": "D8EtU/lQxzCzbNO7f7/rdYUVcSFrgRLNb6jQJPgoQwc=", "filePath": "subpages/divination/bazi/bazi.wxml", "content": "      <!-- 八字展示 -->\n      <view class=\"bazi-display\">\n        <view class=\"bazi-row\">\n          <view class=\"bazi-item\" wx:for=\"{{resultData.tianGan}}\" wx:key=\"index\">\n            <text class=\"bazi-label\">{{item.label}}</text>\n            <text class=\"bazi-char tian\">{{item.value}}</text>\n          </view>\n        </view>\n        <view class=\"bazi-row\">\n          <view class=\"bazi-item\" wx:for=\"{{resultData.diZhi}}\" wx:key=\"index\">\n            <text class=\"bazi-char di\">{{item.value}}</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 五行分析 -->\n      <view class=\"wuxing-section\">\n        <text class=\"section-title\">五行分析</text>\n        <view class=\"wuxing-chart\">\n          <view class=\"wuxing-item\" wx:for=\"{{resultData.wuxing}}\" wx:key=\"name\">\n            <view class=\"wuxing-bar\" style=\"height: {{item.percentage}}%\">\n              <text class=\"wuxing-value\">{{item.count}}</text>\n            </view>\n            <text class=\"wuxing-name\">{{item.name}}</text>\n          </view>\n        </view>\n        <text class=\"wuxing-desc\">{{resultData.wuxingDesc}}</text>\n      </view>\n\n      <!-- 命理解析 -->\n      <view class=\"analysis-section\">\n        <text class=\"section-title\">命理解析</text>\n        \n        <view class=\"analysis-item\">\n          <text class=\"analysis-label\">性格特征</text>\n          <text class=\"analysis-content\">{{resultData.personality}}</text>\n        </view>\n\n        <view class=\"analysis-item\">\n"}