{"chunk": 6, "numChunks": 16, "fileHash": "9ZF2KXbrmWumjo5LxJnE/F14ePFZLBzZ6HxD55jx3zE=", "filePath": "pages/fengshui/fengshui.js", "content": "// pages/fengshui/fengshui.js\nPage({\n  },\n  /**\n   * 生命周期函数--监听页面加载\n   */\n  onLoad(options) {\n    // 加载缓存数据\n    this.loadCachedData()\n  },\n  /**\n   * 生命周期函数--监听页面初次渲染完成\n   */\n  onReady() {\n\n  },\n  /**\n   * 生命周期函数--监听页面显示\n   */\n  onShow() {\n    // 加载缓存数据\n    this.loadCachedData()\n  },\n  /**\n   * 生命周期函数--监听页面隐藏\n   */\n  onHide() {\n\n  },\n  /**\n   * 生命周期函数--监听页面卸载\n   */\n  onUnload() {\n\n  },\n  /**\n   * 页面相关事件处理函数--监听用户下拉动作\n   */\n  onPullDownRefresh() {\n\n  },\n  /**\n   * 页面上拉触底事件的处理函数\n   */\n  onReachBottom() {\n\n  },\n  /**\n   * 用户点击右上角分享\n   */\n  onShareAppMessage() {\n    const { analysisResult } = this.data\n    \n    if (analysisResult) {\n      return {\n        title: `我的房屋风水分析结果：${analysisResult.overall}`,\n        path: '/pages/fengshui/fengshui',\n        imageUrl: '/assets/images/share-fengshui.jpg'\n      }\n    }\n    \n    return {\n      title: '风水布局 - 专业的居家风水分析',\n      path: '/pages/fengshui/fengshui',\n      imageUrl: '/assets/images/share-fengshui.jpg'\n    }\n  },\n  // 加载缓存数据"}