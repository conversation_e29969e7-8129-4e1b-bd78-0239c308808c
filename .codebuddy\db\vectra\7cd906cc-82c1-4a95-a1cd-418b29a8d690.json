{"chunk": 0, "numChunks": 5, "fileHash": "pt0uFokY5+WPeovdWLk6lUfTQUcY8vS6GhDpk14daRU=", "filePath": "pages/fengshui/index.wxml", "content": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<view class=\"container\">\n  <!-- 顶部banner -->\n  <view class=\"banner\">\n    <image class=\"banner-bg\" src=\"/assets/images/fengshui/banner-bg.jpg\" mode=\"aspectFill\"/>\n    <view class=\"banner-title\">\n      <text class=\"main-title\">风水布局</text>\n      <text class=\"sub-title\">趋吉避凶，改善运势</text>\n    </view>\n  </view>\n\n  <!-- 信息输入表单 -->\n  <view class=\"form-section\">\n    <view class=\"form-title\">房屋信息</view>\n    \n    <!-- 房屋类型 -->\n    <view class=\"form-item\">\n      <text class=\"label\">房屋类型</text>\n      <picker mode=\"selector\" range=\"{{houseTypes}}\" value=\"{{houseTypeIndex}}\" bindchange=\"onHouseTypeChange\">\n        <view class=\"picker {{houseTypeIndex > -1 ? '' : 'placeholder'}}\">\n          {{houseTypeIndex > -1 ? houseTypes[houseTypeIndex] : '请选择房屋类型'}}\n        </view>\n      </picker>\n    </view>\n\n    <!-- 房屋朝向 -->\n    <view class=\"form-item\">\n      <text class=\"label\">房屋朝向</text>\n      <picker mode=\"selector\" range=\"{{directions}}\" value=\"{{directionIndex}}\" bindchange=\"onDirectionChange\">\n        <view class=\"picker {{directionIndex > -1 ? '' : 'placeholder'}}\">\n          {{directionIndex > -1 ? directions[directionIndex] : '请选择房屋朝向'}}\n        </view>\n      </picker>\n    </view>\n\n    <!-- 建筑年份 -->\n    <view class=\"form-item\">\n"}