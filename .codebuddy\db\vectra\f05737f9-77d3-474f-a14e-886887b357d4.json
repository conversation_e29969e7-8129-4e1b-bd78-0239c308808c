{"chunk": 4, "numChunks": 5, "fileHash": "MvHilPs2FcNTeJtL9CKWbmgKe5i8pqMev8Y5RetE4QY=", "filePath": "frontend-implementation/pages/login/index.js", "content": "// 登录页面\nPage({\n  async wxLogin() {\n    try {\n      this.setData({ loading: true })\n      showLoading({ title: '登录中...' })\n\n      // 获取登录code\n      const loginRes = await wx.login()\n      if (!loginRes.code) {\n        throw new Error('获取登录code失败')\n      }\n\n      // 调用登录接口\n      const result = await wxLogin(loginRes.code, this.data.userInfo)\n      \n      if (result.status === 'success') {\n        // 保存登录信息\n        setStorageSync('token', result.data.token)\n        setStorageSync('refreshToken', result.data.refresh_token)\n        setStorageSync('userInfo', result.data.user)\n\n        showToast({\n          title: '登录成功',\n          icon: 'success'\n        })\n\n        // 跳转到首页\n        setTimeout(() => {\n          wx.switchTab({\n            url: '/pages/index/index'\n          })\n        }, 1500)\n      } else {\n        throw new Error(result.message || '登录失败')\n      }\n    } catch (error) {\n      console.error('登录失败:', error)\n      showToast({\n        title: error.message || '登录失败',\n        icon: 'none'\n      })\n    } finally {\n      this.setData({ loading: false })\n      hideLoading()\n    }\n  },\n  /**\n   * 获取用户信息\n   */\n  getUserProfile() {\n    wx.getUserProfile({\n      desc: '用于完善用户资料',\n      success: (res) => {\n        this.setData({\n          userInfo: res.userInfo,\n          hasUserInfo: true\n        })\n        // 获取用户信息后自动登录\n        this.wxLogin()\n      },\n      fail: (error) => {\n        console.error('获取用户信息失败:', error)\n        showToast({\n          title: '需要授权才能使用',\n          icon: 'none'\n        })\n      }\n    })\n  },"}