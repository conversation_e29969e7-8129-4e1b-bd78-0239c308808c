{"chunk": 0, "numChunks": 3, "fileHash": "grwM+uqFMSn0OFGeZxTcU6dtA4Dxde56TBRIWq2COXA=", "filePath": "pages/community/index.wxss", "content": "/* pages/community/index.wxss */\n.container {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  position: relative;\n}\n\n/* 分类导航 */\n.category-nav {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background-color: #fff;\n  padding: 20rpx 0;\n  white-space: nowrap;\n  border-bottom: 2rpx solid #f0f0f0;\n}\n\n.category-item {\n  display: inline-block;\n  padding: 12rpx 30rpx;\n  margin: 0 10rpx;\n  font-size: 28rpx;\n  color: #666;\n  border-radius: 30rpx;\n  transition: all 0.3s;\n}\n\n.category-item.active {\n  color: #07c160;\n  background-color: #e8f7ed;\n}\n\n/* 帖子列表 */\n.post-list {\n  margin-top: 100rpx;\n  padding: 20rpx;\n}\n\n.post-item {\n  background-color: #fff;\n  border-radius: 12rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n}\n\n/* 用户信息 */\n.post-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.user-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 40rpx;\n  margin-right: 20rpx;\n}\n\n.user-info {\n  flex: 1;\n}\n\n.username {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n  margin-bottom: 6rpx;\n  display: block;\n}\n\n.post-time {\n  font-size: 24rpx;\n"}