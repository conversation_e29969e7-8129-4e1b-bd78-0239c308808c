{"chunk": 0, "numChunks": 1, "fileHash": "KnAwkpZRZn3Duxp8+3LLgTl1XIpsMdt5H2F7xdrnHSE=", "filePath": "subpages/admin/logs/logs.wxml", "content": "<!--subpages/admin/logs/logs.wxml-->\r\n<view class=\"logs-container\">\r\n  <view class=\"logs-header\">\r\n    <text class=\"logs-title\">系统日志</text>\r\n    <view class=\"logs-filters\">\r\n      <button \r\n        wx:for=\"{{filters}}\" \r\n        wx:key=\"key\"\r\n        class=\"filter-btn {{activeFilter === item.key ? 'active' : ''}}\"\r\n        data-filter=\"{{item.key}}\"\r\n        bindtap=\"filterLogs\"\r\n      >\r\n        {{item.name}}\r\n      </button>\r\n    </view>\r\n  </view>\r\n  \r\n  <view class=\"logs-content\">\r\n    <view class=\"logs-list\">\r\n      <view wx:for=\"{{filteredLogs}}\" wx:key=\"id\" class=\"log-item {{item.level}}\">\r\n        <view class=\"log-info\">\r\n          <text class=\"log-time\">{{item.time}}</text>\r\n          <text class=\"log-level\">{{item.level === 'error' ? 'ERROR' : item.level === 'warning' ? 'WARN' : 'INFO'}}</text>\r\n        </view>\r\n        <text class=\"log-message\">{{item.message}}</text>\r\n        <text class=\"log-source\">{{item.source}}</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <view wx:if=\"{{filteredLogs.length === 0}}\" class=\"empty-logs\">\r\n      <text class=\"empty-text\">暂无日志记录</text>\r\n    </view>\r\n  </view>\r\n  \r\n  <view class=\"logs-actions\">\r\n    <button class=\"action-btn secondary\" bindtap=\"clearLogs\">清理日志</button>\r\n    <button class=\"action-btn primary\" bindtap=\"exportLogs\">导出日志</button>\r\n  </view>\r\n</view> "}