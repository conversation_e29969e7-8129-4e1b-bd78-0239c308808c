{"chunk": 5, "numChunks": 9, "fileHash": "43tyNMTA9IQs33epLq7vkl5MUCocqW9TjhFCqLTPBOQ=", "filePath": "frontend-implementation/store/index.js", "content": "// 全局状态管理\nclass Store {\n  subscribe(path, callback) {\n    if (!this.listeners.has(path)) {\n      this.listeners.set(path, new Set())\n    }\n    \n    this.listeners.get(path).add(callback)\n    \n    // 返回取消订阅函数\n    return () => {\n      const pathListeners = this.listeners.get(path)\n      if (pathListeners) {\n        pathListeners.delete(callback)\n        if (pathListeners.size === 0) {\n          this.listeners.delete(path)\n        }\n      }\n    }\n  }\n  /**\n   * 通知监听器\n   * @param {string} path 状态路径\n   * @param {any} newValue 新值\n   * @param {any} oldValue 旧值\n   */\n  notifyListeners(path, newValue, oldValue) {\n    // 通知精确路径的监听器\n    const pathListeners = this.listeners.get(path)\n    if (pathListeners) {\n      pathListeners.forEach(callback => {\n        try {\n          callback(newValue, oldValue, path)\n        } catch (error) {\n          console.error('状态监听器执行错误:', error)\n        }\n      })\n    }\n    \n    // 通知父路径的监听器\n    const pathParts = path.split('.')\n    for (let i = pathParts.length - 1; i > 0; i--) {\n      const parentPath = pathParts.slice(0, i).join('.')\n      const parentListeners = this.listeners.get(parentPath)\n      if (parentListeners) {\n        const parentValue = this.getState(parentPath)\n        parentListeners.forEach(callback => {\n          try {\n            callback(parentValue, parentValue, parentPath)\n          } catch (error) {\n            console.error('父路径状态监听器执行错误:', error)\n          }\n        })\n      }\n    }\n  }"}