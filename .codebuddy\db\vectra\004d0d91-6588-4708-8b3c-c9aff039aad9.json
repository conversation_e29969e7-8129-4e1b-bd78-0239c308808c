{"chunk": 8, "numChunks": 12, "fileHash": "HkVDVZ9giUC0NxXwGhnv0R1SvQ+r07w6mr3mR/NNt+s=", "filePath": "subpages/divination/bazi/bazi.js", "content": "Page({\n  calculateBazi() {\n    result.wuxing.forEach(item => {\n      item.percentage = (item.count / totalWuxing) * 100\n    })\n    // 找出最多和最少的五行\n    const maxWuxing = result.wuxing.reduce((max, item) => item.count > max.count ? item : max)\n    const minWuxing = result.wuxing.reduce((min, item) => item.count < min.count ? item : min)\n    result.wuxingDesc = `您的五行中${maxWuxing.name}最旺，${minWuxing.name}较弱。建议在生活中适当补充${minWuxing.name}元素，以达到五行平衡。`\n    // 生成性格分析\n    const personalities = [\n      '您性格温和，待人诚恳，具有很强的责任心和进取心。',\n      '您聪明机智，思维敏捷，善于把握机会，具有领导才能。',\n      '您性格坚毅，做事踏实，有耐心和毅力，能够坚持到底。',\n      '您性格开朗，乐观向上，人缘极佳，善于与人交往。'\n    ]\n    result.personality = personalities[Math.floor(Math.random() * personalities.length)]\n    // 生成事业分析"}