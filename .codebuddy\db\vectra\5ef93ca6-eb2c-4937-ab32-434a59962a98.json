{"chunk": 6, "numChunks": 7, "fileHash": "PQSUOKQzQ1nTJojwaszEOU93ZW43HSbpVKnnb8k/pfY=", "filePath": "pages/marriage/index.wxss", "content": "  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 8rpx 16rpx;\n  border-radius: 16rpx;\n  font-size: 24rpx;\n  font-weight: 500;\n}\n\n.detail-content {\n  margin-bottom: 24rpx;\n}\n\n.detail-item {\n  margin-bottom: 20rpx;\n  padding: 20rpx;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e8e8e8 100%);\n  border-radius: 16rpx;\n  border-left: 4rpx solid #667eea;\n}\n\n.detail-item:last-child {\n  margin-bottom: 0;\n}\n\n.detail-title {\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 12rpx;\n}\n\n.detail-desc {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n/* 建议区域 */\n.suggestions {\n  margin-top: 16rpx;\n}\n\n.suggestion-item {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 16rpx;\n  font-size: 28rpx;\n  color: #333;\n  line-height: 1.5;\n}\n\n.suggestion-item:last-child {\n  margin-bottom: 0;\n}\n\n.suggestion-dot {\n  color: #667eea;\n  margin-right: 12rpx;\n  font-weight: bold;\n  margin-top: 2rpx;\n}\n\n.suggestion-text {\n  flex: 1;\n}\n"}