{"chunk": 11, "numChunks": 38, "fileHash": "DpyinnhZJO6jrs0/VY0BXwrNjORlEQ1BeRywBJybhxQ=", "filePath": "pages/ai-chat/ai-chat.js", "content": "const app = getApp()\nPage({\n  /**\n   * 选择快捷回复\n   */\n  selectQuickReply(e) {\n    const { text } = e.currentTarget.dataset\n    this.setData({\n      inputValue: text,\n      quickReplies: []\n    })\n  },\n  /**\n   * 输入框内容变化处理\n   */\n  onInputChange(e) {\n    this.setData({\n      inputValue: e.detail.value\n    })\n  },\n  /**\n   * 发送消息\n   */\n  async sendMessage() {\n    const { inputValue } = this.data\n    if (!inputValue.trim()) {\n      this.setData({ isInputExpanded: false })\n      return\n    }\n\n    // 添加用户消息\n    this.addMessage({\n      type: 'user',\n      content: inputValue,\n      timestamp: this.formatTime(new Date())\n    })\n\n    this.setData({\n      inputValue: '', \n      showQuickActions: false,\n      isInputExpanded: false\n    })\n\n    this.scrollToBottom()\n    this.setData({ isTyping: true })\n    \n    try {\n      // 获取AI回复内容\n      const responses = await this.mockAIResponse(inputValue)\n      // 依次展示回复\n      await this.typeMessage(responses)\n    } catch (error) {\n      console.error('AI回复失败:', error)\n      this.handleError(error)\n    } finally {\n      this.setData({ isTyping: false })\n    }\n  },\n  /**\n   * 处理快捷操作点击\n   */"}