{"chunk": 17, "numChunks": 19, "fileHash": "PEPeuJm140RAD7VBd85WlnsfcWUdoorqVSQj8ISByl0=", "filePath": "pages/ai-chat/ai-chat.wxss", "content": "  animation: typing-fade-in 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.typing-avatar {\n  width: 72rpx;\n  height: 72rpx;\n  background: var(--gradient-primary);\n  color: white;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 32rpx;\n  margin-right: 20rpx;\n  box-shadow: 0 6rpx 20rpx rgba(121, 40, 202, 0.3);\n}\n\n.typing-content {\n  background: var(--card-background);\n  padding: 24rpx 32rpx;\n  border-radius: 24rpx 24rpx 24rpx 8rpx;\n  box-shadow: 0 6rpx 20rpx var(--shadow-color);\n  border: 1rpx solid var(--border-color);\n  display: flex;\n  align-items: center;\n}\n\n.typing-dots {\n  display: flex;\n  gap: 10rpx;\n  margin-right: 20rpx;\n}\n\n.typing-dots .dot {\n  width: 8rpx;\n  height: 8rpx;\n  border-radius: 50%;\n  background: var(--primary-color);\n  animation: typing-dot 1.5s infinite ease-in-out;\n}\n\n.typing-dots .dot:nth-child(2) {\n  animation-delay: 0.2s;\n}\n\n.typing-dots .dot:nth-child(3) {\n  animation-delay: 0.4s;\n}\n\n.typing-text {\n  font-size: 26rpx;\n  color: var(--text-secondary);\n  font-style: italic;\n}\n\n/* 优化动画效果 */\n@keyframes message-slide-in {\n"}