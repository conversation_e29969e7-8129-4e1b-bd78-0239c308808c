{"chunk": 4, "numChunks": 13, "fileHash": "RdYUonhiW47SLv0/ZKSqV2JJEqqe4g6tc2PZPdqonRM=", "filePath": "pages/post-detail/post-detail.js", "content": "const app = getApp()\nPage({\n  async loadPostDetail(postId) {\n    wx.showLoading({\n      title: '加载中...',\n      mask: true\n    })\n    \n    try {\n      const db = wx.cloud.database()\n      const postResult = await db.collection('posts').doc(postId).get()\n      \n      if (!postResult.data) {\n        wx.hideLoading()\n        wx.showToast({\n          title: '帖子不存在',\n          icon: 'none'\n        })\n        setTimeout(() => {\n          wx.navigateBack()\n        }, 1500)\n        return\n      }\n      \n      // 格式化时间\n      const post = postResult.data\n      post.createTime = new Date(post.createTime).toLocaleString()\n      if (post.updateTime) {\n        post.updateTime = new Date(post.updateTime).toLocaleString()\n      }\n      \n      // 更新浏览量\n      await db.collection('posts').doc(postId).update({\n        data: {\n          views: db.command.inc(1)\n        }\n      })\n      \n      this.setData({ post })\n      wx.hideLoading()\n    } catch (error) {\n      console.error('获取帖子详情失败:', error)\n      wx.hideLoading()\n      wx.showToast({\n        title: '获取帖子详情失败',\n        icon: 'none'\n      })\n    }\n  },\n  /**\n   * 检查用户是否已点赞\n   */"}