{"chunk": 5, "numChunks": 55, "fileHash": "fCZ6nFoNZXQqhvADUzMwGWD6jOU+ajj61B8BD3CwDgs=", "filePath": "pages/name-test/name-test.js", "content": "// 姓名测试页面逻辑\nPage({\n  async analyzeNameFortune(surname, givenName, gender) {\n    // 获取字符笔画数\n    const surnameStrokes = this.getCharacterStrokes(surname);\n    const givenNameStrokes = this.getCharacterStrokes(givenName);\n    \n    // 计算五格数理\n    const wuge = this.calculateWuge(surnameStrokes, givenNameStrokes);\n    \n    // 计算三才配置\n    const sancai = this.calculateSancai(wuge);\n    \n    // 生成详细分析\n    const analysis = this.generateAnalysis(wuge, sancai, gender);\n    \n    // 计算总分\n    const totalScore = this.calculateTotalScore(wuge, sancai);\n    \n    // 生成改名建议（如果分数较低）\n    const suggestions = totalScore < 70 ? this.generateSuggestions(surname, gender) : null;\n\n    return {\n      fullName: surname + givenName,\n      totalScore,\n      wuge,\n      sancai,\n      analysis,\n      suggestions\n    };\n  },\n  // 获取字符笔画数（简化版本）"}