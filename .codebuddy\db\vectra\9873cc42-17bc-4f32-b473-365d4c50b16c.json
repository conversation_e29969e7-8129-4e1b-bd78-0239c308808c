{"chunk": 6, "numChunks": 10, "fileHash": "zs0lx95Rie79XClG/B2xJnr9ZnTEU5TU8Cf7eq2kuOY=", "filePath": "frontend-implementation/pages/birth-info/index.js", "content": "// 出生信息页面\nPage({\n  onGenderChange(e) {\n    const gender = this.data.genderOptions[e.detail.value]\n    this.setData({\n      'formData.gender': gender,\n      'errors.gender': null\n    })\n  },\n  /**\n   * 日期选择\n   */\n  onDateChange(e) {\n    const { field } = e.currentTarget.dataset\n    const { value } = e.detail\n    \n    this.setData({\n      [`formData.${field}`]: parseInt(value),\n      [`errors.${field}`]: null\n    })\n    \n    // 如果是年月日变化，重新计算农历和其他信息\n    if (['birth_year', 'birth_month', 'birth_day'].includes(field)) {\n      this.calculateAdditionalInfo()\n    }\n  },\n  /**\n   * 时间选择\n   */\n  onTimeChange(e) {\n    const { value } = e.detail\n    const [hour, minute] = value.split(':').map(Number)\n    \n    this.setData({\n      'formData.birth_hour': hour,\n      'formData.birth_minute': minute,\n      'errors.birth_hour': null,\n      'errors.birth_minute': null\n    })\n  },\n  /**\n   * 计算附加信息（农历、生肖、星座等）\n   */"}