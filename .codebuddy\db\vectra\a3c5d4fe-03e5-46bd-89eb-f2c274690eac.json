{"chunk": 0, "numChunks": 7, "fileHash": "Cqx4nGobpVxA67G0CO//E9qs4sv6sw0mA2CIPhYqZ4g=", "filePath": "subpages/divination/bazi/bazi.wxss", "content": "/* subpages/divination/bazi/bazi.wxss */\n\n.bazi-container {\n  min-height: 100vh;\n  background: #f5f7fa;\n  padding-bottom: 40rpx;\n}\n\n/* 页面头部 */\n.bazi-header {\n  position: relative;\n  height: 320rpx;\n  overflow: hidden;\n}\n\n.header-bg {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  top: 0;\n  left: 0;\n}\n\n.header-content {\n  position: relative;\n  height: 100%;\n  background: linear-gradient(135deg, rgba(138, 43, 226, 0.8), rgba(153, 50, 204, 0.8));\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  color: white;\n}\n\n.header-title {\n  font-size: 48rpx;\n  font-weight: bold;\n  margin-bottom: 16rpx;\n}\n\n.header-subtitle {\n  font-size: 28rpx;\n  opacity: 0.9;\n}\n\n/* 输入表单 */\n.bazi-form {\n  padding: 30rpx;\n  margin-top: -60rpx;\n  position: relative;\n  z-index: 1;\n}\n\n.form-card {\n  background: white;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);\n}\n\n.form-title {\n  text-align: center;\n  margin-bottom: 40rpx;\n}\n\n.title-text {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.title-tips {\n"}