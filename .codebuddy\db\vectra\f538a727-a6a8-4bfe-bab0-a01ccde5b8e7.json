{"chunk": 54, "numChunks": 76, "fileHash": "lPYWZE8QItgwSyEsMXHtl6y/HYBCI8BdGaV+1l8ICMA=", "filePath": "docs/md/API_INTERFACE_DOCUMENTATION.md", "content": "- page: number (页码，默认1)\n- limit: number (每页数量，默认20)\n\nResponse:\n{\n  \"status\": \"success|error\",\n  \"message\": \"string\",\n  \"data\": {\n    \"records\": [\n      {\n        \"id\": \"string\",\n        \"type\": \"string\",\n        \"amount\": \"number\",\n        \"source\": \"string\",\n        \"description\": \"string\",\n        \"balance_after\": \"number\",\n        \"created_at\": \"datetime\"\n      }\n    ],\n    \"pagination\": {\n      \"current_page\": \"number\",\n      \"total_pages\": \"number\",\n      \"total_count\": \"number\"\n    },\n    \"summary\": {\n      \"total_earned\": \"number\",\n      \"total_spent\": \"number\",\n      \"net_change\": \"number\"\n    }\n  }\n}\n```\n\n#### 3. 签到获取积分\n```\nPOST /api/points/sign-in\nAuthorization: Bearer {token}\n\nResponse:\n{\n  \"status\": \"success|error\",\n  \"message\": \"string\",\n  \"data\": {\n    \"points_earned\": \"number\",      // 获得积分\n    \"consecutive_days\": \"number\",   // 连续签到天数\n    \"total_sign_days\": \"number\",    // 总签到天数\n    \"next_reward\": {                // 下次奖励\n      \"days_needed\": \"number\",      // 还需天数\n      \"reward_points\": \"number\"     // 奖励积分\n    },\n    \"current_balance\": \"number\"     // 当前余额\n  }\n}\n```\n\n#### 4. 分享获取积分\n```\nPOST /api/points/share\nAuthorization: Bearer {token}\nContent-Type: application/json\n\nRequest:\n{\n  \"share_type\": \"string\",           // 分享类型 (analysis/app/article)\n"}