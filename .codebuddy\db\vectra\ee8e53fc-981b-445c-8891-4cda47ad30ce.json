{"chunk": 17, "numChunks": 22, "fileHash": "jr30LZOMc1P5VIpMptyaGnan9xv44m/c09rHA5aMeTY=", "filePath": "pages/index/index.js", "content": "// index.js\nPage({\n  navigateTo(url) {\n    try {\n      // 使用统一的导航管理器\n      navigationManager.navigateTo(url, {\n        success: () => {\n          console.log('页面跳转成功:', url)\n        },\n        fail: (error) => {\n          errorHandler.handleNavigationError(error)\n        }\n      })\n    } catch (error) {\n      console.error('页面跳转异常:', error)\n      errorHandler.handle(error, {\n        customMessage: '页面跳转失败，请重试'\n      })\n    }\n  },\n  onGridItemTap(e) {\n    const url = e.currentTarget.dataset.url\n    const type = e.currentTarget.dataset.type\n    \n    if (type) {\n      this.recordUsage(type)\n    }\n    \n    this.navigateTo(url)\n  },\n  onServiceItemTap(e) {\n    const url = e.currentTarget.dataset.url\n    const type = e.currentTarget.dataset.type\n    \n    if (type) {\n      this.recordUsage(type)\n    }\n    \n    console.log('服务项点击，准备跳转到:', url)\n    this.navigateTo(url)\n  },\n  onShareAppMessage() {\n    return {\n      title: '恒琦易道 - 您的命理顾问',\n      path: '/pages/index/index',\n      imageUrl: '/assets/images/share.jpg'\n    }\n  },\n  bindViewTap() {\n    this.navigateTo('../logs/logs')\n  },\n  onChooseAvatar(e) {\n    try {\n    const { avatarUrl } = e.detail\n    const { nickName } = this.data.userInfo\n    this.setData({\n      \"userInfo.avatarUrl\": avatarUrl,\n      hasUserInfo: nickName && avatarUrl && avatarUrl !== defaultAvatarUrl,\n    })\n    } catch (error) {\n      console.error('选择头像失败:', error)\n    }\n  },"}