{"chunk": 1, "numChunks": 2, "fileHash": "O0rNisqoP8E08RCfNEYLUpjs3X2a9r5sxFL1BC9wbMo=", "filePath": "pages/my/my.wxss", "content": "  margin-right: 24rpx;\n  opacity: 0.8;\n}\n\n.function-name {\n  flex: 1;\n  font-size: 30rpx;\n  color: var(--text-primary);\n}\n\n.arrow-right {\n  width: 32rpx;\n  height: 32rpx;\n  opacity: 0.6;\n}\n\n/* 版本信息 */\n.version-info {\n  text-align: center;\n  padding: 32rpx 0;\n  color: var(--text-light);\n  font-size: 26rpx;\n}\n\n/* 退出登录按钮 */\n.logout-btn {\n  background: var(--primary-color);\n  color: #FFFFFF;\n  text-align: center;\n  padding: 24rpx 0;\n  border-radius: 8rpx;\n  margin: 48rpx 32rpx;\n  font-size: 32rpx;\n  box-shadow: 0 4rpx 8rpx var(--shadow-color);\n}\n\n/* 功能分组标题 */\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: var(--primary-color);\n  margin: 32rpx 0 16rpx;\n  padding-left: 16rpx;\n  border-left: 8rpx solid var(--primary-color);\n} \n"}