{"chunk": 2, "numChunks": 5, "fileHash": "lIcWOgwH6HogZZCgkr5fpW32d4QAA+3E2RZGYWvnfwk=", "filePath": "pages/xingming/index.wxss", "content": ".analysis-card {\n  background: #fff;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);\n}\n\n.card-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 24rpx;\n  gap: 12rpx;\n}\n\n.title-icon {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n/* 评分展示 */\n.score-display {\n  display: flex;\n  align-items: baseline;\n  justify-content: center;\n  margin: 30rpx 0;\n}\n\n.score {\n  font-size: 80rpx;\n  font-weight: bold;\n  color: #FF69B4;\n}\n\n.max-score {\n  font-size: 32rpx;\n  color: #999;\n  margin-left: 8rpx;\n}\n\n.score-desc {\n  text-align: center;\n  font-size: 28rpx;\n  color: #666;\n}\n\n/* 五格数理 */\n.wuge-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 20rpx;\n  margin-top: 20rpx;\n}\n\n.wuge-item {\n  background: #F8F9FD;\n  border-radius: 12rpx;\n  padding: 20rpx;\n}\n\n.wuge-name {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: bold;\n  margin-bottom: 12rpx;\n  display: block;\n}\n\n.wuge-number {\n  font-size: 40rpx;\n  color: #FF69B4;\n  font-weight: bold;\n  margin-bottom: 12rpx;\n  display: block;\n}\n\n.wuge-desc {\n"}