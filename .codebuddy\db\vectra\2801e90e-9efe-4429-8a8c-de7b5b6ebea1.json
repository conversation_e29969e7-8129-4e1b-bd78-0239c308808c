{"chunk": 2, "numChunks": 5, "fileHash": "4lpNnoqGg1eEPtIgkdLgszOng5dzR4kuUtSZ8HRwytk=", "filePath": "pages/yijing/yijing.wxss", "content": "  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\n  color: #FFFFFF;\n  font-size: 32rpx;\n  border-radius: 45rpx;\n  margin: 40rpx auto;\n  box-shadow: 0 6rpx 16rpx var(--shadow-color);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-icon {\n  margin-right: 10rpx;\n  font-size: 36rpx;\n}\n\n/* 结果显示区域样式 */\n.result-section {\n  margin-bottom: 40rpx;\n}\n\n/* 卦象显示样式 */\n.hexagram-card {\n  margin: 30rpx 0;\n  padding: 30rpx;\n  background-color: var(--primary-lightest);\n  border-radius: 12rpx;\n  border: 2rpx solid var(--border-color);\n}\n\n.hexagram-header {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 30rpx;\n}\n\n.hexagram-title {\n  font-size: 28rpx;\n  color: var(--text-secondary);\n  margin-right: 15rpx;\n}\n\n.hexagram-name {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: var(--primary-color);\n}\n\n.hexagram-lines {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 15rpx;\n  margin: 30rpx 0;\n}\n\n.line {\n  width: 240rpx;\n  height: 40rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.line.yang {\n  background-color: var(--primary-color);\n  border-radius: 4rpx;\n}\n\n.line.yin {\n  background-color: transparent;\n"}