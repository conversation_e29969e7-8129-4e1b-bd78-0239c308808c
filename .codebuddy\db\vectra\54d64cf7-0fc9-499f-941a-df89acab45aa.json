{"chunk": 0, "numChunks": 5, "fileHash": "D8EtU/lQxzCzbNO7f7/rdYUVcSFrgRLNb6jQJPgoQwc=", "filePath": "subpages/divination/bazi/bazi.wxml", "content": "<!--subpages/divination/bazi/bazi.wxml-->\n<view class=\"bazi-container\">\n  <!-- 页面头部 -->\n  <view class=\"bazi-header\">\n    <image class=\"header-bg\" src=\"/images/bazi-bg.jpg\" mode=\"aspectFill\"></image>\n    <view class=\"header-content\">\n      <text class=\"header-title\">八字分析</text>\n      <text class=\"header-subtitle\">解析您的生辰八字，洞悉命运玄机</text>\n    </view>\n  </view>\n\n  <!-- 输入表单 -->\n  <view class=\"bazi-form\">\n    <view class=\"form-card\">\n      <view class=\"form-title\">\n        <text class=\"title-text\">请输入您的出生信息</text>\n        <text class=\"title-tips\">精确的时间有助于更准确的分析</text>\n      </view>\n\n      <view class=\"form-group\">\n        <text class=\"form-label\">姓名</text>\n        <input class=\"form-input\" placeholder=\"请输入您的姓名\" bindinput=\"onNameInput\" value=\"{{name}}\" />\n      </view>\n\n      <view class=\"form-group\">\n        <text class=\"form-label\">性别</text>\n        <view class=\"gender-select\">\n          <view class=\"gender-option {{gender === 'male' ? 'active' : ''}}\" bindtap=\"selectGender\" data-gender=\"male\">\n            <text class=\"gender-icon\">♂</text>\n            <text>男</text>\n          </view>\n          <view class=\"gender-option {{gender === 'female' ? 'active' : ''}}\" bindtap=\"selectGender\" data-gender=\"female\">\n            <text class=\"gender-icon\">♀</text>\n            <text>女</text>\n          </view>\n        </view>\n"}