{"chunk": 4, "numChunks": 18, "fileHash": "/DUygIsVyFRM4gmsdi8uiMJN8RFlyElKDKTgRRrPFCI=", "filePath": "utils/hehun/calculator.js", "content": "// 五行生克关系\nclass HehunCalculator {\n  constructor(maleBazi, femaleBazi) {\n    this.maleBazi = maleBazi\n    this.femaleBazi = femaleBazi\n    this.wuxingRelations = WUXING_RELATIONS;\n    this.stemCombinations = STEM_COMBINATIONS;\n    this.branchCombinations = BRANCH_COMBINATIONS;\n  }\n  async calculate() {\n    try {\n      // 1. 计算八字相合度\n      const baziScore = this.calculateBaziScore()\n      \n      // 2. 计算五行相合度\n      const wuxingScore = this.calculateWuxingScore()\n      \n      // 3. 分析相生相克\n      const relationScore = this.calculateRelationScore()\n      \n      // 4. 计算最终得分\n      const totalScore = Math.round((baziScore + wuxingScore + relationScore) / 3)\n      \n      // 5. 生成分析结果\n      const result = {\n        score: totalScore,\n        description: this.getScoreDescription(totalScore),\n        maleBaziStr: this.formatBaziString(this.maleBazi),\n        femaleBaziStr: this.formatBaziString(this.femaleBazi),\n        baziAnalysis: this.generateBaziAnalysis(),\n        wuxingAnalysis: this.generateWuxingAnalysis(),\n        wuxingData: this.generateWuxingChartData(),\n        adviceList: this.generateAdvice(totalScore)\n      }\n\n      return result\n    } catch (error) {\n      console.error('合婚计算错误:', error)\n      throw error\n    }\n  }\n  // 计算八字相合度"}