{"chunk": 4, "numChunks": 6, "fileHash": "CxWKtdq0I3NaiNwiPJTyYstBQKlC237Mnt9cmUNUZpE=", "filePath": "pages/fengshui/index.js", "content": "const app = getApp()\nPage({\n  onDirectionChange(e) {\n    this.setData({\n      directionIndex: e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 建筑年份选择\n  onBuildYearChange(e) {\n    this.setData({\n      buildYear: e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 面积输入\n  onAreaInput(e) {\n    this.setData({\n      area: e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 户型选择\n  onRoomNumChange(e) {\n    this.setData({\n      roomNumIndex: e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  onHallNumChange(e) {\n    this.setData({\n      hallNumIndex: e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  onBathNumChange(e) {\n    this.setData({\n      bathNumIndex: e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 检查是否可以提交\n  checkCanSubmit() {\n    const { \n      houseTypeIndex, directionIndex, buildYear, area,\n      roomNumIndex, hallNumIndex, bathNumIndex \n    } = this.data\n\n    const canSubmit = \n      houseTypeIndex !== null && \n      directionIndex !== null && \n      buildYear && \n      area && \n      roomNumIndex > -1 && \n      hallNumIndex > -1 && \n      bathNumIndex > -1\n\n    this.setData({ canSubmit })\n  },\n  // 开始分析"}