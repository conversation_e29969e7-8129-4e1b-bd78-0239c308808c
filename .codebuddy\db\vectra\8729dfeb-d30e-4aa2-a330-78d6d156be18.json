{"chunk": 3, "numChunks": 15, "fileHash": "ydfcj+4tZs2HvARUdw6TErn3fg1suYaeqU0s+F1D6wI=", "filePath": "pages/hehun/index.js", "content": "const app = getApp()\nPage({\n  data: {\n    // 基本信息\n    male: {\n      name: '',\n      birthDate: '',\n      birthTime: ''\n    },\n    female: {\n      name: '',\n      birthDate: '',\n      birthTime: ''\n    },\n    canSubmit: false,\n    price: 38,\n\n    // 结果展示\n    showResult: false,\n    matchScore: 0,\n    matchDesc: '',\n    maleBazi: '',\n    femaleBazi: '',\n    baziAnalysis: '',\n    wuxingAnalysis: '',\n    adviceList: [],\n    maleBirthDate: '',\n    maleBirthTime: '',\n    femaleBirthDate: '',\n    femaleBirthTime: '',\n    loading: false,\n    maleResult: {\n      pillars: []\n    },\n    femaleResult: {\n      pillars: []\n    },\n    marriageResult: {\n      wuxing: [],\n      score: 0,\n      aspects: [],\n      advice: ''\n    }\n  },\n  onLoad() {\n    // 初始化五行图表\n    this.wuxingChart = new WuxingChart()\n  },\n  // 男方信息输入处理\n  onMaleNameInput(e) {\n    this.setData({\n      'male.name': e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  onMaleDateChange(e) {\n    this.setData({\n      'male.birthDate': e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  onMaleTimeChange(e) {\n    this.setData({\n      'male.birthTime': e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 女方信息输入处理\n  onFemaleNameInput(e) {\n    this.setData({\n      'female.name': e.detail.value\n    })\n    this.checkCanSubmit()\n  },"}