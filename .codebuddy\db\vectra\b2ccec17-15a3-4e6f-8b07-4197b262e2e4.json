{"chunk": 14, "numChunks": 15, "fileHash": "ydfcj+4tZs2HvARUdw6TErn3fg1suYaeqU0s+F1D6wI=", "filePath": "pages/hehun/index.js", "content": "const app = getApp()\nPage({\n  },\n  validateInput() {\n    if (!this.data.maleBirthDate || !this.data.maleBirthTime) {\n      wx.showToast({\n        title: '请完善男方出生信息',\n        icon: 'none'\n      });\n      return false;\n    }\n    if (!this.data.femaleBirthDate || !this.data.femaleBirthTime) {\n      wx.showToast({\n        title: '请完善女方出生信息',\n        icon: 'none'\n      });\n      return false;\n    }\n    return true;\n  },"}