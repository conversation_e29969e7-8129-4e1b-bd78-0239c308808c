{"chunk": 2, "numChunks": 5, "fileHash": "IzNtCZVw1h+lk+yR+AP9FPNmtDYd4TsMmzGYHb/6Ia8=", "filePath": "pages/hehun/index.wxss", "content": "  color: #666;\n}\n\n.score-details {\n  margin-top: 30rpx;\n}\n\n.score-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.aspect-name {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.stars {\n  color: #f0b90b;\n  font-size: 28rpx;\n}\n\n.advice {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n/* 顶部banner */\n.banner {\n  position: relative;\n  width: 100%;\n  height: 360rpx;\n  overflow: hidden;\n}\n\n.banner-bg {\n  width: 100%;\n  height: 100%;\n}\n\n.banner-title {\n  position: absolute;\n  left: 40rpx;\n  bottom: 40rpx;\n  color: #fff;\n  z-index: 1;\n}\n\n.main-title {\n  font-size: 48rpx;\n  font-weight: bold;\n  display: block;\n  margin-bottom: 12rpx;\n  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);\n}\n\n.sub-title {\n  font-size: 28rpx;\n  opacity: 0.9;\n}\n\n/* 表单区域 */\n.form-section {\n  margin: 30rpx;\n}\n\n.male .section-title {\n  color: #4A90E2;\n}\n\n.female .section-title {\n  color: #FF69B4;\n}\n\n.submit-section {\n  margin: 30rpx;\n}\n\n.submit-btn {\n  width: 100%;\n  height: 88rpx;\n  background: linear-gradient(135deg, #FF69B4 0%, #4A90E2 100%);\n"}