{"chunk": 66, "numChunks": 74, "fileHash": "UWh+hTmku0OFKrrOI8FY7Ob/w0LwUBFFaP/YfXcJBpg=", "filePath": "utils/xingming/calculator.js", "content": "// 汉字笔画数据库\nclass XingmingCalculator {\n  calculateRenGe() {\n    return this.surname.length === 1 ?\n      STROKE_DATA[this.surname] + STROKE_DATA[this.givenName[0]] :\n      STROKE_DATA[this.surname[1]] + STROKE_DATA[this.givenName[0]]\n  }\n  calculateDiGe() {\n    return this.givenName.length === 1 ?\n      STROKE_DATA[this.givenName[0]] + 1 :\n      STROKE_DATA[this.givenName[0]] + STROKE_DATA[this.givenName[1]]\n  }\n  getWugeDescription(number) {\n    const normalized = number % 10 || 10\n    return WUGE_DATA[normalized]?.description || '普通数理'\n  }\n  calculateWugeScore(wugeList) {\n    let score = 60 // 基础分\n    \n    // 计算吉数的数量\n    const luckyCount = wugeList.filter(item => \n      WUGE_DATA[item.number % 10 || 10]?.luck === '吉'\n    ).length\n\n    // 根据吉数数量加分\n    score += luckyCount * 8\n\n    return Math.min(100, score)\n  }\n  getWuxingAttribute(number) {\n    const normalized = number % 10 || 10\n    return WUGE_DATA[normalized]?.element || '土'\n  }\n  calculateAttributeStrength(element) {\n    let strength = 60\n    \n    // 根据八字配合加分\n    if (this.isWuxingHelpful(element, this.getRiZhuWuxing(this.options.bazi))) {\n      strength += 20\n    }\n    \n    // 根据性别特征加分\n    if (this.isElementSuitableForGender(element)) {\n      strength += "}