{"chunk": 0, "numChunks": 3, "fileHash": "AnVYNl2DM83q7XUZ+V1o51We51jIzl4iPzBkuLj4U0g=", "filePath": "pages/divination/divination.wxss", "content": "/* pages/divination/divination.wxss */\n.container {\n  padding: 20rpx;\n  background-color: #f5f5f5;\n}\n\n/* 占卜方式选择样式 */\n.divination-types {\n  display: flex;\n  justify-content: space-around;\n  padding: 20rpx;\n  background-color: #fff;\n  border-radius: 10rpx;\n  margin-bottom: 20rpx;\n}\n\n.type-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20rpx;\n  border-radius: 8rpx;\n  transition: all 0.3s ease;\n}\n\n.type-item.active {\n  background-color: #e6f3ff;\n}\n\n.type-icon {\n  width: 80rpx;\n  height: 80rpx;\n  margin-bottom: 10rpx;\n}\n\n.type-name {\n  font-size: 26rpx;\n  color: #333;\n}\n\n/* 输入区域样式 */\n.input-section {\n  background-color: #fff;\n  padding: 30rpx;\n  border-radius: 10rpx;\n  margin-bottom: 20rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n}\n\n.question-input {\n  width: 100%;\n  height: 200rpx;\n  padding: 20rpx;\n  background-color: #f8f8f8;\n  border-radius: 6rpx;\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 20rpx;\n}\n\n.submit-btn {\n  background-color: #4a90e2;\n  color: #fff;\n  font-size: 32rpx;\n}\n\n/* 结果区域样式 */\n.result-section {\n"}