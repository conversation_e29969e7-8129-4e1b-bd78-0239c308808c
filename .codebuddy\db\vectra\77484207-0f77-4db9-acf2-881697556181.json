{"chunk": 0, "numChunks": 6, "fileHash": "IwGBwJ25Blubhn4hCt83o/e9MDZEM6l3SvRIm1A0vrE=", "filePath": "pages/fengshui/fengshui.wxml", "content": "<!--pages/fengshui/fengshui.wxml-->\n<view class=\"container\">\n  <!-- 顶部标题区域 -->\n  <view class=\"header\">\n    <view class=\"header-content\">\n      <view class=\"title\">风水布局</view>\n      <view class=\"subtitle\">为您提供专业的居家风水分析与建议</view>\n    </view>\n    <view class=\"header-decoration\">\n      <text class=\"icon\">🏠</text>\n    </view>\n  </view>\n\n  <!-- 表单区域 -->\n  <view class=\"form-container\">\n    <!-- 房屋信息卡片 -->\n    <view class=\"info-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">房屋信息</text>\n        <text class=\"card-icon\">🏘️</text>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">房屋朝向</text>\n          <text class=\"required\">*</text>\n        </view>\n        <picker bindchange=\"onDirectionChange\" value=\"{{directionIndex}}\" range=\"{{directions}}\">\n          <view class=\"picker-wrapper\">\n            <text class=\"picker-text {{directions[directionIndex] ? 'selected' : 'placeholder'}}\">\n              {{directions[directionIndex] || '请选择房屋朝向'}}\n            </text>\n            <text class=\"picker-icon\">🧭</text>\n          </view>\n        </picker>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">房屋类型</text>\n          <text class=\"required\">*</text>\n        </view>\n"}