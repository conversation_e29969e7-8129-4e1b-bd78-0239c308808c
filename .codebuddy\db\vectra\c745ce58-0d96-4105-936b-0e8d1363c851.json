{"chunk": 10, "numChunks": 13, "fileHash": "UUsAqDc9MY1pjBTLokkGc1GjjJmTwiOLz2lDZFa/4nc=", "filePath": "pages/index/index.wxss", "content": "}\n\n.sign-in-text {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.sign-title {\n  font-size: 32rpx;\n  color: #2e7d32;\n  font-weight: bold;\n  margin-bottom: 8rpx;\n}\n\n.sign-desc {\n  font-size: 24rpx;\n  color: #558b2f;\n}\n\n.sign-btn {\n  background: #4caf50;\n  color: white;\n  padding: 15rpx 35rpx;\n  border-radius: 30rpx;\n  font-size: 28rpx;\n  font-weight: 500;\n  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);\n}\n\n/* 最近使用功能 - 美化 */\n.section {\n  margin-bottom: 40rpx;\n}\n\n.recent-scroll {\n  white-space: nowrap;\n  margin-top: 20rpx;\n}\n\n.recent-item {\n  display: inline-flex;\n  flex-direction: column;\n  align-items: center;\n  width: 140rpx;\n  padding: 20rpx;\n  margin-right: 20rpx;\n  background: white;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n}\n\n.recent-icon {\n  width: 60rpx;\n  height: 60rpx;\n  margin-bottom: 10rpx;\n}\n\n.recent-text {\n  font-size: 24rpx;\n  color: #666;\n  text-align: center;\n}\n\n/* 增强现有样式 */\n.grid-item,\n.service-item {\n  position: relative;\n  overflow: hidden;\n  background: var(--card-background);\n  border-radius: 20rpx;\n"}