{"chunk": 7, "numChunks": 12, "fileHash": "a4LdV5V9/sM95i4esh3ydW49XOc5FHg6OAvfDYJ8/so=", "filePath": "pages/name/index.js", "content": "const { NameCalculator } = require('../../utils/name/calculator');\nPage({\n  async analyzeName() {\n    try {\n      const calculator = new NameCalculator();\n      const result = await calculator.calculate({\n        surname: this.data.surname,\n        givenName: this.data.givenName,\n        gender: this.data.gender,\n        birthDate: this.data.birthDate\n      });"}