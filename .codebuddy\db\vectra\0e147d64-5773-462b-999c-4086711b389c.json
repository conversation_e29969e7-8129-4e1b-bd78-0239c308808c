{"chunk": 6, "numChunks": 9, "fileHash": "RJtqiMp6ufLzCDA9+EFDGWW4xytfcsd5HAKWaRu2Drc=", "filePath": "frontend-implementation/pages/index/index.js", "content": "// 首页 - 使用示例\ncreatePage({\n  async loadUserStats() {\n    try {\n      const result = await getUserStatistics()\n      if (result.status === 'success') {\n        this.setData({ userStats: result.data })\n      }\n    } catch (error) {\n      console.error('加载用户统计失败:', error)\n    }\n  },\n  /**\n   * 加载出生信息\n   */\n  async loadBirthInfo() {\n    try {\n      const result = await getBirthInfo()\n      if (result.status === 'success' && result.data.birth_info) {\n        this.setData({ birthInfo: result.data.birth_info })\n        store.setBirthInfo(result.data.birth_info)\n      }\n    } catch (error) {\n      console.error('加载出生信息失败:', error)\n    }\n  },\n  /**\n   * 加载最近分析\n   */\n  async loadRecentAnalyses() {\n    try {\n      const result = await getAnalysisHistory({ limit: 5 })\n      if (result.status === 'success') {\n        this.setData({ recentAnalyses: result.data.analyses || [] })\n      }\n    } catch (error) {\n      console.error('加载分析历史失败:', error)\n    }\n  },\n  /**\n   * 加载聊天会话\n   */\n  async loadChatSessions() {\n    try {\n      const result = await getChatSessions({ limit: 3 })\n      if (result.status === 'success') {\n        this.setData({ chatSessions: result.data.sessions || [] })\n      }\n    } catch (error) {\n      console.error('加载聊天会话失败:', error)\n    }\n  },\n  /**\n   * 加载快捷操作\n   */"}