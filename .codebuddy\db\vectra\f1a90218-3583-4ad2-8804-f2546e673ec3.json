{"chunk": 11, "numChunks": 13, "fileHash": "UUsAqDc9MY1pjBTLokkGc1GjjJmTwiOLz2lDZFa/4nc=", "filePath": "pages/index/index.wxss", "content": "  box-shadow: 0 6rpx 20rpx var(--shadow-color);\n  border: 1rpx solid var(--border-color);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.grid-item::before,\n.service-item::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: var(--gradient-primary);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.grid-item:active,\n.service-item:active {\n  transform: translateY(2rpx) scale(0.98);\n  box-shadow: 0 3rpx 10rpx var(--shadow-color);\n}\n\n.grid-item:active::before,\n.service-item:active::before {\n  opacity: 0.08;\n}\n\n/* 骨架屏加载样式 */\n.skeleton {\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: loading 1.5s infinite;\n}\n\n@keyframes loading {\n  0% {\n    background-position: 200% 0;\n  }\n  100% {\n    background-position: -200% 0;\n  }\n}\n\n.skeleton-banner {\n  height: 300rpx;\n  border-radius: 16rpx;\n  margin-bottom: 32rpx;\n}\n\n.skeleton-card {\n  height: 120rpx;\n  border-radius: 16rpx;\n  margin-bottom: 16rpx;\n}\n\n.skeleton-text {\n  height: 32rpx;\n  border-radius: 4rpx;\n"}