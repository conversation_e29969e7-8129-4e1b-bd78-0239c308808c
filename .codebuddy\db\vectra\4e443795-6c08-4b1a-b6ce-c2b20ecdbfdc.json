{"chunk": 4, "numChunks": 6, "fileHash": "8h8E6IyA2QKaLBRxCAXNE6RcQ2dvD/eV8dT/ZBdIuAw=", "filePath": "pages/marriage/marriage.wxml", "content": "            <view class=\"pillar\" wx:for=\"{{analysisResult.maleBazi}}\" wx:key=\"index\">\n              <view class=\"pillar-top\">{{item.tiangan}}</view>\n              <view class=\"pillar-bottom\">{{item.dizhi}}</view>\n            </view>\n          </view>\n        </view>\n        <view class=\"vs-divider\">\n          <text class=\"vs-icon\">💕</text>\n        </view>\n        <view class=\"bazi-column\">\n          <view class=\"person-name\">{{femaleInfo.name}}</view>\n          <view class=\"bazi-pillars\">\n            <view class=\"pillar\" wx:for=\"{{analysisResult.femaleBazi}}\" wx:key=\"index\">\n              <view class=\"pillar-top\">{{item.tiangan}}</view>\n              <view class=\"pillar-bottom\">{{item.dizhi}}</view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 详细分析 -->\n    <view class=\"info-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">详细分析</text>\n        <text class=\"card-icon\">📊</text>\n      </view>\n      <view class=\"analysis-details\">\n        <view class=\"analysis-item\" wx:for=\"{{analysisResult.details}}\" wx:key=\"type\">\n          <view class=\"analysis-header\">\n            <view class=\"analysis-title\">\n              <text class=\"analysis-icon\">{{item.icon}}</text>\n              <text>{{item.title}}</text>\n            </view>\n            <view class=\"analysis-score {{item.level}}\">{{item.score}}分</view>\n          </view>\n          <view class=\"analysis-content\">{{item.content}}</view>\n          <view class=\"score-bar\">\n            <view class=\"score-fill\" style=\"width: {{item.score}}%; background: {{item.color}}\"></view>\n          </view>\n"}