{"chunk": 3, "numChunks": 6, "fileHash": "m/bcrIpornnqwv0iP6r9/jZF/qprhOPA5RFcfEHrukg=", "filePath": "pages/profile/profile.wxss", "content": "  background: linear-gradient(135deg, #9575cd 0%, #7e57c2 100%);\n  color: white;\n  border-radius: 30rpx;\n  font-size: 28rpx;\n  font-weight: 500;\n}\n\n/* 常用功能 */\n.quick-section {\n  margin: 30rpx;\n}\n\n.section-header {\n  margin-bottom: 25rpx;\n}\n\n.section-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.quick-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 20rpx;\n}\n\n.quick-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 30rpx 15rpx;\n  background: white;\n  border-radius: 25rpx;\n  position: relative;\n  transition: all 0.3s ease;\n}\n\n.quick-item:active {\n  transform: scale(0.95);\n}\n\n.quick-icon {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 25rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 40rpx;\n  margin-bottom: 15rpx;\n  color: white;\n  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);\n}\n\n.quick-text {\n  font-size: 26rpx;\n  color: #666;\n  text-align: center;\n}\n\n.quick-badge {\n  position: absolute;\n  top: 20rpx;\n  right: 20rpx;\n  min-width: 36rpx;\n  height: 36rpx;\n  line-height: 36rpx;\n"}