{"chunk": 2, "numChunks": 4, "fileHash": "jubZTzyunGc3ITYrDRZliCEjMyZub9rUjJcBcvHhj28=", "filePath": "pages/name-test/name-test.wxml", "content": "          <text class=\"sancai-value\">{{testResult.sancai.di}}</text>\n        </view>\n      </view>\n      <view class=\"sancai-analysis\">\n        <text>{{testResult.sancai.analysis}}</text>\n      </view>\n    </view>\n\n    <!-- 详细分析 -->\n    <view class=\"analysis-section\">\n      <view class=\"section-title\">详细分析</view>\n      <view class=\"analysis-item\" wx:for=\"{{testResult.analysis}}\" wx:key=\"type\">\n        <view class=\"analysis-title\">{{item.title}}</view>\n        <view class=\"analysis-content\">{{item.content}}</view>\n        <view class=\"analysis-score\">\n          <text>{{item.score}}分</text>\n          <view class=\"score-bar\">\n            <view class=\"score-fill\" style=\"width: {{item.score}}%\"></view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 改名建议 -->\n    <view class=\"suggestion-section\" wx:if=\"{{testResult.suggestions}}\">\n      <view class=\"section-title\">改名建议</view>\n      <view class=\"suggestion-list\">\n        <view class=\"suggestion-item\" wx:for=\"{{testResult.suggestions}}\" wx:key=\"index\">\n          <view class=\"suggestion-name\">{{item.name}}</view>\n          <view class=\"suggestion-score\">{{item.score}}分</view>\n          <view class=\"suggestion-reason\">{{item.reason}}</view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 操作按钮 -->\n    <view class=\"action-buttons\">\n      <button class=\"save-btn\" bindtap=\"saveResult\">保存结果</button>\n      <button class=\"share-btn\" bindtap=\"shareResult\">分享</button>\n"}