{"chunk": 36, "numChunks": 38, "fileHash": "DpyinnhZJO6jrs0/VY0BXwrNjORlEQ1BeRywBJybhxQ=", "filePath": "pages/ai-chat/ai-chat.js", "content": "const app = getApp()\nPage({\n  navigateToPage(url) {\n    const needsBirthInfo = [\n      '/pages/bazi/bazi',\n      '/pages/ziwei/ziwei',\n      '/pages/wuxing/wuxing',\n      '/pages/yijing/yijing',\n      '/pages/fortune/fortune',\n      '/pages/marriage/marriage'\n    ]\n\n    if (needsBirthInfo.includes(url)) {\n      const birthInfo = wx.getStorageSync('birthInfo')\n      if (!birthInfo) {\n        wx.showModal({\n          title: '需要出生信息',\n          content: '该功能需要您的出生信息才能提供准确分析，是否前往填写？',\n          confirmText: '去填写',\n          cancelText: '取消',\n          success: (res) => {\n            if (res.confirm) {\n              wx.setStorageSync('targetPage', url)\n              wx.navigateTo({\n                url: '/pages/birth-info/birth-info'\n              })\n            }\n          }\n        })\n        return\n      }\n    }\n\n    wx.navigateTo({ url })\n  },\n  /**\n   * 处理快捷操作\n   */\n  async handleQuickAction(type) {\n    const quickActions = {\n      'daily_fortune': '请帮我看看今日运势',\n      'bazi_analysis': '请帮我分析八字',\n      'yijing_divine': '我想用易经占卜',\n      'fengshui_check': '请帮我看看风水'\n    }\n\n    const message = quickActions[type]\n    if (message) {\n      await this.sendMessage(message)\n    }\n  },\n  /**\n   * 重试消息\n   */"}