{"chunk": 6, "numChunks": 8, "fileHash": "y2IL59YXs36741pVvCZKEDopMVE9nury7qdEmYs0pAw=", "filePath": "pages/marriage/marriage.wxss", "content": "  font-size: 28rpx;\n  font-weight: 600;\n  padding: 4rpx 12rpx;\n  border-radius: 12rpx;\n}\n\n.analysis-score.high {\n  background: #a8e6cf;\n  color: #2d6a4f;\n}\n\n.analysis-score.medium {\n  background: #ffd3b6;\n  color: #9c4221;\n}\n\n.analysis-score.low {\n  background: #ffaaa5;\n  color: #742a2a;\n}\n\n.analysis-content {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n  margin-bottom: 16rpx;\n}\n\n.score-bar {\n  height: 8rpx;\n  background: #e8e8e8;\n  border-radius: 4rpx;\n  overflow: hidden;\n}\n\n.score-fill {\n  height: 100%;\n  border-radius: 4rpx;\n  transition: width 0.3s ease;\n}\n\n/* 建议列表样式 */\n.suggestions-list {\n  padding: 8rpx 0;\n}\n\n.suggestion-item {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 16rpx;\n  font-size: 28rpx;\n  line-height: 1.5;\n}\n\n.suggestion-item:last-child {\n  margin-bottom: 0;\n}\n\n.suggestion-dot {\n  color: #667eea;\n  margin-right: 12rpx;\n  font-weight: bold;\n}\n\n.suggestion-text {\n  flex: 1;\n  color: #666;\n}\n\n/* 操作按钮样式 */\n.action-buttons {\n  display: flex;\n  gap: 20rpx;\n  margin-top: 40rpx;\n}\n\n.reset-btn, .share-btn {\n  flex: 1;\n  height: 88rpx;\n"}