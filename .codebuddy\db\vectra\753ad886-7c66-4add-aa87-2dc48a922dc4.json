{"chunk": 3, "numChunks": 5, "fileHash": "4lpNnoqGg1eEPtIgkdLgszOng5dzR4kuUtSZ8HRwytk=", "filePath": "pages/yijing/yijing.wxss", "content": "  border-top: 4rpx solid var(--primary-color);\n  border-bottom: 4rpx solid var(--primary-color);\n}\n\n.line-value {\n  position: absolute;\n  right: -50rpx;\n  color: var(--primary-color);\n  font-size: 24rpx;\n  font-weight: bold;\n}\n\n.hexagram-nature {\n  text-align: center;\n  font-size: 26rpx;\n  color: var(--text-secondary);\n  margin-top: 20rpx;\n}\n\n/* 解读内容样式 */\n.interpretation {\n  margin-top: 40rpx;\n}\n\n.interpretation-section {\n  margin-bottom: 30rpx;\n}\n\n.interpretation-title {\n  display: flex;\n  align-items: center;\n  font-size: 30rpx;\n  color: var(--primary-color);\n  margin-bottom: 20rpx;\n}\n\n.interpretation-content {\n  font-size: 28rpx;\n  color: var(--text-primary);\n  line-height: 1.8;\n  padding: 20rpx;\n  background-color: var(--primary-lightest);\n  border-radius: 12rpx;\n  border: 2rpx solid var(--border-color);\n}\n\n/* 分享按钮样式 */\n.share-btn {\n  margin-top: 30rpx;\n  background-color: var(--primary-lightest);\n  color: var(--primary-color);\n  font-size: 28rpx;\n  border-radius: 40rpx;\n  height: 80rpx;\n  line-height: 80rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2rpx solid var(--border-color);\n}\n\n/* 底部提示 */\n.footer-tip {\n  text-align: center;\n  font-size: 24rpx;\n  color: var(--text-light);\n"}