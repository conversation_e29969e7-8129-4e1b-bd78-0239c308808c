{"chunk": 12, "numChunks": 13, "fileHash": "UUsAqDc9MY1pjBTLokkGc1GjjJmTwiOLz2lDZFa/4nc=", "filePath": "pages/index/index.wxss", "content": "  margin-bottom: 16rpx;\n}\n\n.skeleton-text.short {\n  width: 60%;\n}\n\n/* 响应式设计 */\n@media screen and (max-width: 375px) {\n  .grid-container {\n    grid-template-columns: repeat(3, 1fr);\n  }\n  \n  .grid-item {\n    padding: 20rpx;\n  }\n  \n  .grid-icon {\n    width: 48rpx;\n    height: 48rpx;\n  }\n}\n"}