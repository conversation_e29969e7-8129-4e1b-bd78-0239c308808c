{"chunk": 5, "numChunks": 15, "fileHash": "ydfcj+4tZs2HvARUdw6TErn3fg1suYaeqU0s+F1D6wI=", "filePath": "pages/hehun/index.js", "content": "const app = getApp()\nPage({\n  async onAnalyze() {\n    if (!this.data.canSubmit) return\n\n    wx.showLoading({ title: '正在测算...' })\n\n    try {\n      // 计算男女双方八字\n      const maleCalculator = new BaziCalculator({\n        name: this.data.male.name,\n        gender: 'male',\n        birthDate: this.data.male.birthDate,\n        birthTime: this.data.male.birthTime\n      })\n\n      const femaleCalculator = new BaziCalculator({\n        name: this.data.female.name,\n        gender: 'female',\n        birthDate: this.data.female.birthDate,\n        birthTime: this.data.female.birthTime\n      })\n\n      const maleBazi = await maleCalculator.calculate()\n      const femaleBazi = await femaleCalculator.calculate()\n\n      // 合婚测算\n      const hehunCalculator = new HehunCalculator(maleBazi, femaleBazi)\n      const result = await hehunCalculator.calculate()\n\n      // 更新结果\n      this.setData({\n        showResult: true,\n        matchScore: result.score,\n        matchDesc: result.description,\n        maleBazi: result.maleBaziStr,\n        femaleBazi: result.femaleBaziStr,\n        baziAnalysis: result.baziAnalysis,\n        wuxingAnalysis: result.wuxingAnalysis,\n        adviceList: result.adviceList\n      })\n\n      // 绘制五行图表\n      this.drawWuxingChart(result.wuxingData)\n\n      wx.hideLoading()\n    } catch (error) {\n      console.error('测算失败:', error)\n      wx.hideLoading()\n      wx.showToast({\n        title: '测算失败，请重试',\n        icon: 'none'\n      })\n    }\n  },\n  // 绘制五行图表"}