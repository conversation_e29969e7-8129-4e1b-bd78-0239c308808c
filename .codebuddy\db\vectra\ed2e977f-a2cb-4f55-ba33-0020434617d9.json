{"chunk": 2, "numChunks": 9, "fileHash": "yDcz6SvSgZVWvKwtTExumLiXk7y0FYiP8gZ9qIHubRA=", "filePath": "pages/fengshui/fengshui.wxss", "content": "  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);\n}\n\n.input-placeholder {\n  color: #bbb;\n}\n\n.input-unit {\n  position: absolute;\n  right: 20rpx;\n  font-size: 28rpx;\n  color: #999;\n}\n\n/* 选择器 */\n.picker-wrapper {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n  border: 2rpx solid #e8e8e8;\n  border-radius: 16rpx;\n  padding: 0 20rpx;\n  background: #fafafa;\n  transition: all 0.3s ease;\n}\n\n.picker-wrapper:active {\n  border-color: #667eea;\n  background: white;\n}\n\n.picker-text {\n  font-size: 32rpx;\n  flex: 1;\n}\n\n.picker-text.placeholder {\n  color: #bbb;\n}\n\n.picker-text.selected {\n  color: #333;\n}\n\n.picker-icon {\n  font-size: 32rpx;\n  opacity: 0.5;\n}\n\n/* 房间计数器 */\n.room-counter {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n  border: 2rpx solid #e8e8e8;\n  border-radius: 16rpx;\n  padding: 0 20rpx;\n  background: #fafafa;\n}\n\n.counter-btn {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 40rpx;\n  color: #667eea;\n  background: rgba(102, 126, 234, 0.1);\n"}