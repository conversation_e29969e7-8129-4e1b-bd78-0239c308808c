{"chunk": 7, "numChunks": 11, "fileHash": "6cvegKFbV+qpODqHzHmmaj3L2MYUXg3Sv6AOEWns8A8=", "filePath": "frontend-implementation/mixins/basePage.js", "content": "// 页面基础混入\nexport const basePageMixin = {\n  async safeApiCall(apiCall, options = {}) {\n    const {\n      showLoading: shouldShowLoading = true,\n      loadingText = '加载中...',\n      showError: shouldShowError = true,\n      onSuccess,\n      onError,\n      onFinally\n    } = options\n\n    try {\n      if (shouldShowLoading) {\n        this.showLoading(loadingText)\n      }\n\n      const result = await apiCall()\n\n      if (onSuccess) {\n        onSuccess(result)\n      }\n\n      return result\n    } catch (error) {\n      console.error('API调用失败:', error)\n\n      if (shouldShowError) {\n        this.showError(error.message || '操作失败')\n      }\n\n      if (onError) {\n        onError(error)\n      }\n\n      throw error\n    } finally {\n      if (shouldShowLoading) {\n        this.hideLoading()\n      }\n\n      if (onFinally) {\n        onFinally()\n      }\n    }\n  },\n  /**\n   * 防抖函数\n   * @param {function} func 要防抖的函数\n   * @param {number} delay 延迟时间\n   */\n  debounce(func, delay = 300) {\n    if (this.debounceTimer) {\n      clearTimeout(this.debounceTimer)\n    }\n    \n    this.debounceTimer = setTimeout(() => {\n      func.call(this)\n    }, delay)\n  },\n  /**\n   * 节流函数\n   * @param {function} func 要节流的函数\n   * @param {number} delay 延迟时间\n   */\n  throttle(func, delay = 300) {\n    if (this.throttleTimer) {\n      return\n    }\n    \n    this.throttleTimer = setTimeout(() => {\n      func.call(this)\n      this.throttleTimer = null\n    }, delay"}