{"chunk": 3, "numChunks": 6, "fileHash": "LfPZZEUjbDaiOVOLCtwxAuZgKgXnDAGyjWMO0X626Ik=", "filePath": "pages/ai-chat/ai-chat.wxml", "content": "              <!-- 消息反馈 - 只在消息完全显示后显示 -->\n              <view class=\"message-feedback\" wx:if=\"{{!item.isTyping}}\">\n                <view class=\"feedback-btn\" data-content=\"{{item.content}}\" bindtap=\"copyMessage\">\n                  <text class=\"feedback-icon\">📋</text>\n                  <text class=\"feedback-text\">复制</text>\n                </view>\n                <view class=\"feedback-btn\" data-content=\"{{item.content}}\" bindtap=\"shareMessage\">\n                  <image class=\"feedback-icon-img\" src=\"https://img.icons8.com/fluency/48/wechat.png\"></image>\n                  <text class=\"feedback-text\">分享</text>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n\n\n      <!-- 底部占位 -->\n      <view class=\"bottom-spacer\"></view>\n    </scroll-view>\n  </view>\n\n  <!-- 悬浮输入区域 - 屏幕中间位置 -->\n  <view class=\"floating-input-container {{isInputExpanded ? 'expanded' : 'collapsed'}}\">\n    <!-- 展开状态 - 上下布局 -->\n    <view class=\"expanded-input-panel\" wx:if=\"{{isInputExpanded}}\">\n      <!-- 输入框区域 -->\n      <view class=\"input-section\">\n        <view class=\"input-wrapper\">\n          <textarea\n            class=\"message-input\"\n            value=\"{{inputValue}}\"\n            placeholder=\"请输入您的问题...\"\n            placeholder-class=\"input-placeholder\"\n            maxlength=\"1000\"\n            cursor-spacing=\"20\"\n            show-confirm-bar=\"{{false}}\"\n            adjust-position=\"{{true}}\"\n            bindinput=\"onInputChange\"\n            bindconfirm=\"sendMessage\"\n            bindfocus=\"onInputFocus\"\n            auto-height\n            fixed=\"true\"\n          />\n"}