{"chunk": 2, "numChunks": 6, "fileHash": "u49Rqom2MlWUCSY5d2kW1CfpdWY95ku7je8yGKj9+oc=", "filePath": "pages/name-test/name-test.wxss", "content": ".overall-score {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 10rpx;\n}\n\n.score-label {\n  font-size: 28rpx;\n  opacity: 0.9;\n}\n\n.score-value {\n  font-size: 64rpx;\n  font-weight: bold;\n}\n\n/* 五格数理 */\n.wuge-section {\n  padding: 40rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.section-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 30rpx;\n}\n\n.wuge-grid {\n  display: grid;\n  grid-template-columns: repeat(5, 1fr);\n  gap: 20rpx;\n}\n\n.wuge-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20rpx;\n  background: #f9f9f9;\n  border-radius: 12rpx;\n}\n\n.wuge-name {\n  font-size: 24rpx;\n  color: #666;\n  margin-bottom: 8rpx;\n}\n\n.wuge-number {\n  font-size: 32rpx;\n  font-weight: bold;\n  margin-bottom: 8rpx;\n}\n\n.wuge-level {\n  font-size: 20rpx;\n  padding: 4rpx 8rpx;\n  border-radius: 6rpx;\n  color: white;\n}\n\n.wuge-level.good {\n  background: #52c41a;\n}\n\n.wuge-level.bad {\n  background: #ff4d4f;\n}\n\n.wuge-level.normal {\n  background: #faad14;\n}\n\n/* 三才配置 */\n.sancai-section {\n  padding: 40rpx;\n"}