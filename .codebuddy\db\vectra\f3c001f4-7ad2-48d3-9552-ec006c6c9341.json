{"chunk": 46, "numChunks": 76, "fileHash": "lPYWZE8QItgwSyEsMXHtl6y/HYBCI8BdGaV+1l8ICMA=", "filePath": "docs/md/API_INTERFACE_DOCUMENTATION.md", "content": "        \"vulnerable_areas\": [\"string\"],   // 易患疾病部位\n        \"health_tips\": [\"string\"],        // 健康建议\n        \"beneficial_activities\": [\"string\"] // 有益活动\n      }\n    },\n    \"enhancement_suggestions\": {    // 增强建议\n      \"colors\": {                   // 有利颜色\n        \"primary\": [\"string\"],      // 主要颜色\n        \"secondary\": [\"string\"],    // 次要颜色\n        \"avoid\": [\"string\"]         // 避免颜色\n      },\n      \"directions\": {               // 有利方位\n        \"favorable\": [\"string\"],    // 有利方位\n        \"unfavorable\": [\"string\"]   // 不利方位\n      },\n      \"numbers\": {                  // 有利数字\n        \"lucky\": [\"number\"],        // 幸运数字\n        \"unlucky\": [\"number\"]       // 不利数字\n      },\n      \"lifestyle\": [\"string\"]       // 生活方式建议\n    },\n    \"points_cost\": \"number\"\n  }\n}\n```\n\n#### 5. 紫薇斗数分析\n```\nPOST /api/analysis/ziwei\nAuthorization: Bearer {token}\nContent-Type: application/json\n\nRequest:\n{\n  \"use_birth_info\": \"boolean\",\n  \"birth_info\": {\n    // 同八字分析的birth_info结构\n  },\n  \"analysis_depth\": \"string\"        // 分析深度 (basic/detailed/comprehensive)\n}\n\nResponse:\n{\n  \"status\": \"success|error\",\n  \"message\": \"string\",\n  \"data\": {\n    \"analysis_id\": \"string\",\n    \"life_palace\": {                // 命宫\n"}