{"chunk": 5, "numChunks": 13, "fileHash": "RdYUonhiW47SLv0/ZKSqV2JJEqqe4g6tc2PZPdqonRM=", "filePath": "pages/post-detail/post-detail.js", "content": "const app = getApp()\nPage({\n  async checkLikeStatus(postId) {\n    try {\n      // 获取用户信息\n      const userInfo = await wx.cloud.callFunction({\n        name: 'getUserInfo'\n      }).catch(() => ({ result: { openid: 'anonymous' } }))\n      \n      const db = wx.cloud.database()\n      const _ = db.command\n      \n      // 查询用户点赞记录\n      const likeResult = await db.collection('likes')\n        .where({\n          postId: postId,\n          userId: userInfo.result.openid\n        })\n        .count()\n      \n      this.setData({\n        isLiked: likeResult.total > 0\n      })\n    } catch (error) {\n      console.error('检查点赞状态失败:', error)\n    }\n  },\n  /**\n   * 加载评论列表\n   */"}