{"chunk": 4, "numChunks": 13, "fileHash": "UUsAqDc9MY1pjBTLokkGc1GjjJmTwiOLz2lDZFa/4nc=", "filePath": "pages/index/index.wxss", "content": "  width: 100%;\n  height: 100%;\n  border-radius: 25rpx;\n}\n\n.banner-title {\n  position: absolute;\n  bottom: 30rpx;\n  left: 30rpx;\n  color: white;\n  font-size: 36rpx;\n  font-weight: bold;\n  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);\n}\n\n/* 轮播图圆点优化 */\n.wx-swiper-dots {\n  bottom: 20rpx !important;\n}\n\n.wx-swiper-dot {\n  width: 16rpx;\n  height: 16rpx;\n  margin: 0 8rpx;\n  background: rgba(255, 255, 255, 0.4);\n  border-radius: 8rpx;\n  transition: all 0.3s ease;\n}\n\n.wx-swiper-dot-active {\n  width: 32rpx;\n  background: #ffffff;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\n}\n\n/* 测算类目 - 美化 */\n.category-section {\n  margin: 40rpx 0;\n}\n\n.section-title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 25rpx;\n  padding: 0 10rpx;\n}\n\n.section-more {\n  font-size: 26rpx;\n  color: #9575cd;\n  font-weight: normal;\n}\n\n.category-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 25rpx;\n  padding: 0 10rpx;\n}\n\n"}