{"chunk": 4, "numChunks": 7, "fileHash": "YmBtAc19/HOz7eUBhWxGsON9KNrEblPnxcHiHsXvXv8=", "filePath": "pages/community/community.wxss", "content": "  justify-content: center;\n}\n\n.modal-content {\n  width: 90%;\n  max-height: 80vh;\n  background: #fff;\n  border-radius: 12px;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n.modal-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 15px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.modal-header text {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.close-icon {\n  width: 20px;\n  height: 20px;\n  padding: 5px;\n}\n\n.modal-body {\n  padding: 15px;\n  flex: 1;\n  overflow-y: auto;\n}\n\n.title-input {\n  width: 100%;\n  height: 40px;\n  border: 1px solid #f0f0f0;\n  border-radius: 8px;\n  padding: 0 12px;\n  margin-bottom: 12px;\n  font-size: 14px;\n}\n\n.content-input {\n  width: 100%;\n  height: 120px;\n  border: 1px solid #f0f0f0;\n  border-radius: 8px;\n  padding: 12px;\n  margin-bottom: 12px;\n  font-size: 14px;\n}\n\n/* 标签选择样式 */\n.post-tags {\n  display: flex;\n  gap: 10px;\n  margin-bottom: 12px;\n}\n\n.tag {\n  padding: 6px 12px;\n  font-size: 12px;\n  color: #666;\n  background-color: #f5f5f5;\n  border-radius: 15px;\n}\n\n.tag.active {\n  color: #fff;\n"}