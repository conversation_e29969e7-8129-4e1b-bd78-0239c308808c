{"chunk": 1, "numChunks": 4, "fileHash": "euo6OT+uMcA1jHrVDL9HcA2afhD9hscN1rx91h/6LSA=", "filePath": "pages/birth-info/birth-info.wxml", "content": "            <text class=\"gender-icon\">👨</text>\n            <text class=\"gender-text\">男</text>\n          </view>\n          <view\n            class=\"gender-option {{gender === '女' ? 'active' : ''}}\"\n            bindtap=\"selectGender\"\n            data-gender=\"女\"\n          >\n            <text class=\"gender-icon\">👩</text>\n            <text class=\"gender-text\">女</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 出生时间卡片 -->\n    <view class=\"info-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">出生时间</text>\n        <text class=\"card-icon\">📅</text>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">出生日期时间</text>\n          <text class=\"required\">*</text>\n        </view>\n        <picker\n          mode=\"multiSelector\"\n          value=\"{{dateTimeArray}}\"\n          bindchange=\"onDateTimeChange\"\n          bindcolumnchange=\"onDateTimeColumnChange\"\n          range=\"{{dateTime}}\"\n        >\n          <view class=\"picker-wrapper\">\n            <text class=\"picker-text {{selectedDateTime ? 'selected' : 'placeholder'}}\">\n              {{selectedDateTime || '请选择出生日期时间'}}\n            </text>\n            <text class=\"picker-icon\">📅</text>\n          </view>\n        </picker>\n        <view class=\"picker-tip\">精确的出生时间有助于提供更准确的分析</view>\n      </view>\n    </view>\n\n    <!-- 辅助信息卡片 -->\n    <view class=\"info-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">辅助信息</text>\n"}