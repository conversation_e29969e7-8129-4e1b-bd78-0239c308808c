{"chunk": 4, "numChunks": 18, "fileHash": "yi4gCVr62AiqkHDrZGxGnPy2D7On+5yxIebQ7IGvQ04=", "filePath": "pages/community/community.js", "content": "// pages/community/community.js\nPage({\n  async initCloud() {\n    if (!wx.cloud) {\n      wx.showToast({\n        title: '请使用 2.2.3 或以上的基础库以使用云能力',\n        icon: 'none'\n      })\n      return\n    }\n\n    try {\n      await wx.cloud.init({\n        env: 'cloud1-7gnaonfy0429a3eb',\n        traceUser: true\n      })\n      \n      this.setData({ isCloudInited: true }, () => {\n        this.loadPosts()\n      })\n    } catch (error) {\n      console.error('云开发初始化失败:', error)\n      wx.showToast({\n        title: '系统初始化失败',\n        icon: 'none'\n      })\n    }\n  },\n  /**\n   * 生命周期函数--监听页面初次渲染完成\n   */\n  onReady() {\n\n  },\n  /**\n   * 生命周期函数--监听页面显示\n   */\n  onShow() {\n    if (typeof this.getTabBar === 'function' && this.getTabBar()) {\n      this.getTabBar().setData({\n        selected: 1\n      })\n    }\n  },\n  /**\n   * 生命周期函数--监听页面隐藏\n   */\n  onHide() {\n\n  },\n  /**\n   * 生命周期函数--监听页面卸载\n   */\n  onUnload() {\n\n  },\n  /**\n   * 页面相关事件处理函数--监听用户下拉动作\n   */\n  onPullDownRefresh() {\n    if (!this.data.isCloudInited) {\n      wx.stopPullDownRefresh()\n      return\n    }\n\n    this.setData({\n      posts: [],\n      pageNum: 1,\n      hasMore: true\n    }, () => {\n      this.loadPosts().then(() => {\n        wx.stopPullDownRefresh()\n      })\n    })\n  },\n  /**\n   * 页面上拉触底事件的处理函数\n   */"}