{"chunk": 1, "numChunks": 5, "fileHash": "pt0uFokY5+WPeovdWLk6lUfTQUcY8vS6GhDpk14daRU=", "filePath": "pages/fengshui/index.wxml", "content": "      <text class=\"label\">建筑年份</text>\n      <picker mode=\"date\" fields=\"year\" value=\"{{buildYear}}\" bindchange=\"onBuildYearChange\">\n        <view class=\"picker {{buildYear ? '' : 'placeholder'}}\">\n          {{buildYear || '请选择建筑年份'}}\n        </view>\n      </picker>\n    </view>\n\n    <!-- 房屋面积 -->\n    <view class=\"form-item\">\n      <text class=\"label\">房屋面积（平方米）</text>\n      <input class=\"input\" type=\"digit\" placeholder=\"请输入房屋面积\" model:value=\"{{area}}\" bindinput=\"onAreaInput\"/>\n    </view>\n\n    <!-- 户型选择 -->\n    <view class=\"form-item\">\n      <text class=\"label\">户型结构</text>\n      <view class=\"room-picker\">\n        <view class=\"room-item\">\n          <text>室</text>\n          <picker mode=\"selector\" range=\"{{roomNums}}\" value=\"{{roomNumIndex}}\" bindchange=\"onRoomNumChange\">\n            <view class=\"number-picker\">{{roomNumIndex > -1 ? roomNums[roomNumIndex] : '0'}}</view>\n          </picker>\n        </view>\n        <view class=\"room-item\">\n          <text>厅</text>\n          <picker mode=\"selector\" range=\"{{hallNums}}\" value=\"{{hallNumIndex}}\" bindchange=\"onHallNumChange\">\n            <view class=\"number-picker\">{{hallNumIndex > -1 ? hallNums[hallNumIndex] : '0'}}</view>\n          </picker>\n        </view>\n        <view class=\"room-item\">\n          <text>卫</text>\n"}