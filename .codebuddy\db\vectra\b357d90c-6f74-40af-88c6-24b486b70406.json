{"chunk": 12, "numChunks": 14, "fileHash": "3g+f4UKfyPQOeFOGstuRr+TvNVx6NqSI5ZfRnXdVunU=", "filePath": "pages/profile/profile.js", "content": "// pages/profile/profile.js\nPage({\n  },\n  calculateLevel(points) {\n    // 等级计算规则：每100点升一级\n    return Math.floor(points / 100) + 1\n  },\n  formatTime(timestamp) {\n    if (!timestamp) return ''\n    const date = new Date(timestamp)\n    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`\n  },"}