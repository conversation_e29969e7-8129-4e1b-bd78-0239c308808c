{"chunk": 16, "numChunks": 18, "fileHash": "/DUygIsVyFRM4gmsdi8uiMJN8RFlyElKDKTgRRrPFCI=", "filePath": "utils/hehun/calculator.js", "content": "// 五行生克关系\nclass HehunCalculator {\n  getWeddingDateAdvice() {\n    const maleWuxing = this.getMainWuxing(this.maleBazi)\n    const femaleWuxing = this.getMainWuxing(this.femaleBazi)\n    \n    let advice = '建议选择'\n    if (this.isHelpful(maleWuxing, femaleWuxing)) {\n      advice += '双方五行相生的月份，如'\n      switch(maleWuxing) {\n        case '金': advice += '农历七月、八月'; break\n        case '木': advice += '农历三月、四月'; break\n        case '水': advice += '农历十一月、十二月'; break\n        case '火': advice += '农历五月、六月'; break\n        case '土': advice += '农历九月、十月'; break\n      }\n    } else {\n      advice += '五行能量旺盛的月份，避开双方五行相克的时节'\n    }\n    advice += '举办婚礼。'\n    return advice\n  }"}