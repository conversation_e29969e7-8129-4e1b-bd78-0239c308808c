{"chunk": 61, "numChunks": 74, "fileHash": "UWh+hTmku0OFKrrOI8FY7Ob/w0LwUBFFaP/YfXcJBpg=", "filePath": "utils/xingming/calculator.js", "content": "// 汉字笔画数据库\nclass XingmingCalculator {\n  calculateWuge() {\n    // 计算天格\n    const tianGe = this.surname.length === 1 ? \n      STROKE_DATA[this.surname] + 1 :\n      STROKE_DATA[this.surname[0]] + STROKE_DATA[this.surname[1]]\n\n    // 计算人格\n    const renGe = this.surname.length === 1 ?\n      STROKE_DATA[this.surname] + STROKE_DATA[this.givenName[0]] :\n      STROKE_DATA[this.surname[1]] + STROKE_DATA[this.givenName[0]]\n\n    // 计算地格\n    const diGe = this.givenName.length === 1 ?\n      STROKE_DATA[this.givenName[0]] + 1 :\n      STROKE_DATA[this.givenName[0]] + STROKE_DATA[this.givenName[1]]\n\n    // 计算外格\n    const waiGe = tianGe + diGe - renGe\n\n    // 计算总格\n    const zongGe = this.calculateTotalStrokes()\n\n    // 生成五格数据\n    const wugeList = [\n      { name: '天格', number: tianGe, description: this.getWugeDescription(tianGe) },\n      { name: '人格', number: renGe, description: this.getWugeDescription(renGe) },\n      { name: '地格', number: diGe, description: this.getWugeDescription(diGe) },\n      { name: '外格', number: waiGe, description: this.getWugeDescription(waiGe) },\n      { name: '总格', number: zongGe, description: this.getWugeDescription(zongGe) }\n    ]\n\n    // 计算五格得分\n    const score = this.calculateWugeScore(wuge"}