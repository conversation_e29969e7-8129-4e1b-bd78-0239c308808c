{"chunk": 0, "numChunks": 5, "fileHash": "kj9oZjkyC/FHxSTFwX241qsgDqE06+EB/MIRUDnQNTM=", "filePath": "pages/login/login.wxml", "content": "<!--登录页面-->\n<view class=\"login-container\">\n  <!-- 背景装饰 -->\n  <view class=\"background-decoration\">\n    <view class=\"decoration-circle circle1\"></view>\n    <view class=\"decoration-circle circle2\"></view>\n    <view class=\"decoration-circle circle3\"></view>\n  </view>\n\n  <!-- 顶部Logo和标题 -->\n  <view class=\"header-section\">\n    <image class=\"app-logo\" src=\"/assets/images/logo.png\" mode=\"aspectFit\" />\n    <view class=\"app-title\">恒琦易道</view>\n    <view class=\"app-subtitle\">专业的命理测算平台</view>\n  </view>\n\n  <!-- 登录表单 -->\n  <view class=\"login-form\">\n    <!-- 微信一键登录 -->\n    <view class=\"quick-login-section\">\n      <view class=\"section-title\">\n        <text>快速登录</text>\n        <text class=\"section-desc\">使用微信账号快速登录</text>\n      </view>\n      \n      <!-- 微信授权登录按钮 -->\n      <button \n        class=\"wx-login-btn\"\n        open-type=\"getUserProfile\"\n        bindgetuserprofile=\"onGetUserProfile\"\n        wx:if=\"{{!userInfo.nickName}}\"\n      >\n        <view class=\"btn-content\">\n          <image class=\"wx-icon\" src=\"/assets/icons/wechat.png\" mode=\"aspectFit\" />\n          <text class=\"btn-text\">微信授权登录</text>\n        </view>\n      </button>\n\n      <!-- 用户信息显示 -->\n      <view class=\"user-info-card\" wx:if=\"{{userInfo.nickName}}\">\n        <image class=\"user-avatar\" src=\"{{userInfo.avatarUrl}}\" mode=\"aspectFill\" />\n        <view class=\"user-details\">\n"}