{"chunk": 35, "numChunks": 38, "fileHash": "DpyinnhZJO6jrs0/VY0BXwrNjORlEQ1BeRywBJybhxQ=", "filePath": "pages/ai-chat/ai-chat.js", "content": "const app = getApp()\nPage({\n  onMessageAction(e) {\n    const { action, url } = e.currentTarget.dataset\n\n    switch (action) {\n      case 'goto_birth_info':\n        // 保存当前页面为目标页面，填写完出生信息后返回\n        wx.setStorageSync('targetPage', '/pages/ai-chat/ai-chat')\n        wx.navigateTo({\n          url: '/pages/birth-info/birth-info'\n        })\n        break\n      case 'goto_page':\n        // 通用页面跳转\n        if (url) {\n          this.navigateToPage(url)\n        }\n        break\n      case 'retry':\n        // 重试最后一条用户消息\n        const lastUserMessage = this.data.messages\n          .slice()\n          .reverse()\n          .find(msg => msg.type === 'user')\n        if (lastUserMessage) {\n          this.retryMessage(lastUserMessage.content)\n        }\n        break\n      case 'quick_action':\n        // 快捷操作\n        const { quickType } = e.currentTarget.dataset\n        this.handleQuickAction(quickType)\n        break\n    }\n  },\n  /**\n   * 处理页面跳转（带出生信息检查）\n   */"}