{"chunk": 3, "numChunks": 8, "fileHash": "y2IL59YXs36741pVvCZKEDopMVE9nury7qdEmYs0pAw=", "filePath": "pages/marriage/marriage.wxss", "content": "  color: #d68910;\n}\n\n.tips-content {\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.tip-item {\n  font-size: 26rpx;\n  color: #b7950b;\n  line-height: 1.4;\n}\n\n/* 提交按钮 */\n.submit-section {\n  margin-top: 40rpx;\n}\n\n.submit-btn {\n  width: 100%;\n  height: 96rpx;\n  border-radius: 24rpx;\n  font-size: 36rpx;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12rpx;\n  transition: all 0.3s ease;\n  border: none;\n}\n\n.submit-btn.active {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);\n}\n\n.submit-btn.disabled {\n  background: #e8e8e8;\n  color: #bbb;\n  box-shadow: none;\n}\n\n.submit-btn.active:active {\n  transform: translateY(2rpx);\n  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);\n}\n\n.btn-text {\n  font-size: 36rpx;\n}\n\n.btn-icon {\n  font-size: 32rpx;\n}\n\n/* 分析结果样式 */\n.score-section {\n  text-align: center;\n  padding: 20rpx 0;\n}\n\n.score-ring {\n  position: relative;\n  width: 240rpx;\n  height: 240rpx;\n  margin: 0 auto 30rpx;\n"}