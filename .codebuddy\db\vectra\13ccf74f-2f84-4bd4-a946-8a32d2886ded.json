{"chunk": 5, "numChunks": 6, "fileHash": "3s34je0qL5+ULluXZdiBSih2mwyLkPtuCPPtZ9V/C7o=", "filePath": "pages/community/community.wxml", "content": "            </view>\n          </picker>\n        </view>\n      </view>\n      <view class=\"modal-footer\">\n        <button class=\"cancel-btn\" bindtap=\"hidePublishModal\">取消</button>\n        <button class=\"confirm-btn\" bindtap=\"publishPost\" disabled=\"{{!canPublish}}\">发布</button>\n      </view>\n    </view>\n  </view>\n\n  <!-- 回到顶部按钮 -->\n  <view class=\"back-to-top\" wx:if=\"{{showBackToTop}}\" bindtap=\"backToTop\">\n    <icon type=\"clear\" size=\"20\" style=\"transform: rotate(180deg);\"></icon>\n  </view>\n</view>\n"}