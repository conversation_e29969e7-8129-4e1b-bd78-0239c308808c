{"chunk": 7, "numChunks": 9, "fileHash": "u79DhtWBcqv2jyfo/kouN6jRcunleYVqTQDoY+P3gFo=", "filePath": "pages/bazi/bazi.wxss", "content": ".loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 60vh;\n}\n\n.loading-spinner {\n  width: 80rpx;\n  height: 80rpx;\n  border: 6rpx solid #f5f0ff;\n  border-top-color: #9575cd;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  to { transform: rotate(360deg); }\n}\n\n.loading-text {\n  margin-top: 30rpx;\n  font-size: 30rpx;\n  color: #666;\n}\n\n/* 错误提示样式 */\n.error-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 60vh;\n  padding: 40rpx;\n}\n\n.error-text {\n  font-size: 30rpx;\n  color: #ff6b6b;\n  text-align: center;\n  margin-bottom: 40rpx;\n}\n\n.retry-button {\n  padding: 20rpx 60rpx;\n  background: linear-gradient(135deg, #9575cd 0%, #7e57c2 100%);\n  color: white;\n  border-radius: 50rpx;\n  font-size: 30rpx;\n  font-weight: 500;\n  border: none;\n  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.3);\n}\n\n/* 动画效果 */\n.fade-in {\n  animation: fadeIn 0.3s ease-out;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20rpx);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n"}