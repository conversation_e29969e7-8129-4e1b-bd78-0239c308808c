{"chunk": 13, "numChunks": 18, "fileHash": "yi4gCVr62AiqkHDrZGxGnPy2D7On+5yxIebQ7IGvQ04=", "filePath": "pages/community/community.js", "content": "// pages/community/community.js\nPage({\n  async publishPost() {\n    const { title, content, images, categoryIndex, tag } = this.data.newPost\n    const category = this.data.categories[categoryIndex]\n    if (!title.trim()) {\n      wx.showToast({\n        title: '请输入标题',\n        icon: 'none'\n      })\n      return\n    }\n    if (!content.trim()) {\n      wx.showToast({\n        title: '请输入内容',\n        icon: 'none'\n      })\n      return\n    }\n    wx.showLoading({\n      title: '发布中...',\n      mask: true\n    })"}