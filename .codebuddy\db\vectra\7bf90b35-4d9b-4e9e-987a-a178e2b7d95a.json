{"chunk": 4, "numChunks": 12, "fileHash": "5X5f8yfnxquoBxYzBGhnUPxPUBZeZKQywMAReox6cBk=", "filePath": "pages/bazi/index.js", "content": "const app = getApp()\nPage({\n  onTimeChange(e) {\n    this.setData({ birthTime: e.detail.value })\n    this.checkCanSubmit()\n  },\n  onCalendarSwitch(e) {\n    this.setData({ isLunar: e.detail.value })\n  },\n  // 检查是否可以提交\n  checkCanSubmit() {\n    const { name, gender, birthDate, birthTime } = this.data\n    const canSubmit = name && gender && birthDate && birthTime\n    this.setData({ canSubmit })\n  },\n  // 初始化图表\n  initCharts() {\n    this.wuxingChart = new WuxingChart()\n    this.yunshiChart = new YunshiChart()\n  },\n  // 分析按钮点击"}