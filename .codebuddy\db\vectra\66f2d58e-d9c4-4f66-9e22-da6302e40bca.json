{"chunk": 3, "numChunks": 10, "fileHash": "xkTYshMjb1fCus39fEPzOqyYq9soXCPLKD0cb2FPCl8=", "filePath": "pages/marriage/marriage.js", "content": "// marriage.js\nPage({\n  data: {\n    // 男方信息\n    maleInfo: {\n      name: '',\n      birthDate: '',\n      birthTime: ''\n    },\n    // 女方信息\n    femaleInfo: {\n      name: '',\n      birthDate: '',\n      birthTime: ''\n    },\n    // 分析结果\n    analysisResult: null,\n    // 是否可以提交\n    canSubmit: false,\n    // 加载状态\n    isLoading: false\n  },\n  onLoad(options) {\n    // 如果有缓存的数据，加载缓存数据\n    this.loadCachedData()\n  },\n  // 加载缓存数据\n  loadCachedData() {\n    try {\n      const cachedData = wx.getStorageSync('marriageAnalysisData')\n      if (cachedData) {\n        this.setData({\n          maleInfo: cachedData.maleInfo || this.data.maleInfo,\n          femaleInfo: cachedData.femaleInfo || this.data.femaleInfo\n        })\n        this.checkCanSubmit()\n      }\n    } catch (error) {\n      console.error('加载缓存数据失败:', error)\n    }\n  },\n  // 保存数据到缓存\n  saveCachedData() {\n    try {\n      const { maleInfo, femaleInfo } = this.data\n      wx.setStorageSync('marriageAnalysisData', {\n        maleInfo,\n        femaleInfo,\n        timestamp: new Date().getTime()\n      })\n    } catch (error) {\n      console.error('保存缓存数据失败:', error)\n    }\n  },\n  // 男方姓名输入\n  onMaleNameInput(e) {\n    this.setData({\n      'maleInfo.name': e.detail.value.trim()\n    })\n    this.checkCanSubmit()\n  },\n  // 男方日期选择"}