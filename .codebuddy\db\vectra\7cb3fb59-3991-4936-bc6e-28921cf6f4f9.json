{"chunk": 0, "numChunks": 2, "fileHash": "O0rNisqoP8E08RCfNEYLUpjs3X2a9r5sxFL1BC9wbMo=", "filePath": "pages/my/my.wxss", "content": "/* pages/my/my.wxss */\npage {\n  background-color: var(--background-color) !important;\n}\n\n.container {\n  padding: 24rpx;\n  min-height: 100vh;\n  background-color: var(--background-color) !important;\n}\n\n/* 用户信息区域 */\n.user-info {\n  background: var(--primary-color);\n  padding: 40rpx 32rpx;\n  border-radius: 24rpx;\n  margin-bottom: 24rpx;\n  box-shadow: 0 4rpx 12rpx var(--shadow-color);\n  display: flex;\n  align-items: center;\n}\n\n.avatar {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 60rpx;\n  border: 4rpx solid var(--primary-lightest);\n  margin-right: 24rpx;\n}\n\n.user-detail {\n  flex: 1;\n}\n\n.nickname {\n  font-size: 36rpx;\n  color: #FFFFFF;\n  font-weight: bold;\n  margin-bottom: 8rpx;\n}\n\n.user-id {\n  font-size: 28rpx;\n  color: var(--primary-lightest);\n}\n\n/* 功能列表 */\n.function-list {\n  background: var(--card-background);\n  border-radius: 16rpx;\n  padding: 24rpx;\n  margin-bottom: 24rpx;\n  box-shadow: 0 4rpx 12rpx var(--shadow-color);\n  border: 2rpx solid var(--border-color);\n}\n\n.function-item {\n  display: flex;\n  align-items: center;\n  padding: 24rpx 0;\n  border-bottom: 2rpx solid var(--border-color);\n}\n\n.function-item:last-child {\n  border-bottom: none;\n}\n\n.function-icon {\n  width: 48rpx;\n  height: 48rpx;\n"}