{"chunk": 1, "numChunks": 5, "fileHash": "IzNtCZVw1h+lk+yR+AP9FPNmtDYd4TsMmzGYHb/6Ia8=", "filePath": "pages/hehun/index.wxss", "content": "  padding: 30rpx;\n  margin-bottom: 30rpx;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n  padding-bottom: 20rpx;\n  border-bottom: 2rpx solid #eee;\n}\n\n.bazi-comparison {\n  display: flex;\n  flex-direction: column;\n  gap: 30rpx;\n}\n\n.person-bazi {\n  flex: 1;\n}\n\n.person-title {\n  font-size: 28rpx;\n  color: #666;\n  margin-bottom: 20rpx;\n  display: block;\n}\n\n.bazi-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 20rpx;\n}\n\n.grid-item {\n  text-align: center;\n}\n\n.pillar-name {\n  font-size: 26rpx;\n  color: #666;\n  margin-bottom: 10rpx;\n  display: block;\n}\n\n.pillar-content {\n  background: #f8f8f8;\n  padding: 16rpx;\n  border-radius: 8rpx;\n}\n\n.pillar-content text {\n  font-size: 26rpx;\n  color: #333;\n}\n\n.wuxing-analysis {\n  margin-top: 20rpx;\n}\n\n.wuxing-item {\n  margin-bottom: 20rpx;\n}\n\n.element-name {\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 10rpx;\n  display: block;\n}\n\n.marriage-score {\n  text-align: center;\n  margin: 30rpx 0;\n}\n\n.score {\n  font-size: 72rpx;\n  font-weight: bold;\n  color: #4a5568;\n}\n\n.max-score {\n  font-size: 32rpx;\n"}