{"chunk": 1, "numChunks": 5, "fileHash": "lIcWOgwH6HogZZCgkr5fpW32d4QAA+3E2RZGYWvnfwk=", "filePath": "pages/xingming/index.wxss", "content": "  font-size: 28rpx;\n  color: #333;\n}\n\n.picker {\n  width: 100%;\n  height: 88rpx;\n  background: #F8F9FD;\n  border-radius: 12rpx;\n  padding: 0 24rpx;\n  font-size: 28rpx;\n  color: #333;\n  display: flex;\n  align-items: center;\n}\n\n.picker.placeholder {\n  color: #999;\n}\n\n/* 性别选择器 */\n.gender-picker {\n  display: flex;\n  gap: 20rpx;\n}\n\n.gender-option {\n  flex: 1;\n  height: 88rpx;\n  background: #F8F9FD;\n  border-radius: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  color: #666;\n  transition: all 0.3s;\n}\n\n.gender-option.active {\n  background: #FF69B4;\n  color: #fff;\n}\n\n/* 提交按钮 */\n.submit-section {\n  margin: 30rpx;\n}\n\n.submit-btn {\n  width: 100%;\n  height: 88rpx;\n  background: linear-gradient(135deg, #FF69B4 0%, #FF8C69 100%);\n  border-radius: 44rpx;\n  color: #fff;\n  font-size: 32rpx;\n  font-weight: bold;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.submit-btn.disabled {\n  opacity: 0.6;\n}\n\n.price {\n  position: absolute;\n  right: 30rpx;\n  font-size: 28rpx;\n  font-weight: normal;\n}\n\n/* 结果区域 */\n.result-section {\n  margin: 30rpx;\n}\n\n"}