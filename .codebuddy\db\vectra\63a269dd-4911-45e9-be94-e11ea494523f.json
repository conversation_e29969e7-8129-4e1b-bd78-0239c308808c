{"chunk": 0, "numChunks": 5, "fileHash": "rXA9I8zblvy793sn1s5fHDcAnMr2WCJwMU+H52clkck=", "filePath": "pages/birth-info/birth-info.wxss", "content": "/* pages/birth-info/birth-info.wxss */\n.container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 0;\n}\n\n/* 顶部标题区域 */\n.header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 60rpx 40rpx 40rpx;\n  color: white;\n}\n\n.header-content {\n  flex: 1;\n}\n\n.title {\n  font-size: 56rpx;\n  font-weight: bold;\n  margin-bottom: 12rpx;\n  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\n}\n\n.subtitle {\n  font-size: 28rpx;\n  opacity: 0.9;\n  line-height: 1.4;\n}\n\n.header-decoration {\n  margin-left: 20rpx;\n}\n\n.icon {\n  font-size: 80rpx;\n  opacity: 0.8;\n}\n\n/* 表单容器 */\n.form-container {\n  background: #f8f9fa;\n  border-radius: 40rpx 40rpx 0 0;\n  padding: 40rpx;\n  min-height: calc(100vh - 200rpx);\n}\n\n/* 信息卡片 */\n.info-card {\n  background: white;\n  border-radius: 24rpx;\n  padding: 32rpx;\n  margin-bottom: 24rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n  border: 1rpx solid rgba(102, 126, 234, 0.1);\n}\n\n.card-header {\n  display: flex;\n  align-items: center;\n"}