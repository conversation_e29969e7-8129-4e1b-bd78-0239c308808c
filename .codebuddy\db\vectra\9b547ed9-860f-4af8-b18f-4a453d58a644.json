{"chunk": 3, "numChunks": 5, "fileHash": "yLT5DZWgzA1pMyoKDto/o26wX3DjjCYshSeMtgr/cn4=", "filePath": "pages/xingming/index.wxml", "content": "          <text class=\"label\">日柱：</text>\n          <text class=\"value\">{{baziInfo.day}}</text>\n        </view>\n        <view class=\"bazi-row\">\n          <text class=\"label\">时柱：</text>\n          <text class=\"value\">{{baziInfo.time}}</text>\n        </view>\n      </view>\n      <view class=\"bazi-analysis\">{{baziAnalysis}}</view>\n    </view>\n\n    <!-- 吉凶分析 -->\n    <view class=\"analysis-card\">\n      <view class=\"card-title\">\n        <image class=\"title-icon\" src=\"/assets/icons/jixiong.png\"/>\n        <text>吉凶分析</text>\n      </view>\n      <view class=\"jixiong-list\">\n        <view class=\"jixiong-item\" wx:for=\"{{jixiongList}}\" wx:key=\"type\">\n          <view class=\"jixiong-type\">{{item.type}}</view>\n          <view class=\"jixiong-desc\">{{item.description}}</view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 建议方案 -->\n    <view class=\"analysis-card\">\n      <view class=\"card-title\">\n        <image class=\"title-icon\" src=\"/assets/icons/advice.png\"/>\n        <text>建议方案</text>\n      </view>\n      <view class=\"advice-list\">\n        <view class=\"advice-item\" wx:for=\"{{adviceList}}\" wx:key=\"title\">\n          <view class=\"advice-title\">{{item.title}}</view>\n          <view class=\"advice-content\">{{item.content}}</view>\n        </view>\n      </view>\n    </view>\n  </view>\n\n  <!-- 底部分享按钮 -->\n  <view class=\"share-section\" wx:if=\"{{showResult}}\">\n"}