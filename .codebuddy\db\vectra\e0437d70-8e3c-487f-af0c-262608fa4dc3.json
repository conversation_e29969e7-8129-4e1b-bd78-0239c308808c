{"chunk": 8, "numChunks": 38, "fileHash": "DpyinnhZJO6jrs0/VY0BXwrNjORlEQ1BeRywBJybhxQ=", "filePath": "pages/ai-chat/ai-chat.js", "content": "const app = getApp()\nPage({\n  /**\n   * 处理悬浮按钮点击\n   */\n  onFabClick() {\n    const newExpanded = !this.data.isInputExpanded\n    this.setData({\n      isInputExpanded: newExpanded\n    })\n\n    // 更新悬浮球图标\n    if (newExpanded) {\n      this.setData({ fabIcon: '✕' })\n    } else {\n      this.setData({ fabIcon: '💬' })\n    }\n  },\n  /**\n   * 清空聊天记录\n   */\n  clearChat() {\n    wx.showModal({\n      title: '确认清空',\n      content: '确定要清空所有聊天记录吗？',\n      success: (res) => {\n        if (res.confirm) {\n          this.setData({\n            messages: [],\n            showQuickActions: true\n          })\n          wx.showToast({\n            title: '已清空',\n            icon: 'success'\n          })\n        }\n      }\n    })\n  },\n  /**\n   * 显示设置\n   */\n  showSettings() {\n    wx.showActionSheet({\n      itemList: ['清空聊天记录', '反馈建议', '使用帮助'],\n      success: (res) => {\n        switch (res.tapIndex) {\n          case 0:\n            this.clearChat()\n            break\n          case 1:\n            navigationManager.navigateTo('/pages/feedback/feedback')\n            break\n          case 2:\n            this.showHelp()\n            break\n        }\n      }\n    })\n  },\n  /**\n   * 显示帮助信息\n   */"}