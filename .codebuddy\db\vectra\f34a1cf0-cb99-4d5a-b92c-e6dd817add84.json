{"chunk": 14, "numChunks": 15, "fileHash": "An9WOmGw20FYVB4+SrCnlnnpOBHN6cohdD4SvY7rXL8=", "filePath": "pages/ziwei/ziwei.js", "content": "// ziwei.js\nPage({\n  generateHealthDetails(stars) {\n    const healthStars = stars.filter(s => s.position === 6) // 疾厄宫\n    const luckyStars = healthStars.filter(s => s.nature === '吉')\n    const evilStars = healthStars.filter(s => s.nature === '凶')\n    \n    const details = []\n    \n    // 根据吉星和煞星生成详细预测\n    if (luckyStars.length > 0) {\n      details.push({\n        title: '健康状况',\n        content: `您的身体状况较好，有${luckyStars.map(s => s.name).join('、')}等吉星相助，但仍需注意保养。`\n      })\n    }\n    \n    if (evilStars.length > 0) {\n      details.push({\n        title: '健康风险',\n        content: `您的健康需注意，有${evilStars.map(s => s.name).join('、')}等煞星影响，易有疾病，建议定期体检。`\n      })\n    }\n    \n    // 如果没有特定星耀，添加通用预测\n    if (details.length === 0) {\n      details.push({\n        title: '健康建议',\n        content: '您的健康状况较为平稳，建议保持规律作息，均衡饮食，适度运动。'\n      })\n    }\n    \n    return details\n  },"}