{"chunk": 15, "numChunks": 19, "fileHash": "PEPeuJm140RAD7VBd85WlnsfcWUdoorqVSQj8ISByl0=", "filePath": "pages/ai-chat/ai-chat.wxss", "content": "  justify-content: center;\n  margin-bottom: 12rpx;\n  width: 100%;\n}\n\n/* 消息时间样式 */\n.message-time {\n  font-size: 24rpx;\n  color: var(--text-muted);\n  background-color: rgba(0, 0, 0, 0.04);\n  padding: 6rpx 16rpx;\n  border-radius: 12rpx;\n  display: inline-block;\n  line-height: 1.4;\n}\n\n/* 用户消息 */\n.message-user {\n  display: flex;\n  justify-content: flex-end;\n  align-items: flex-start;\n  gap: 16rpx;\n  max-width: 100%;\n}\n\n.message-user .message-content {\n  background: var(--gradient-primary);\n  color: white;\n  padding: 24rpx 28rpx;\n  border-radius: 24rpx 24rpx 8rpx 24rpx;\n  max-width: 70%;\n  margin-right: 0;\n  box-shadow: 0 8rpx 24rpx rgba(121, 40, 202, 0.25);\n  position: relative;\n  overflow: hidden;\n  word-break: break-all;\n}\n\n.message-user .message-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  background: var(--gradient-secondary);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 32rpx;\n  box-shadow: 0 4rpx 12rpx var(--shadow-color);\n  flex-shrink: 0;\n}\n\n\n\n/* 消息文本 */\n.message-text {\n  font-size: 30rpx;\n  line-height: 1.7;\n  color: var(--text-primary);\n  word-wrap: break-word;\n"}