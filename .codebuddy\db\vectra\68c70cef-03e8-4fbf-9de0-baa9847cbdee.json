{"chunk": 0, "numChunks": 3, "fileHash": "r0aOo34YwMB6Jqlyx1ur0PKXSDJ+/cbp6WOdiOy5hjY=", "filePath": "pages/profile/index.wxss", "content": "/* pages/profile/index.wxss */\n.container {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  padding-bottom: 40rpx;\n}\n\n/* 用户信息卡片 */\n.user-card {\n  background: linear-gradient(135deg, #07c160, #0ab856);\n  padding: 40rpx 30rpx;\n  color: #fff;\n  margin-bottom: 20rpx;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n\n.avatar {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 60rpx;\n  border: 4rpx solid rgba(255, 255, 255, 0.4);\n}\n\n.info-right {\n  margin-left: 20rpx;\n  flex: 1;\n}\n\n.nickname {\n  font-size: 36rpx;\n  font-weight: 500;\n  margin-bottom: 10rpx;\n}\n\n.user-level {\n  display: flex;\n  align-items: center;\n}\n\n.level-tag {\n  background-color: rgba(255, 255, 255, 0.2);\n  padding: 4rpx 12rpx;\n  border-radius: 20rpx;\n  font-size: 24rpx;\n  margin-right: 20rpx;\n}\n\n.points {\n  font-size: 24rpx;\n  opacity: 0.9;\n}\n\n.card-bottom {\n  display: flex;\n  justify-content: space-around;\n  padding-top: 20rpx;\n  border-top: 2rpx solid rgba(255, 255, 255, 0.1);\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-num {\n  display: block;\n  font-size: 32rpx;\n  font-weight: 500;\n"}