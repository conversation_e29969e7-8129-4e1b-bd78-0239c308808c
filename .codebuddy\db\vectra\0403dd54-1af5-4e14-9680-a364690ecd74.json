{"chunk": 15, "numChunks": 18, "fileHash": "/DUygIsVyFRM4gmsdi8uiMJN8RFlyElKDKTgRRrPFCI=", "filePath": "utils/hehun/calculator.js", "content": "// 五行生克关系\nclass HehunCalculator {\n  generateAdvice(score) {\n    const adviceList = []\n\n    // 婚期选择\n    adviceList.push({\n      title: '婚期选择',\n      content: this.getWeddingDateAdvice()\n    })\n\n    // 婚后相处\n    adviceList.push({\n      title: '婚后相处',\n      content: this.getRelationshipAdvice(score)\n    })\n\n    // 事业发展\n    adviceList.push({\n      title: '事业发展',\n      content: this.getCareerAdvice()\n    })\n\n    // 家庭和谐\n    adviceList.push({\n      title: '家庭和谐',\n      content: this.getFamilyAdvice()\n    })\n\n    return adviceList\n  }"}