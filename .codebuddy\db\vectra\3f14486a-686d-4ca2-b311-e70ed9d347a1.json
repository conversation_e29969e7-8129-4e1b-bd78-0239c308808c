{"chunk": 3, "numChunks": 7, "fileHash": "nGMyzD5kIPI75b8ATQIO6k03owW27BytPf8s1C7ATWc=", "filePath": "pages/bazi/index.wxss", "content": "/* 日期时间选择器 */\n.picker.placeholder {\n  color: #999;\n}\n\n/* 农历阳历切换 */\n.calendar-text {\n  font-size: 28rpx;\n  color: #666;\n  margin-left: 16rpx;\n}\n\n/* 提交按钮 */\n.submit-section {\n  margin: 30rpx;\n}\n\n.submit-btn {\n  width: 100%;\n  height: 88rpx;\n  background: linear-gradient(135deg, #6236FF 0%, #9F6EFF 100%);\n  border-radius: 44rpx;\n  color: #fff;\n  font-size: 32rpx;\n  font-weight: bold;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.submit-btn.disabled {\n  opacity: 0.6;\n}\n\n.price {\n  position: absolute;\n  right: 30rpx;\n  font-size: 28rpx;\n  font-weight: normal;\n}\n\n/* 分析结果区域 */\n.result-section {\n  margin: 30rpx;\n}\n\n/* 分析卡片通用样式 */\n.analysis-card {\n  background: #fff;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);\n}\n\n.card-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 24rpx;\n  gap: 12rpx;\n}\n\n.title-icon {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n/* 八字排盘图 */\n.bazi-chart {\n  width: 100%;\n  height: 400rpx;\n  margin-bottom: 30rpx;\n}\n\n.bazi-canvas {\n"}