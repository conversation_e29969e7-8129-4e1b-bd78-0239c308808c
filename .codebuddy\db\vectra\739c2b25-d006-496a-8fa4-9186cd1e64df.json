{"chunk": 6, "numChunks": 7, "fileHash": "OC4bchRgffgqpj5ffRE3rPdsxwNsfIQSvzKsBv0wUBE=", "filePath": "pages/profile/index.js", "content": "const app = getApp()\nPage({\n  },\n  onLoad() {\n    this.getUserInfo()\n    this.getStatistics()\n  },\n  onShow() {\n    if (typeof this.getTabBar === 'function' && this.getTabBar()) {\n      this.getTabBar().setData({\n        selected: 4  // 个人中心的 tabBar 索引\n      })\n    }\n  },\n  // 获取用户信息\n  getUserInfo() {\n    const userInfo = wx.getStorageSync('userInfo')\n    if (userInfo) {\n      this.setData({\n        userInfo\n      })\n    }\n  },\n  // 获取统计信息\n  async getStatistics() {\n    try {\n      const res = await wx.cloud.callFunction({\n        name: 'getUserStatistics'\n      })\n      \n      if (res.result) {\n        this.setData({\n          statistics: res.result\n        })\n      }\n    } catch (err) {\n      console.error('获取用户统计信息失败:', err)\n    }\n  },\n  // 登录\n  handleLogin() {\n    if (!this.data.userInfo.nickName || this.data.userInfo.nickName === '未登录') {\n      wx.navigateTo({\n        url: '/pages/login/index'\n      })\n    }\n  },\n  // 跳转到功能页面\n  navigateToFunction(e) {\n    const { url } = e.currentTarget.dataset\n    wx.navigateTo({ url })\n  },\n  // 跳转到设置页面\n  navigateToSetting(e) {\n    const { url } = e.currentTarget.dataset\n    wx.navigateTo({ url })\n  },\n  // 退出登录"}