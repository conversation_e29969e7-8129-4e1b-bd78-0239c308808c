{"chunk": 7, "numChunks": 9, "fileHash": "yDcz6SvSgZVWvKwtTExumLiXk7y0FYiP8gZ9qIHubRA=", "filePath": "pages/fengshui/fengshui.wxss", "content": "  height: 100%;\n  border-radius: 4rpx;\n  transition: width 0.3s ease;\n}\n\n/* 建议列表样式 */\n.suggestions-list {\n  padding: 8rpx 0;\n}\n\n.suggestion-item {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 16rpx;\n  font-size: 28rpx;\n  line-height: 1.5;\n}\n\n.suggestion-item:last-child {\n  margin-bottom: 0;\n}\n\n.suggestion-dot {\n  color: #667eea;\n  margin-right: 12rpx;\n  font-weight: bold;\n}\n\n.suggestion-text {\n  flex: 1;\n  color: #666;\n}\n\n/* 操作按钮样式 */\n.action-buttons {\n  display: flex;\n  gap: 20rpx;\n  margin-top: 40rpx;\n}\n\n.reset-btn, .share-btn {\n  flex: 1;\n  height: 88rpx;\n  border-radius: 20rpx;\n  font-size: 32rpx;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8rpx;\n  border: none;\n  transition: all 0.3s ease;\n}\n\n.reset-btn {\n  background: linear-gradient(135deg, #ff7043 0%, #ff8a65 100%);\n  color: white;\n  box-shadow: 0 4rpx 16rpx rgba(255, 112, 67, 0.3);\n}\n\n.share-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);\n}\n\n"}