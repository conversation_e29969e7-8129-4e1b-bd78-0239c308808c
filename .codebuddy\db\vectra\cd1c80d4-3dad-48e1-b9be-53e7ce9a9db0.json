{"chunk": 1, "numChunks": 4, "fileHash": "jubZTzyunGc3ITYrDRZliCEjMyZub9rUjJcBcvHhj28=", "filePath": "pages/name-test/name-test.wxml", "content": "    </view>\n    <view class=\"loading-text\">正在分析姓名...</view>\n  </view>\n\n  <!-- 测试结果 -->\n  <view class=\"result-section\" wx:if=\"{{testResult && !isLoading}}\">\n    <view class=\"result-header\">\n      <view class=\"test-name\">{{testResult.fullName}}</view>\n      <view class=\"overall-score\">\n        <text class=\"score-label\">综合评分</text>\n        <text class=\"score-value\">{{testResult.totalScore}}分</text>\n      </view>\n    </view>\n\n    <!-- 五格数理 -->\n    <view class=\"wuge-section\">\n      <view class=\"section-title\">五格数理</view>\n      <view class=\"wuge-grid\">\n        <view class=\"wuge-item\" wx:for=\"{{testResult.wuge}}\" wx:key=\"type\">\n          <view class=\"wuge-name\">{{item.name}}</view>\n          <view class=\"wuge-number\">{{item.number}}</view>\n          <view class=\"wuge-level {{item.level}}\">{{item.levelText}}</view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 三才配置 -->\n    <view class=\"sancai-section\">\n      <view class=\"section-title\">三才配置</view>\n      <view class=\"sancai-result\">\n        <view class=\"sancai-item\">\n          <text class=\"sancai-label\">天格</text>\n          <text class=\"sancai-value\">{{testResult.sancai.tian}}</text>\n        </view>\n        <view class=\"sancai-item\">\n          <text class=\"sancai-label\">人格</text>\n          <text class=\"sancai-value\">{{testResult.sancai.ren}}</text>\n        </view>\n        <view class=\"sancai-item\">\n          <text class=\"sancai-label\">地格</text>\n"}