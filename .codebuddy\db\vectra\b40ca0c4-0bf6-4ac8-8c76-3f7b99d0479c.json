{"chunk": 4, "numChunks": 7, "fileHash": "PQSUOKQzQ1nTJojwaszEOU93ZW43HSbpVKnnb8k/pfY=", "filePath": "pages/marriage/index.wxss", "content": "  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n  border: 1rpx solid rgba(102, 126, 234, 0.1);\n}\n\n.analysis-tabs {\n  white-space: nowrap;\n  padding: 8rpx 0;\n}\n\n.analysis-tab {\n  display: inline-block;\n  padding: 16rpx 24rpx;\n  margin-right: 16rpx;\n  background: #f8f9fa;\n  border-radius: 16rpx;\n  font-size: 28rpx;\n  color: #666;\n  border: 1rpx solid #e8e8e8;\n  transition: all 0.3s ease;\n}\n\n.analysis-tab.active {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border-color: #667eea;\n  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);\n}\n\n/* 评分区域 */\n.score-section {\n  text-align: center;\n  padding: 20rpx 0;\n}\n\n.score-ring {\n  position: relative;\n  width: 240rpx;\n  height: 240rpx;\n  margin: 0 auto 30rpx;\n  background: conic-gradient(from 0deg, #667eea 0%, #764ba2 70%, #e8e8e8 100%);\n  border-radius: 50%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.2);\n}\n\n.score-ring::before {\n"}