{"chunk": 2, "numChunks": 5, "fileHash": "pt0uFokY5+WPeovdWLk6lUfTQUcY8vS6GhDpk14daRU=", "filePath": "pages/fengshui/index.wxml", "content": "          <picker mode=\"selector\" range=\"{{bathNums}}\" value=\"{{bathNumIndex}}\" bindchange=\"onBathNumChange\">\n            <view class=\"number-picker\">{{bathNumIndex > -1 ? bathNums[bathNumIndex] : '0'}}</view>\n          </picker>\n        </view>\n      </view>\n    </view>\n  </view>\n\n  <!-- 提交按钮 -->\n  <view class=\"submit-section\">\n    <button class=\"submit-btn {{canSubmit ? '' : 'disabled'}}\" bindtap=\"onAnalyze\" disabled=\"{{!canSubmit}}\">\n      <text>开始分析</text>\n      <text class=\"price\" wx:if=\"{{price > 0}}\">￥{{price}}</text>\n    </button>\n  </view>\n\n  <!-- 分析结果 -->\n  <view class=\"result-section\" wx:if=\"{{showResult}}\">\n    <!-- 整体评分 -->\n    <view class=\"analysis-card\">\n      <view class=\"card-title\">\n        <image class=\"title-icon\" src=\"/assets/icons/score.png\"/>\n        <text>风水评分</text>\n      </view>\n      <view class=\"score-display\">\n        <view class=\"score\">{{totalScore}}</view>\n        <view class=\"max-score\">/100</view>\n      </view>\n      <view class=\"score-desc\">{{scoreDesc}}</view>\n    </view>\n\n    <!-- 八方位分析 -->\n    <view class=\"analysis-card\">\n      <view class=\"card-title\">\n        <image class=\"title-icon\" src=\"/assets/icons/bagua.png\"/>\n        <text>八方位分析</text>\n      </view>\n      <view class=\"bagua-chart\">\n        <canvas canvas-id=\"baguaCanvas\" class=\"bagua-canvas\"></canvas>\n      </view>\n"}