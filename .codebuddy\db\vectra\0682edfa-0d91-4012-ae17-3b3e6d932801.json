{"chunk": 0, "numChunks": 6, "fileHash": "dNwun7ERRMYazRQ1UOKQO3ggF1ZH6El+O2/hG+Rh3/o=", "filePath": "pages/chat-list/chat-list.wxml", "content": "<!--聊天列表页面-->\n<view class=\"page-container\">\n  <!-- 头部搜索栏 -->\n  <view class=\"search-header\">\n    <view class=\"search-box\">\n      <view class=\"search-icon\">🔍</view>\n      <input \n        class=\"search-input\"\n        placeholder=\"搜索聊天记录\"\n        value=\"{{searchKeyword}}\"\n        bindinput=\"onSearchInput\"\n        bindconfirm=\"searchChats\"\n      />\n      <view class=\"search-clear\" wx:if=\"{{searchKeyword}}\" bindtap=\"clearSearch\">×</view>\n    </view>\n  </view>\n\n  <!-- 快速功能 -->\n  <view class=\"quick-functions\">\n    <view class=\"function-item\" bindtap=\"startNewChat\">\n      <view class=\"function-icon new-chat\">💬</view>\n      <text class=\"function-text\">新建对话</text>\n    </view>\n    <view class=\"function-item\" bindtap=\"viewFavoriteChats\">\n      <view class=\"function-icon favorite\">⭐</view>\n      <text class=\"function-text\">收藏对话</text>\n    </view>\n    <view class=\"function-item\" bindtap=\"exportChatHistory\">\n      <view class=\"function-icon export\">📋</view>\n      <text class=\"function-text\">导出记录</text>\n    </view>\n  </view>\n\n  <!-- 聊天列表 -->\n  <view class=\"chat-list-container\">\n    <!-- 固定对话 -->\n    <view class=\"pinned-section\" wx:if=\"{{pinnedChats.length > 0}}\">\n      <view class=\"section-title\">📌 置顶对话</view>\n      <view class=\"chat-list\">\n        <view \n          class=\"chat-item pinned\"\n          wx:for=\"{{pinnedChats}}\"\n          wx:key=\"id\"\n"}