{"chunk": 1, "numChunks": 4, "fileHash": "JxYmunO+MuN/nw3IhbaSBWIwBZxgEQi9Pk/YphlGNzQ=", "filePath": "pages/post-detail/post-detail.wxml", "content": "      <!-- 相关标签 -->\n      <view class=\"article-tags\" wx:if=\"{{article.tags && article.tags.length}}\">\n        <view class=\"tag-item\" wx:for=\"{{article.tags}}\" wx:key=\"index\">\n          #{{item}}\n        </view>\n      </view>\n    </view>\n    \n    <!-- 互动区域 -->\n    <view class=\"interaction-section\">\n      <view class=\"interaction-bar\">\n        <view class=\"interaction-item {{liked ? 'active' : ''}}\" bindtap=\"handleLike\">\n          <text class=\"iconfont icon-like\">👍</text>\n          <text>{{article.likeCount || 0}}</text>\n        </view>\n        <view class=\"interaction-item\" bindtap=\"handleCollect\">\n          <text class=\"iconfont icon-collect\">{{collected ? '⭐' : '☆'}}</text>\n          <text>收藏</text>\n        </view>\n        <view class=\"interaction-item\" bindtap=\"handleShare\">\n          <text class=\"iconfont icon-share\">📤</text>\n          <text>分享</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 相关推荐 -->\n    <view class=\"related-section\" wx:if=\"{{relatedArticles.length}}\">\n      <view class=\"section-title\">相关推荐</view>\n      <view class=\"related-list\">\n        <view class=\"related-item\" wx:for=\"{{relatedArticles}}\" wx:key=\"id\" bindtap=\"goToArticle\" data-id=\"{{item.id}}\">\n          <image class=\"related-image\" src=\"{{item.coverImage}}\" mode=\"aspectFill\" />\n          <view class=\"related-info\">\n            <text class=\"related-title\">{{item.title}}</text>\n            <text class=\"related-meta\">{{item.author.name}} · {{item.readCount}}阅读</text>\n"}