{"chunk": 14, "numChunks": 18, "fileHash": "/DUygIsVyFRM4gmsdi8uiMJN8RFlyElKDKTgRRrPFCI=", "filePath": "utils/hehun/calculator.js", "content": "// 五行生克关系\nclass HehunCalculator {\n  generateWuxingChartData() {\n    const maleWuxing = this.getMainWuxing(this.maleBazi)\n    const femaleWuxing = this.getMainWuxing(this.femaleBazi)\n\n    return [\n      { name: '金', value: this.calculateWuxingValue('金', maleWuxing, femaleWuxing), color: '#FFD700' },\n      { name: '木', value: this.calculateWuxingValue('木', maleWuxing, femaleWuxing), color: '#90EE90' },\n      { name: '水', value: this.calculateWuxingValue('水', maleWuxing, femaleWuxing), color: '#87CEEB' },\n      { name: '火', value: this.calculateWuxingValue('火', maleWuxing, femaleWuxing), color: '#FF6B6B' },\n      { name: '土', value: this.calculateWuxingValue('土', maleWuxing, femaleWuxing), color: '#DEB887' }\n    ]\n  }\n  calculateWuxingValue(wuxing, maleWuxing, femaleWuxing) {\n    let value = 20 // 基础值\n    if (wuxing === maleWuxing) value += 30\n    if (wuxing === femaleWuxing) value += 30\n    if (WU_XING_RELATIONS[wuxing].generates === maleWuxing) value += 10\n    if (WU_XING_RELATIONS[wuxing].generates === femaleWuxing) value += 10\n    return value\n  }"}