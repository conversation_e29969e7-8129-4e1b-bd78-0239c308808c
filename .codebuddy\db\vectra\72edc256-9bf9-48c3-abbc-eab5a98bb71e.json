{"chunk": 13, "numChunks": 15, "fileHash": "ydfcj+4tZs2HvARUdw6TErn3fg1suYaeqU0s+F1D6wI=", "filePath": "pages/hehun/index.js", "content": "const app = getApp()\nPage({\n  async analyzeMarriage() {\n    try {\n      this.setData({\n        loading: false,\n        showResult: true,\n        maleResult: {\n          pillars: [\n            { name: '年柱', heavenlyStem: maleResult.yearStem, earthlyBranch: maleResult.yearBranch },\n            { name: '月柱', heavenlyStem: maleResult.monthStem, earthlyBranch: maleResult.monthBranch },\n            { name: '日柱', heavenlyStem: maleResult.dayStem, earthlyBranch: maleResult.dayBranch },\n            { name: '时柱', heavenlyStem: maleResult.hourStem, earthlyBranch: maleResult.hourBranch }\n          ]\n        },\n        femaleResult: {\n          pillars: [\n            { name: '年柱', heavenlyStem: femaleResult.yearStem, earthlyBranch: femaleResult.yearBranch },\n            { name: '月柱', heavenlyStem: femaleResult.monthStem, earthlyBranch: femaleResult.monthBranch },\n            { name: '日柱', heavenlyStem: femaleResult.dayStem, earthlyBranch: femaleResult.dayBranch },\n            { name: '时柱', heavenlyStem: femaleResult.hourStem, earthlyBranch: femaleResult.hourBranch }\n          ]\n        },"}