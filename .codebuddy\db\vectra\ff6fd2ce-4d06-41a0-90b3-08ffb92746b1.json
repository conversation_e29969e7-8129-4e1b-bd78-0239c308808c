{"chunk": 66, "numChunks": 76, "fileHash": "lPYWZE8QItgwSyEsMXHtl6y/HYBCI8BdGaV+1l8ICMA=", "filePath": "docs/md/API_INTERFACE_DOCUMENTATION.md", "content": "  lunar_year SMALLINT,\n  lunar_month TINYINT,\n  lunar_day TINYINT,\n  lunar_leap_month B<PERSON><PERSON>EAN DEFAULT FALSE,\n  zodiac VARCHAR(10),\n  constellation VARCHAR(20),\n  lucky_number TINYINT,\n  bazi JSON,\n  wuxing JSO<PERSON>,\n  is_verified <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE,\n  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n\n  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,\n  INDEX idx_user_id (user_id),\n  INDEX idx_birth_date (birth_year, birth_month, birth_day)\n);\n```\n\n#### 3. 聊天会话表 (chat_sessions)\n```sql\nCREATE TABLE chat_sessions (\n  id VARCHAR(36) PRIMARY KEY,\n  user_id VARCHAR(36) NOT NULL,\n  title VARCHAR(200),\n  status ENUM('active', 'ended', 'archived') DEFAULT 'active',\n  message_count INT DEFAULT 0,\n  total_tokens INT DEFAULT 0,\n  total_cost DECIMAL(10,4) DEFAULT 0,\n  start_time DATETIME DEFAULT CURRENT_TIMESTAMP,\n  end_time DATETIME,\n  last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,\n  metadata JSON,\n  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n\n  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,\n  INDEX idx_user_id (user_id),\n  INDEX idx_status (status),\n  INDEX idx_last_activity (last_activity),\n  INDEX idx_created_at (created_at)\n);\n```\n\n#### 4. 聊天记录表 (chat_messages)\n```sql\nCREATE TABLE chat_messages (\n  id VARCHAR(36) PRIMARY KEY,\n  user_id VARCHAR(36) NOT NULL,\n  session_id VARCHAR(36) NOT NULL,\n  message_type ENUM('user', 'ai', 'system') NOT NULL,\n  content TEXT NOT NULL,\n"}