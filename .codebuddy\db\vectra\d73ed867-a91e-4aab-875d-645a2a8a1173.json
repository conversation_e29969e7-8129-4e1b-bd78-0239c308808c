{"chunk": 15, "numChunks": 38, "fileHash": "DpyinnhZJO6jrs0/VY0BXwrNjORlEQ1BeRywBJybhxQ=", "filePath": "pages/ai-chat/ai-chat.js", "content": "const app = getApp()\nPage({\n  async onQuickActionTap(e) {\n    if (action.query) {\n      // 添加用户消息\n      this.addMessage({\n        type: 'user',\n        content: action.query,\n        timestamp: this.formatTime(new Date())\n      })\n      this.setData({\n        showQuickActions: false,\n        isTyping: true\n      })"}