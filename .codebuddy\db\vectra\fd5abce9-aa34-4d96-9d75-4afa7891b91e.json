{"chunk": 0, "numChunks": 1, "fileHash": "CTnNN0YRAGDVmGptLMduZi0QE5bKZKGKlYV3qFfGQWc=", "filePath": "frontend-implementation/api/auth.js", "content": "// 用户认证相关API\nimport { request } from '../utils/request'\n\n/**\n * 微信登录\n * @param {string} code - 微信登录code\n * @param {object} userInfo - 用户信息\n */\nexport const wxLogin = (code, userInfo = {}) => {\n  return request({\n    url: '/api/auth/wx-login',\n    method: 'POST',\n    data: {\n      code,\n      user_info: userInfo\n    }\n  })\n}\n\n/**\n * 获取用户信息\n */\nexport const getUserProfile = () => {\n  return request({\n    url: '/api/user/profile',\n    method: 'GET'\n  })\n}\n\n/**\n * 更新用户信息\n * @param {object} data - 用户信息\n */\nexport const updateUserProfile = (data) => {\n  return request({\n    url: '/api/user/profile',\n    method: 'PUT',\n    data\n  })\n}\n\n/**\n * 获取用户统计信息\n */\nexport const getUserStatistics = () => {\n  return request({\n    url: '/api/user/statistics',\n    method: 'GET'\n  })\n}\n\n/**\n * 刷新Token\n * @param {string} refreshToken - 刷新Token\n */\nexport const refreshToken = (refreshToken) => {\n  return request({\n    url: '/api/auth/refresh',\n    method: 'POST',\n    data: {\n      refresh_token: refreshToken\n    }\n  })\n}\n\n/**\n * 退出登录\n */\nexport const logout = () => {\n  return request({\n    url: '/api/auth/logout',\n    method: 'POST'\n  })\n}\n"}