{"chunk": 2, "numChunks": 12, "fileHash": "HkVDVZ9giUC0NxXwGhnv0R1SvQ+r07w6mr3mR/NNt+s=", "filePath": "subpages/divination/bazi/bazi.js", "content": "Page({\n  /**\n   * 页面的初始数据\n   */\n  data: {\n    name: '',\n    gender: 'male',\n    birthDate: '',\n    birthTime: '',\n    location: '',\n    analyzing: false,\n    showResult: false,\n    resultData: null,\n    historyList: []\n  },\n  /**\n   * 生命周期函数--监听页面加载\n   */\n  onLoad(options) {\n    this.loadHistory()\n  },\n  /**\n   * 生命周期函数--监听页面显示\n   */\n  onShow() {\n    \n  },\n  /**\n   * 输入姓名\n   */\n  onNameInput(e) {\n    this.setData({\n      name: e.detail.value\n    })\n  },\n  /**\n   * 选择性别\n   */\n  selectGender(e) {\n    const gender = e.currentTarget.dataset.gender\n    this.setData({\n      gender: gender\n    })\n  },\n  /**\n   * 选择出生日期\n   */\n  onDateChange(e) {\n    this.setData({\n      birthDate: e.detail.value\n    })\n  },\n  /**\n   * 选择出生时间\n   */\n  onTimeChange(e) {\n    this.setData({\n      birthTime: e.detail.value\n    })\n  },\n  /**\n   * 输入出生地点\n   */\n  onLocationInput(e) {\n    this.setData({\n      location: e.detail.value\n    })\n  },\n  /**\n   * 分析八字\n   */"}