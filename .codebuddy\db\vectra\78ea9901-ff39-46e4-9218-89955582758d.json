{"chunk": 3, "numChunks": 13, "fileHash": "RdYUonhiW47SLv0/ZKSqV2JJEqqe4g6tc2PZPdqonRM=", "filePath": "pages/post-detail/post-detail.js", "content": "const app = getApp()\nPage({\n  /**\n   * 页面的初始数据\n   */\n  data: {\n    post: null,\n    comments: [],\n    commentText: '',\n    isLiked: false,\n    isLoadingComments: false,\n    hasMoreComments: true,\n    commentPageNum: 1,\n    commentPageSize: 10\n  },\n  /**\n   * 生命周期函数--监听页面加载\n   */\n  onLoad(options) {\n    try {\n      console.log('帖子详情页加载，参数:', options)\n      const postId = options.id\n      \n      if (!postId) {\n        console.error('帖子ID不存在')\n        wx.showToast({\n          title: '帖子ID不存在',\n          icon: 'none'\n        })\n        setTimeout(() => {\n          wx.navigateBack()\n        }, 1500)\n        return\n      }\n      \n      // 从云数据库获取帖子详情\n      this.loadPostDetail(postId)\n      // 从云数据库获取评论列表\n      this.loadComments(postId)\n      // 检查用户是否已点赞\n      this.checkLikeStatus(postId)\n    } catch (error) {\n      console.error('帖子详情页加载出错:', error)\n      wx.showToast({\n        title: '加载失败',\n        icon: 'none'\n      })\n    }\n  },\n  /**\n   * 加载帖子详情\n   */"}