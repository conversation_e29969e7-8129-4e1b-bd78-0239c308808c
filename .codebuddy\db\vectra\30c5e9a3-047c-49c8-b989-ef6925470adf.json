{"chunk": 5, "numChunks": 8, "fileHash": "y2IL59YXs36741pVvCZKEDopMVE9nury7qdEmYs0pAw=", "filePath": "pages/marriage/marriage.wxss", "content": "  align-items: center;\n  justify-content: space-between;\n  padding: 20rpx 0;\n}\n\n.bazi-column {\n  flex: 1;\n  text-align: center;\n}\n\n.person-name {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 16rpx;\n}\n\n.bazi-pillars {\n  display: flex;\n  justify-content: center;\n  gap: 12rpx;\n}\n\n.pillar {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  background: #f8f9fa;\n  border-radius: 12rpx;\n  padding: 12rpx;\n  min-width: 60rpx;\n}\n\n.pillar-top, .pillar-bottom {\n  font-size: 28rpx;\n  color: #333;\n  line-height: 1.4;\n}\n\n.vs-divider {\n  padding: 0 20rpx;\n}\n\n.vs-icon {\n  font-size: 48rpx;\n  color: #667eea;\n}\n\n/* 分析详情样式 */\n.analysis-details {\n  margin-top: 16rpx;\n}\n\n.analysis-item {\n  margin-bottom: 24rpx;\n  padding: 20rpx;\n  background: #f8f9fa;\n  border-radius: 16rpx;\n}\n\n.analysis-item:last-child {\n  margin-bottom: 0;\n}\n\n.analysis-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.analysis-title {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.analysis-icon {\n  font-size: 28rpx;\n}\n\n.analysis-score {\n"}