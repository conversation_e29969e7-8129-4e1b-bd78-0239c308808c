{"chunk": 3, "numChunks": 12, "fileHash": "a4LdV5V9/sM95i4esh3ydW49XOc5FHg6OAvfDYJ8/so=", "filePath": "pages/name/index.js", "content": "const { NameCalculator } = require('../../utils/name/calculator');\nPage({\n  data: {\n    surname: '',\n    givenName: '',\n    gender: 'male',\n    birthDate: '',\n    loading: false,\n    showResult: false,\n    wugeResult: [],\n    nameScore: 0,\n    scoreDescription: '',\n    wuxingResult: [],\n    nameDetails: [],\n    fortuneResult: [],\n    suggestions: []\n  },\n  onLoad() {\n    // 页面加载时的初始化逻辑\n  },\n  onSurnameInput(e) {\n    this.setData({\n      surname: e.detail.value\n    });\n  },\n  onGivenNameInput(e) {\n    this.setData({\n      givenName: e.detail.value\n    });\n  },\n  onGenderChange(e) {\n    this.setData({\n      gender: e.detail.value\n    });\n  },\n  onBirthDateChange(e) {\n    this.setData({\n      birthDate: e.detail.value\n    });\n  },"}