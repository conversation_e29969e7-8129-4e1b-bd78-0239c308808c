{"chunk": 13, "numChunks": 15, "fileHash": "An9WOmGw20FYVB4+SrCnlnnpOBHN6cohdD4SvY7rXL8=", "filePath": "pages/ziwei/ziwei.js", "content": "// ziwei.js\nPage({\n  generateWealthDetails(stars) {\n    const wealthStars = stars.filter(s => s.position === 5) // 财帛宫\n    const luckyStars = wealthStars.filter(s => s.nature === '吉')\n    const evilStars = wealthStars.filter(s => s.nature === '凶')\n    \n    const details = []\n    \n    // 根据吉星和煞星生成详细预测\n    if (luckyStars.length > 0) {\n      details.push({\n        title: '财运状况',\n        content: `您的财运较好，有${luckyStars.map(s => s.name).join('、')}等吉星相助，适合投资理财。`\n      })\n    }\n    \n    if (evilStars.length > 0) {\n      details.push({\n        title: '理财风险',\n        content: `您的理财需谨慎，有${evilStars.map(s => s.name).join('、')}等煞星影响，避免冒险投资。`\n      })\n    }\n    \n    // 如果没有特定星耀，添加通用预测\n    if (details.length === 0) {\n      details.push({\n        title: '财运发展',\n        content: '您的财运发展较为平稳，建议保持稳健和理性的理财态度。'\n      })\n    }\n    \n    return details\n  },\n  // 生成健康详细预测"}