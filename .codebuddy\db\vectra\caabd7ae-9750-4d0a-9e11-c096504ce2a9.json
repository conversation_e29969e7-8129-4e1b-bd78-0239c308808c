{"chunk": 5, "numChunks": 6, "fileHash": "zijKJpj+VeHL/vTTYDoWlsbYNW+DrvZWabqBQj2IYDU=", "filePath": "pages/marriage/index.js", "content": "const app = getApp()\nPage({\n  bindFemaleDateChange(e) {\n    this.setData({\n      'female.date': e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 女方时间选择器\n  bindFemaleTimeChange(e) {\n    this.setData({\n      'female.time': e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 女方地点选择\n  chooseFemaleLocation() {\n    wx.chooseLocation({\n      success: (res) => {\n        this.setData({\n          'female.location': res.address\n        })\n      }\n    })\n  },\n  // 切换分析类型\n  switchAnalysisType(e) {\n    const { type } = e.currentTarget.dataset\n    const { analysisTypes } = this.data\n    const currentTypeIndex = analysisTypes.findIndex(item => item.id === type)\n    this.setData({\n      currentType: type,\n      currentTypeIndex\n    })\n  },\n  // 检查是否可以提交\n  checkCanSubmit() {\n    const { male, female } = this.data\n    const canSubmit = male.date && male.time && female.date && female.time\n    this.setData({ canSubmit })\n  },\n  // 提交分析"}