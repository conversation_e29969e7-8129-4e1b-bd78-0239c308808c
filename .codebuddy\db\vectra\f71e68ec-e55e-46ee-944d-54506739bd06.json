{"chunk": 4, "numChunks": 76, "fileHash": "lPYWZE8QItgwSyEsMXHtl6y/HYBCI8BdGaV+1l8ICMA=", "filePath": "docs/md/API_INTERFACE_DOCUMENTATION.md", "content": "    \"jin\": \"number\",                // 金\n    \"mu\": \"number\",                 // 木\n    \"shui\": \"number\",               // 水\n    \"huo\": \"number\",                // 火\n    \"tu\": \"number\"                  // 土\n  },\n  \"is_verified\": \"boolean\",         // 是否已验证\n  \"created_at\": \"datetime\",         // 创建时间\n  \"updated_at\": \"datetime\"          // 更新时间\n}\n```\n\n### 出生信息API接口\n\n#### 1. 保存出生信息\n```\nPOST /api/birth-info\nAuthorization: Bearer {token}\nContent-Type: application/json\n\nRequest:\n{\n  \"name\": \"string\",                 // 姓名 (必填)\n  \"gender\": \"string\",               // 性别 (必填: 男/女)\n  \"birth_year\": \"number\",           // 出生年份 (必填)\n  \"birth_month\": \"number\",          // 出生月份 (必填)\n  \"birth_day\": \"number\",            // 出生日期 (必填)\n  \"birth_hour\": \"number\",           // 出生小时 (必填)\n  \"birth_minute\": \"number\",         // 出生分钟 (可选, 默认0)\n  \"zodiac\": \"string\",               // 生肖 (可选)\n  \"lucky_number\": \"number\"          // 幸运数字 (可选)\n}\n\nResponse:\n{\n  \"status\": \"success|error\",\n  \"message\": \"string\",\n  \"data\": {\n    \"birth_info\": {\n      // 完整出生信息，包含计算后的八字、五行等\n    },\n    \"analysis\": {\n      \"bazi_summary\": \"string\",     // 八字简要分析\n"}