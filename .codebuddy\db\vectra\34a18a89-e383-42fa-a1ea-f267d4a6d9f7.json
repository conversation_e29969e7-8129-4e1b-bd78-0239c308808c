{"chunk": 5, "numChunks": 9, "fileHash": "b5v2J+xqqZLB03zKt2qI4RvF0LmgPXZeserUsC/MOx0=", "filePath": "frontend-implementation/utils/wx.js", "content": "// 微信小程序API封装工具\nexport const getUserProfile = (options = {}) => {\n  return new Promise((resolve, reject) => {\n    wx.getUserProfile({\n      desc: '用于完善用户资料',\n      ...options,\n      success: resolve,\n      fail: reject\n    })\n  })\n}\n/**\n * 微信登录\n */\nexport const login = () => {\n  return new Promise((resolve, reject) => {\n    wx.login({\n      success: resolve,\n      fail: reject\n    })\n  })\n}\n/**\n * 检查会话密钥是否有效\n */\nexport const checkSession = () => {\n  return new Promise((resolve, reject) => {\n    wx.checkSession({\n      success: resolve,\n      fail: reject\n    })\n  })\n}\n/**\n * 设置剪贴板内容\n * @param {string} data 剪贴板内容\n */\nexport const setClipboardData = (data) => {\n  return new Promise((resolve, reject) => {\n    wx.setClipboardData({\n      data,\n      success: resolve,\n      fail: reject\n    })\n  })\n}\n/**\n * 获取剪贴板内容\n */\nexport const getClipboardData = () => {\n  return new Promise((resolve, reject) => {\n    wx.getClipboardData({\n      success: resolve,\n      fail: reject\n    })\n  })\n}\n/**\n * 选择图片\n * @param {Object} options 配置选项\n */\nexport const chooseImage = (options = {}) => {\n  const defaultOptions = {\n    count: 1,\n    sizeType: ['original', 'compressed'],\n    sourceType: ['album', 'camera']\n  }\n  \n  return new Promise((resolve, reject) => {\n    wx.chooseImage({\n      ...defaultOptions,\n      ...options,\n      success: resolve,\n      fail: reject\n    })\n  })\n"}