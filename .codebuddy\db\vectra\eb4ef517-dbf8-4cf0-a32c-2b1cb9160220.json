{"chunk": 5, "numChunks": 76, "fileHash": "lPYWZE8QItgwSyEsMXHtl6y/HYBCI8BdGaV+1l8ICMA=", "filePath": "docs/md/API_INTERFACE_DOCUMENTATION.md", "content": "      \"wuxing_summary\": \"string\",   // 五行简要分析\n      \"personality\": \"string\",      // 性格特点\n      \"fortune_trend\": \"string\"     // 运势趋势\n    }\n  }\n}\n```\n\n#### 2. 获取出生信息\n```\nGET /api/birth-info\nAuthorization: Bearer {token}\n\nResponse:\n{\n  \"status\": \"success|error\",\n  \"message\": \"string\",\n  \"data\": {\n    \"birth_info\": {\n      // 完整出生信息\n    }\n  }\n}\n```\n\n#### 3. 更新出生信息\n```\nPUT /api/birth-info\nAuthorization: Bearer {token}\nContent-Type: application/json\n\nRequest:\n{\n  // 与保存接口相同的字段结构\n}\n\nResponse:\n{\n  \"status\": \"success|error\",\n  \"message\": \"string\",\n  \"data\": {\n    \"birth_info\": {\n      // 更新后的完整出生信息\n    }\n  }\n}\n```\n\n## 🤖 AI问答系统\n\n### AI问答数据结构\n\n```javascript\n// 聊天记录表 (chat_messages)\n{\n  \"id\": \"string\",                   // 消息ID\n  \"user_id\": \"string\",              // 用户ID\n  \"session_id\": \"string\",           // 会话ID\n  \"message_type\": \"string\",         // 消息类型 (user/ai/system)\n  \"content\": \"text\",                // 消息内容\n  \"intent\": \"string\",               // 意图识别结果\n  \"context\": \"json\",                // 上下文信息\n  \"attachments\": \"json\",            // 附件信息\n  \"metadata\": {                     // 元数据\n    \"analysis_type\": \"string\",      // 分析类型\n    \"confidence\": \"number\",         // 置信度\n"}