{"chunk": 5, "numChunks": 6, "fileHash": "gjgKY958lwZRjwR5WBqAv14luMAhHXf3C7kpKqAQJe0=", "filePath": "pages/yijing/yijing.js", "content": "// yijing.js\nPage({\n  calculateYijing() {\n    try {\n      const { birthDate, birthTime, gender, question } = this.data\n      \n      if (!birthDate || !birthTime || !gender) {\n        wx.showToast({\n          title: '请填写完整的出生信息',\n          icon: 'none'\n        })\n        return\n      }\n\n      if (!question) {\n        wx.showToast({\n          title: '请输入您的问题',\n          icon: 'none'\n        })\n        return\n      }\n\n      // 显示加载状态\n      this.setData({ loading: true })\n      wx.showLoading({\n        title: '正在占卜...',\n        mask: true\n      })\n\n      // 调用易经计算器\n      const result = yijing.calculate({\n        birthDate,\n        birthTime,\n        question\n      })\n\n      // 隐藏加载状态\n      this.setData({ \n        result: result,\n        loading: false\n      })\n      wx.hideLoading()\n\n      // 保存结果到历史记录\n      this.saveToHistory(result)\n      \n      // 滚动到结果区域\n      wx.pageScrollTo({\n        scrollTop: 1000,\n        duration: 300\n      })\n    } catch (error) {\n      console.error('易经计算出错：', error)\n      this.setData({ loading: false })\n      wx.hideLoading()\n      wx.showToast({\n        title: '计算出错，请重试',\n        icon: 'none'\n      })\n    }\n  },\n  // 保存到历史记录"}