{"chunk": 1, "numChunks": 6, "fileHash": "dNwun7ERRMYazRQ1UOKQO3ggF1ZH6El+O2/hG+Rh3/o=", "filePath": "pages/chat-list/chat-list.wxml", "content": "          bindtap=\"openChat\"\n          data-id=\"{{item.id}}\"\n          bindlongpress=\"showChatOptions\"\n          data-chat=\"{{item}}\"\n        >\n          <view class=\"chat-avatar\">\n            <image src=\"{{item.avatar || '/assets/icons/ai-avatar.png'}}\" mode=\"aspectFill\" />\n            <view class=\"chat-status {{item.status}}\"></view>\n          </view>\n          <view class=\"chat-content\">\n            <view class=\"chat-header\">\n              <text class=\"chat-title\">{{item.title}}</text>\n              <text class=\"chat-time\">{{item.lastMessageTime}}</text>\n            </view>\n            <view class=\"chat-preview\">\n              <text class=\"last-message\">{{item.lastMessage}}</text>\n              <view class=\"chat-badges\">\n                <view class=\"unread-badge\" wx:if=\"{{item.unreadCount > 0}}\">{{item.unreadCount}}</view>\n                <view class=\"chat-type\">{{item.chatType}}</view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 普通对话 -->\n    <view class=\"normal-section\">\n      <view class=\"section-title\" wx:if=\"{{pinnedChats.length > 0}}\">💬 全部对话</view>\n      \n      <!-- 筛选标签 -->\n      <view class=\"filter-tabs\">\n        <view \n          class=\"filter-tab {{activeFilter === item.value ? 'active' : ''}}\"\n          wx:for=\"{{filterTabs}}\"\n          wx:key=\"value\"\n          bindtap=\"switchFilter\"\n          data-filter=\"{{item.value}}\"\n        >\n          {{item.label}}\n        </view>\n      </view>\n\n      <!-- 聊天列表 -->\n      <view class=\"chat-list\">\n        <view \n          class=\"chat-item {{item.isPinned ? 'pinned' : ''}}\"\n"}