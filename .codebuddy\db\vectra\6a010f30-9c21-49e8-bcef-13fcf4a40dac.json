{"chunk": 5, "numChunks": 11, "fileHash": "6cvegKFbV+qpODqHzHmmaj3L2MYUXg3Sv6AOEWns8A8=", "filePath": "frontend-implementation/mixins/basePage.js", "content": "// 页面基础混入\nexport const basePageMixin = {\n  updatePageState() {\n    const userState = store.getState('user')\n    const appState = store.getState('app')\n    \n    this.setData({\n      isLogin: userState.isLogin,\n      userInfo: userState.userInfo,\n      networkStatus: appState.networkStatus\n    })\n  },\n  /**\n   * 订阅状态变化\n   */\n  subscribeStoreChanges() {\n    // 订阅用户状态变化\n    this.unsubscribeUser = store.subscribe('user', (userState) => {\n      this.setData({\n        isLogin: userState.isLogin,\n        userInfo: userState.userInfo\n      })\n    })\n    \n    // 订阅应用状态变化\n    this.unsubscribeApp = store.subscribe('app', (appState) => {\n      this.setData({\n        loading: appState.loading,\n        networkStatus: appState.networkStatus\n      })\n    })\n  },\n  /**\n   * 取消状态订阅\n   */\n  unsubscribeStoreChanges() {\n    if (this.unsubscribeUser) {\n      this.unsubscribeUser()\n    }\n    if (this.unsubscribeApp) {\n      this.unsubscribeApp()\n    }\n  },\n  /**\n   * 检查登录状态\n   */\n  checkLoginStatus() {\n    const token = getStorageSync('token')\n    const userInfo = getStorageSync('userInfo')\n    \n    if (!token || !userInfo) {\n      // 如果页面需要登录\n      if (this.requireLogin) {\n        this.redirectToLogin()\n      }\n    }\n  },\n  /**\n   * 跳转到登录页面\n   */\n  redirectToLogin() {\n    showToast({\n      title: '请先登录',\n      icon: 'none'\n    })\n    \n    setTimeout(() => {\n      wx.reLaunch({\n        url: '/pages/login/index'\n      })\n    }, 1500)\n  },"}