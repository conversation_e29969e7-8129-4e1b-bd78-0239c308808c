{"chunk": 4, "numChunks": 6, "fileHash": "IwGBwJ25Blubhn4hCt83o/e9MDZEM6l3SvRIm1A0vrE=", "filePath": "pages/fengshui/fengshui.wxml", "content": "        <text class=\"tip-item\">• 标记*的为必填项，请准确填写</text>\n        <text class=\"tip-item\">• 房屋朝向请使用指南针确认</text>\n        <text class=\"tip-item\">• 环境信息越详细，分析越准确</text>\n      </view>\n    </view>\n\n    <!-- 提交按钮 -->\n    <view class=\"submit-section\">\n      <button\n        class=\"submit-btn {{canSubmit ? 'active' : 'disabled'}}\"\n        bindtap=\"analyzeFengshui\"\n        disabled=\"{{!canSubmit}}\"\n      >\n        <text class=\"btn-text\">开始分析</text>\n        <text class=\"btn-icon\">🔍</text>\n      </button>\n    </view>\n  </view>\n\n  <!-- 分析结果区域 -->\n  <view class=\"result-container\" wx:if=\"{{analysisResult}}\">\n    <!-- 总体评分 -->\n    <view class=\"info-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">风水评分</text>\n        <text class=\"card-icon\">💯</text>\n      </view>\n      <view class=\"score-section\">\n        <view class=\"score-ring\">\n          <view class=\"score-value\">{{analysisResult.totalScore}}分</view>\n          <view class=\"score-desc\">{{analysisResult.overall}}</view>\n        </view>\n        <view class=\"score-detail\">{{analysisResult.description}}</view>\n      </view>\n    </view>\n\n    <!-- 详细分析 -->\n    <view class=\"info-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">详细分析</text>\n        <text class=\"card-icon\">📊</text>\n      </view>\n"}