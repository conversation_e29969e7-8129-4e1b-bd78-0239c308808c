{"chunk": 2, "numChunks": 19, "fileHash": "PEPeuJm140RAD7VBd85WlnsfcWUdoorqVSQj8ISByl0=", "filePath": "pages/ai-chat/ai-chat.wxss", "content": "  right: -20rpx;\n  bottom: -20rpx;\n  background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);\n  border-radius: 50%;\n  animation: glow-pulse 3s ease-in-out infinite;\n}\n\n.avatar-icon {\n  font-size: 120rpx;\n  display: block;\n  position: relative;\n  z-index: 1;\n}\n\n.welcome-avatar-icon {\n  width: 120rpx;\n  height: 120rpx;\n  display: block;\n  position: relative;\n  z-index: 1;\n}\n\n.welcome-title {\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #1f2937;\n  margin-bottom: 12rpx;\n}\n\n.welcome-subtitle {\n  font-size: 28rpx;\n  color: #6b7280;\n  line-height: 1.5;\n}\n\n/* 快捷功能卡片 */\n.quick-actions-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 20rpx;\n  margin: 32rpx 0;\n}\n\n.quick-card {\n  background: white;\n  border-radius: 20rpx;\n  padding: 32rpx 24rpx;\n  text-align: center;\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n  border: 1rpx solid rgba(102, 126, 234, 0.1);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.quick-card:active {\n  transform: translateY(-4rpx);\n"}