{"chunk": 0, "numChunks": 4, "fileHash": "euo6OT+uMcA1jHrVDL9HcA2afhD9hscN1rx91h/6LSA=", "filePath": "pages/birth-info/birth-info.wxml", "content": "<!--pages/birth-info/birth-info.wxml-->\n<view class=\"container\">\n  <!-- 顶部标题区域 -->\n  <view class=\"header\">\n    <view class=\"header-content\">\n      <view class=\"title\">完善出生信息</view>\n      <view class=\"subtitle\">为您提供更精准的命理分析</view>\n    </view>\n    <view class=\"header-decoration\">\n      <text class=\"icon\">🔮</text>\n    </view>\n  </view>\n\n  <!-- 表单区域 -->\n  <view class=\"form-container\">\n    <!-- 基本信息卡片 -->\n    <view class=\"info-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">基本信息</text>\n        <text class=\"card-icon\">👤</text>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">姓名</text>\n          <text class=\"required\">*</text>\n        </view>\n        <view class=\"input-wrapper\">\n          <input\n            class=\"input\"\n            placeholder=\"请输入您的姓名\"\n            bindinput=\"onNameInput\"\n            value=\"{{name}}\"\n            maxlength=\"20\"\n            placeholder-class=\"input-placeholder\"\n          />\n          <text class=\"input-icon\">✏️</text>\n        </view>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">性别</text>\n          <text class=\"required\">*</text>\n        </view>\n        <view class=\"gender-selector\">\n          <view\n            class=\"gender-option {{gender === '男' ? 'active' : ''}}\"\n            bindtap=\"selectGender\"\n            data-gender=\"男\"\n          >\n"}