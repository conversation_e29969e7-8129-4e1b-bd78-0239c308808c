{"chunk": 4, "numChunks": 14, "fileHash": "3g+f4UKfyPQOeFOGstuRr+TvNVx6NqSI5ZfRnXdVunU=", "filePath": "pages/profile/profile.js", "content": "// pages/profile/profile.js\nPage({\n  onLoad(options) {\n    if (wx.getUserProfile) {\n      this.setData({\n        canIUseGetUserProfile: true\n      })\n    }\n\n    // 初始化全局状态监听\n    this.initStateListeners()\n\n    // 加载初始数据\n    this.loadInitialData()\n  },\n  /**\n   * 生命周期函数--监听页面初次渲染完成\n   */\n  onReady() {\n\n  },\n  /**\n   * 生命周期函数--监听页面显示\n   */\n  onShow() {\n    try {\n      if (typeof this.getTabBar === 'function' && this.getTabBar()) {\n        // 调用自定义 tabBar 的 setSelected 方法\n        this.getTabBar().setSelected()\n      }\n    } catch (error) {\n      console.error('设置 TabBar 选中状态失败:', error)\n    }\n\n    // 刷新全局状态数据\n    this.refreshGlobalState()\n    this.loadUserData()\n  },\n  /**\n   * 初始化状态监听器\n   */\n  initStateListeners() {\n    // 监听用户信息变化\n    this.userInfoListener = globalState.addListener('userInfo', (newUserInfo) => {\n      this.setData({\n        userInfo: newUserInfo,\n        hasUserInfo: !!newUserInfo\n      })\n    })\n\n    // 监听积分变化\n    this.pointsListener = globalState.addListener('points', (newPoints) => {\n      this.setData({\n        points: newPoints,\n        level: this.calculateLevel(newPoints)\n      })\n    })\n\n    // 监听出生信息变化\n    this.birthInfoListener = globalState.addListener('birthInfo', (newBirthInfo) => {\n      this.setData({ birthInfo: newBirthInfo })\n    })\n\n    // 监听签到状态变化\n    this.signInListener = globalState.addListener('signInStatus', (newStatus) => {\n      this.setData({ signInStatus: newStatus })\n    })\n"}