{"chunk": 1, "numChunks": 6, "fileHash": "YXf85xx1jxmB9as0Va5OH90PaK79Xnt82sfEgjKj2bE=", "filePath": "pages/post-detail/post-detail.wxss", "content": "  margin: -40rpx 20rpx 20rpx;\n  border-radius: 25rpx;\n  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.1);\n  position: relative;\n  z-index: 1;\n}\n\n.author-info {\n  display: flex;\n  align-items: center;\n}\n\n.author-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  margin-right: 20rpx;\n  border: 4rpx solid #f5f0ff;\n}\n\n.author-details {\n  display: flex;\n  flex-direction: column;\n}\n\n.author-name {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.author-desc {\n  font-size: 26rpx;\n  color: #999;\n}\n\n.article-stats {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  gap: 8rpx;\n}\n\n.stat-item {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 文章内容 */\n.article-content {\n  padding: 0 30rpx;\n}\n\n.article-summary {\n  padding: 25rpx;\n  background: linear-gradient(135deg, #f8f5ff 0%, #f0ebf8 100%);\n  border-radius: 20rpx;\n  margin-bottom: 30rpx;\n  font-size: 30rpx;\n  color: #666;\n  line-height: 1.8;\n  border-left: 6rpx solid #9575cd;\n}\n\n.article-body {\n  font-size: 32rpx;\n  color: #333;\n  line-height: 2;\n  margin-bottom: 40rpx;\n"}