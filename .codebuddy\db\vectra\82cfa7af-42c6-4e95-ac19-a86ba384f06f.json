{"chunk": 12, "numChunks": 15, "fileHash": "An9WOmGw20FYVB4+SrCnlnnpOBHN6cohdD4SvY7rXL8=", "filePath": "pages/ziwei/ziwei.js", "content": "// ziwei.js\nPage({\n  generateMarriageDetails(shen<PERSON>ong, stars) {\n    const marriageStars = stars.filter(s => s.position === shenGong)\n    const luckyStars = marriageStars.filter(s => s.nature === '吉')\n    const evilStars = marriageStars.filter(s => s.nature === '凶')\n    \n    const details = []\n    \n    // 根据吉星和煞星生成详细预测\n    if (luckyStars.length > 0) {\n      details.push({\n        title: '婚姻质量',\n        content: `您的婚姻质量较好，有${luckyStars.map(s => s.name).join('、')}等吉星相助，婚姻生活较为和谐。`\n      })\n    }\n    \n    if (evilStars.length > 0) {\n      details.push({\n        title: '婚姻挑战',\n        content: `您的婚姻可能面临一些挑战，有${evilStars.map(s => s.name).join('、')}等煞星影响，需要多加沟通和理解。`\n      })\n    }\n    \n    // 如果没有特定星耀，添加通用预测\n    if (details.length === 0) {\n      details.push({\n        title: '婚姻发展',\n        content: '您的婚姻发展较为平稳，建议在感情中保持真诚和包容的态度。'\n      })\n    }\n    \n    return details\n  },\n  // 生成财运详细预测"}