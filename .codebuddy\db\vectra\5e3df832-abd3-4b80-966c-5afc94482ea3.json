{"chunk": 10, "numChunks": 18, "fileHash": "/DUygIsVyFRM4gmsdi8uiMJN8RFlyElKDKTgRRrPFCI=", "filePath": "utils/hehun/calculator.js", "content": "// 五行生克关系\nclass HehunCalculator {\n  calculateRiZhuHe(male<PERSON>an, male<PERSON><PERSON>, female<PERSON>an, female<PERSON>hi) {\n    let score = 0\n    const maleWuxing = TIAN_GAN_WU_XING[maleGan]\n    const femaleWuxing = TIAN_GAN_WU_XING[femaleGan]\n\n    // 日柱五行相生\n    if (WU_XING_RELATIONS[maleWuxing].generates === femaleWuxing) {\n      score += 30\n    }\n\n    // 日支相合\n    if (this.isZhiHe(male<PERSON>hi, female<PERSON>hi)) {\n      score += 20\n    }\n\n    return score\n  }\n  getMainWuxing(bazi) {\n    // 获取日柱天干的五行属性作为主五行\n    return TIAN_GAN_WU_XING[bazi.day.gan]\n  }\n  isHelpful(wuxing1, wuxing2) {\n    return WU_XING_RELATIONS[wuxing1].generates === wuxing2 ||\n           WU_XING_RELATIONS[wuxing2].generates === wuxing1\n  }\n  isConflict(wuxing1, wuxing2) {\n    return WU_XING_RELATIONS[wuxing1].restricts === wuxing2 ||\n           WU_XING_RELATIONS[wuxing2].restricts === wuxing1\n  }"}