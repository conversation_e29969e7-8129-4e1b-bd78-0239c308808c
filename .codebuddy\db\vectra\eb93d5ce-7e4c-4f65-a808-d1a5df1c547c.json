{"chunk": 3, "numChunks": 5, "fileHash": "MvHilPs2FcNTeJtL9CKWbmgKe5i8pqMev8Y5RetE4QY=", "filePath": "frontend-implementation/pages/login/index.js", "content": "// 登录页面\nPage({\n  data: {\n    userInfo: null,\n    hasUserInfo: false,\n    canIUseGetUserProfile: false,\n    loading: false\n  },\n  onLoad() {\n    // 检查是否支持getUserProfile\n    if (wx.getUserProfile) {\n      this.setData({\n        canIUseGetUserProfile: true\n      })\n    }\n\n    // 检查是否已登录\n    this.checkLoginStatus()\n  },\n  /**\n   * 检查登录状态\n   */\n  checkLoginStatus() {\n    const token = getStorageSync('token')\n    const userInfo = getStorageSync('userInfo')\n    \n    if (token && userInfo) {\n      // 已登录，跳转到首页\n      wx.switchTab({\n        url: '/pages/index/index'\n      })\n    }\n  },\n  /**\n   * 微信登录\n   */"}