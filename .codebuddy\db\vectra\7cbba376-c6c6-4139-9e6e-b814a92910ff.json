{"chunk": 2, "numChunks": 6, "fileHash": "m/bcrIpornnqwv0iP6r9/jZF/qprhOPA5RFcfEHrukg=", "filePath": "pages/profile/profile.wxss", "content": "  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20rpx;\n}\n\n.stat-value {\n  font-size: 40rpx;\n  font-weight: bold;\n  color: #9575cd;\n  margin-bottom: 8rpx;\n}\n\n.stat-label {\n  font-size: 26rpx;\n  color: #666;\n}\n\n.stat-divider {\n  width: 1rpx;\n  height: 60rpx;\n  background: #eee;\n}\n\n/* 签到卡片 */\n.sign-card {\n  margin: 120rpx 30rpx 30rpx;\n  background: linear-gradient(135deg, #fff 0%, #f8f5ff 100%);\n  border-radius: 25rpx;\n  padding: 30rpx;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.1);\n  transition: all 0.3s ease;\n}\n\n.sign-card:active {\n  transform: translateY(2rpx);\n  box-shadow: 0 4rpx 12rpx rgba(149, 117, 205, 0.15);\n}\n\n.sign-icon {\n  font-size: 60rpx;\n  margin-right: 25rpx;\n}\n\n.sign-content {\n  flex: 1;\n}\n\n.sign-title {\n  display: block;\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.sign-desc {\n  display: block;\n  font-size: 26rpx;\n  color: #999;\n}\n\n.sign-action {\n  padding: 16rpx 32rpx;\n"}