{"chunk": 4, "numChunks": 6, "fileHash": "u49Rqom2MlWUCSY5d2kW1CfpdWY95ku7je8yGKj9+oc=", "filePath": "pages/name-test/name-test.wxss", "content": "  height: 8rpx;\n  background: #e0e0e0;\n  border-radius: 4rpx;\n  overflow: hidden;\n}\n\n.score-fill {\n  height: 100%;\n  background: linear-gradient(90deg, #52c41a 0%, #faad14 50%, #ff4d4f 100%);\n  transition: width 0.3s ease;\n}\n\n/* 改名建议 */\n.suggestion-section {\n  padding: 40rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.suggestion-list {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.suggestion-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20rpx;\n  background: #fff7e6;\n  border-radius: 12rpx;\n  border-left: 6rpx solid #faad14;\n}\n\n.suggestion-name {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.suggestion-score {\n  font-size: 28rpx;\n  color: #fa8c16;\n  font-weight: bold;\n}\n\n.suggestion-reason {\n  font-size: 24rpx;\n  color: #666;\n  margin-top: 8rpx;\n}\n\n/* 操作按钮 */\n.action-buttons {\n  display: flex;\n  gap: 20rpx;\n  padding: 40rpx;\n}\n\n.action-buttons button {\n  flex: 1;\n  height: 80rpx;\n  border-radius: 40rpx;\n  font-size: 28rpx;\n  border: none;\n}\n\n.save-btn {\n  background: #52c41a;\n  color: white;\n}\n\n.share-btn {\n  background: #1890ff;\n"}