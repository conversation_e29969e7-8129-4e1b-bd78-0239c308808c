{"chunk": 37, "numChunks": 38, "fileHash": "DpyinnhZJO6jrs0/VY0BXwrNjORlEQ1BeRywBJybhxQ=", "filePath": "pages/ai-chat/ai-chat.js", "content": "const app = getApp()\nPage({\n  async retryMessage(content) {\n    this.setData({ isTyping: true })\n    \n    try {\n      const response = await apiService.intelligentQuery(\n        content,\n        this.data.userInfo\n      )\n      \n      this.setData({ isTyping: false })\n      this.handleApiResponse(response, content)\n    } catch (error) {\n      this.setData({ isTyping: false })\n      this.handleApiError(error, content)\n    }\n  },\n  /**\n   * 打字机效果函数\n   * @param {String} text - 要显示的文本\n   * @param {Number} messageIndex - 消息在数组中的索引\n   * @param {Number} speed - 打字速度（毫秒）\n   */\n  typewriterEffect(text, messageIndex, speed = 50) {\n    return new Promise((resolve) => {\n      let charIndex = 0;\n      const messageKey = `messages[${messageIndex}].content`;\n      \n      // 先设置空内容\n      this.setData({\n        [messageKey]: '',\n        [`messages[${messageIndex}].isTyping`]: true\n      });\n      \n      const typeInterval = setInterval(() => {\n        if (charIndex < text.length) {\n          // 逐字添加\n          const currentText = text.substring(0, charIndex + 1);\n          this.setData({\n            [messageKey]: currentText\n          });\n          charIndex++;\n        } else {\n          // 打字完成\n          clearInterval(typeInterval);\n          this.setData({\n            [`messages[${messageIndex}].isTyping`]: false\n          });\n          resolve();\n        }\n      }, speed);\n    });\n  },\n  /**\n   * 处理AI响应的函数\n   */"}