{"chunk": 4, "numChunks": 5, "fileHash": "phyefDDaTxSx6AIRrru0mvvxsrADGzW4S4ymFFvEUoY=", "filePath": "utils/yijing/hexagrams.js", "content": "// 八卦基础数据\n// 获取所有卦象\nconst getAllHexagrams = () => {\n  return hexagramsData;\n};\n// 根据ID获取特定卦象\nconst getHexagramById = (id) => {\n  return hexagramsData.find(h => h.id === id);\n};\n// 根据名称获取卦象\nconst getHexagramByName = (name) => {\n  return hexagramsData.find(h => h.name === name);\n};\n// 获取卦象的详细解释\nconst getHexagramInterpretation = (hexagram) => {\n  if (!hexagram) return null;\n  return {\n    name: hexagram.name,\n    description: hexagram.description,\n    judgment: hexagram.judgment,\n    image: hexagram.image,\n    interpretation: hexagram.interpretation,\n    lines: hexagram.lines\n  };\n};"}