{"chunk": 0, "numChunks": 3, "fileHash": "iCmaZ6jMvTDmQ81NioPpkFyGvlIKjeIU1BQv/3IrIRc=", "filePath": "pages/customer-service/index.wxml", "content": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<view class=\"container\">\n  <!-- 客服信息 -->\n  <view class=\"service-header\">\n    <image class=\"service-avatar\" src=\"{{serviceInfo.avatar}}\" mode=\"aspectFill\"/>\n    <view class=\"service-info\">\n      <text class=\"service-name\">{{serviceInfo.name}}</text>\n      <text class=\"service-status\">在线</text>\n    </view>\n  </view>\n\n  <!-- 消息列表 -->\n  <scroll-view \n    class=\"message-list\" \n    scroll-y \n    id=\"message-list\"\n    scroll-into-view=\"msg-{{messages.length - 1}}\"\n  >\n    <!-- 常见问题 -->\n    <view class=\"faq-section\">\n      <text class=\"faq-title\">常见问题</text>\n      <view class=\"faq-list\">\n        <view \n          class=\"faq-item\"\n          wx:for=\"{{faqList}}\"\n          wx:key=\"*this\"\n          bindtap=\"handleSelectFaq\"\n          data-question=\"{{item}}\"\n        >\n          {{item}}\n        </view>\n      </view>\n    </view>\n\n    <!-- 消息内容 -->\n    <view \n      class=\"message-item {{item.type}}\" \n      wx:for=\"{{messages}}\" \n      wx:key=\"time\"\n      id=\"msg-{{index}}\"\n    >\n      <image \n        class=\"avatar\" \n        src=\"{{item.type === 'service' ? serviceInfo.avatar : userInfo.avatarUrl}}\" \n        mode=\"aspectFill\"\n      />\n      <view class=\"message-content\">\n        <text class=\"name\">{{item.type === 'service' ? serviceInfo.name : userInfo.nickName}}</text>\n        <!-- 文本消息 -->\n        <view class=\"content\" wx:if=\"{{item.contentType === 'text' || !item.contentType}}\">\n          {{item.content}}\n        </view>\n        <!-- 图片消息 -->\n"}