{"chunk": 0, "numChunks": 9, "fileHash": "b5v2J+xqqZLB03zKt2qI4RvF0LmgPXZeserUsC/MOx0=", "filePath": "frontend-implementation/utils/wx.js", "content": "// 微信小程序API封装工具\n/**\n * 存储数据到本地缓存\n * @param {string} key 键名\n * @param {any} data 数据\n */\nexport const setStorageSync = (key, data) => {\n  try {\n    wx.setStorageSync(key, data)\n  } catch (error) {\n    console.error('存储数据失败:', error)\n  }\n}\n/**\n * 从本地缓存获取数据\n * @param {string} key 键名\n * @param {any} defaultValue 默认值\n */\nexport const getStorageSync = (key, defaultValue = null) => {\n  try {\n    return wx.getStorageSync(key) || defaultValue\n  } catch (error) {\n    console.error('获取缓存数据失败:', error)\n    return defaultValue\n  }\n}\n/**\n * 删除本地缓存数据\n * @param {string} key 键名\n */\nexport const removeStorageSync = (key) => {\n  try {\n    wx.removeStorageSync(key)\n  } catch (error) {\n    console.error('删除缓存数据失败:', error)\n  }\n}\n/**\n * 清空本地缓存\n */\nexport const clearStorageSync = () => {\n  try {\n    wx.clearStorageSync()\n  } catch (error) {\n    console.error('清空缓存失败:', error)\n  }\n}\n/**\n * 显示消息提示框\n * @param {Object} options 配置选项\n */\nexport const showToast = (options) => {\n  const defaultOptions = {\n    title: '操作成功',\n    icon: 'success',\n    duration: 2000,\n    mask: false\n  }\n  \n  wx.showToast({\n    ...defaultOptions,\n    ...options\n  })\n}\n/**\n * 显示加载提示\n * @param {Object} options 配置选项\n */"}