{"chunk": 0, "numChunks": 4, "fileHash": "AzClV7baUsatVdHoLC/V2zxViVlpnMYViBcaI7+/JbU=", "filePath": "pages/customer-service/index.wxss", "content": "/* pages/customer-service/index.wxss */\n.container {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 客服信息 */\n.service-header {\n  background-color: #fff;\n  padding: 30rpx;\n  display: flex;\n  align-items: center;\n  border-bottom: 2rpx solid #f0f0f0;\n}\n\n.service-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 40rpx;\n  margin-right: 20rpx;\n}\n\n.service-info {\n  flex: 1;\n}\n\n.service-name {\n  font-size: 32rpx;\n  color: #333;\n  font-weight: 500;\n  display: block;\n}\n\n.service-status {\n  font-size: 24rpx;\n  color: #07c160;\n  display: block;\n  margin-top: 4rpx;\n}\n\n/* 消息列表 */\n.message-list {\n  flex: 1;\n  padding: 30rpx;\n}\n\n/* 常见问题 */\n.faq-section {\n  margin-bottom: 40rpx;\n}\n\n.faq-title {\n  font-size: 28rpx;\n  color: #999;\n  margin-bottom: 20rpx;\n  display: block;\n}\n\n.faq-list {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 20rpx;\n}\n\n.faq-item {\n  background-color: #fff;\n  padding: 16rpx 30rpx;\n  border-radius: 30rpx;\n  font-size: 26rpx;\n  color: #333;\n}\n\n/* 消息项 */\n.message-item {\n  display: flex;\n  margin-bottom: 30rpx;\n}\n\n.message-item.service {\n  flex-direction: row;\n}\n\n.message-item.user {\n"}