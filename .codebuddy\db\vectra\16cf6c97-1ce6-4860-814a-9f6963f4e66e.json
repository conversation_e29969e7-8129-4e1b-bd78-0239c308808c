{"chunk": 0, "numChunks": 3, "fileHash": "QmNcpjJB1sye5Kvfemz6/OCLuYiC8a2AxSrxnx4MzRM=", "filePath": "pages/wuxing/wuxing.wxml", "content": "<!--wuxing.wxml-->\n<view class=\"container\">\n  <!-- 使用出生信息组件 -->\n  <birth-info bind:save=\"onBirthInfoSave\"></birth-info>\n  \n  <!-- 结果展示区域 -->\n  <view class=\"result-section\" wx:if=\"{{wuxingResult}}\">\n    <view class=\"section-title\">五行分析结果</view>\n    \n    <!-- 五行分布图 -->\n    <view class=\"wuxing-chart\">\n      <view class=\"chart-title\">五行分布</view>\n      <view class=\"distribution-grid\">\n        <view class=\"distribution-item\" wx:for=\"{{wuxingResult.distribution}}\" wx:key=\"element\">\n          <view class=\"element-name\">{{item.element}}</view>\n          <view class=\"element-strength\">{{item.strength}}%</view>\n          <view class=\"strength-bar\">\n            <view class=\"bar-fill\" style=\"width: {{item.strength}}%\"></view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 五行分析 -->\n    <view class=\"analysis-section\">\n      <view class=\"analysis-item\">\n        <view class=\"item-title\">五行属性</view>\n        <view class=\"item-content\">{{wuxingResult.analysis.attribute}}</view>\n      </view>\n\n      <view class=\"analysis-item\">\n        <view class=\"item-title\">性格特点</view>\n        <view class=\"item-content\">{{wuxingResult.analysis.personality}}</view>\n      </view>\n\n      <view class=\"analysis-item\">\n        <view class=\"item-title\">事业方向</view>\n        <view class=\"item-content\">{{wuxingResult.analysis.career}}</view>\n      </view>\n\n      <view class=\"analysis-item\">\n        <view class=\"item-title\">健康建议</view>\n"}