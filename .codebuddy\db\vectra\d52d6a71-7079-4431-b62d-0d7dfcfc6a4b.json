{"chunk": 3, "numChunks": 55, "fileHash": "fCZ6nFoNZXQqhvADUzMwGWD6jOU+ajj61B8BD3CwDgs=", "filePath": "pages/name-test/name-test.js", "content": "// 姓名测试页面逻辑\nPage({\n  data: {\n    surname: '', // 姓氏\n    givenName: '', // 名字\n    gender: 'male', // 性别\n    isLoading: false,\n    testResult: null,\n    canTest: false\n  },\n  onLoad() {\n    // 设置页面标题\n    wx.setNavigationBarTitle({\n      title: '姓名测试'\n    });\n  },\n  // 姓氏输入\n  onSurnameInput(e) {\n    this.setData({\n      surname: e.detail.value\n    });\n    this.checkCanTest();\n  },\n  // 名字输入\n  onGivenNameInput(e) {\n    this.setData({\n      givenName: e.detail.value\n    });\n    this.checkCanTest();\n  },\n  // 性别选择\n  onGenderChange(e) {\n    this.setData({\n      gender: e.detail.value\n    });\n  },\n  // 检查是否可以开始测试\n  checkCanTest() {\n    const { surname, givenName } = this.data;\n    this.setData({\n      canTest: surname.trim() && givenName.trim()\n    });\n  },\n  // 开始姓名测试"}