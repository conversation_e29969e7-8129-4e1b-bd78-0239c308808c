{"chunk": 0, "numChunks": 3, "fileHash": "15fIrrZZavpLMg/CeD22sxtSKr3cmD/mbJHXpMwsVQQ=", "filePath": "pages/hehun/index.wxml", "content": "<view class=\"container\">\n  <view class=\"header\">\n    <text class=\"title\">合婚测算</text>\n    <text class=\"subtitle\">缘分天注定，姻缘有迹可循</text>\n  </view>\n\n  <view class=\"form-section\">\n    <view class=\"person-info male\">\n      <text class=\"section-title\">男方信息</text>\n      <view class=\"form-item\">\n        <text class=\"label\">出生日期</text>\n        <picker mode=\"date\" value=\"{{maleBirthDate}}\" bindchange=\"onMaleBirthDateChange\">\n          <view class=\"picker\">{{maleBirthDate || '请选择出生日期'}}</view>\n        </picker>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"label\">出生时间</text>\n        <picker mode=\"time\" value=\"{{maleBirthTime}}\" bindchange=\"onMaleBirthTimeChange\">\n          <view class=\"picker\">{{maleBirthTime || '请选择出生时间'}}</view>\n        </picker>\n      </view>\n    </view>\n\n    <view class=\"person-info female\">\n      <text class=\"section-title\">女方信息</text>\n      <view class=\"form-item\">\n        <text class=\"label\">出生日期</text>\n        <picker mode=\"date\" value=\"{{femaleBirthDate}}\" bindchange=\"onFemaleBirthDateChange\">\n          <view class=\"picker\">{{femaleBirthDate || '请选择出生日期'}}</view>\n        </picker>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"label\">出生时间</text>\n        <picker mode=\"time\" value=\"{{femaleBirthTime}}\" bindchange=\"onFemaleBirthTimeChange\">\n          <view class=\"picker\">{{femaleBirthTime || '请选择出生时间'}}</view>\n"}