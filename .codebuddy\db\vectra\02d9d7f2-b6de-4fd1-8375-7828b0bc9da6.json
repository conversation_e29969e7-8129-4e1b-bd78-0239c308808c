{"chunk": 16, "numChunks": 19, "fileHash": "PEPeuJm140RAD7VBd85WlnsfcWUdoorqVSQj8ISByl0=", "filePath": "pages/ai-chat/ai-chat.wxss", "content": "  white-space: pre-wrap;\n}\n\n.message-user .message-text {\n  color: white;\n}\n\n/* 打字机效果的文本样式 */\n.message-text.typing-effect {\n  position: relative;\n}\n\n.message-text.typing-effect::after {\n  content: '.';\n  position: absolute;\n  color: #7928CA;\n  animation: dots-typing 1.5s infinite;\n  font-weight: bold;\n}\n\n@keyframes dots-typing {\n  0% {\n    content: '.';\n  }\n  33% {\n    content: '..';\n  }\n  66% {\n    content: '...';\n  }\n  100% {\n    content: '.';\n  }\n}\n\n.message-actions {\n  margin-top: 24rpx;\n  padding-top: 24rpx;\n  border-top: 1rpx solid var(--border-color);\n}\n\n.action-btn {\n  background: var(--gradient-primary);\n  color: white;\n  border: none;\n  border-radius: 18rpx;\n  padding: 18rpx 36rpx;\n  font-size: 26rpx;\n  line-height: 1;\n  box-shadow: 0 6rpx 16rpx rgba(121, 40, 202, 0.2);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.action-btn:active {\n  transform: translateY(2rpx) scale(0.98);\n  box-shadow: 0 3rpx 8rpx rgba(121, 40, 202, 0.25);\n}\n\n/* 输入中状态 */\n.typing-indicator {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 40rpx;\n"}