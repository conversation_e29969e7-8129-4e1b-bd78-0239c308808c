{"chunk": 5, "numChunks": 12, "fileHash": "Le/FlEMr7xpFlnsEezDNSUltFPN5XSRHUvE0NpcG53I=", "filePath": "frontend-implementation/pages/ai-chat/index.js", "content": "// AI聊天页面\nPage({\n  async loadChatHistory() {\n    try {\n      showLoading({ title: '加载中...' })\n      \n      const result = await getChatHistory({\n        session_id: this.data.currentSessionId,\n        limit: 50\n      })\n      \n      if (result.status === 'success') {\n        this.setData({\n          messages: result.data.messages || [],\n          showQuickActions: false\n        })\n        this.scrollToBottom()\n      }\n    } catch (error) {\n      console.error('加载聊天历史失败:', error)\n      showToast({\n        title: '加载失败',\n        icon: 'none'\n      })\n    } finally {\n      hideLoading()\n    }\n  },\n  /**\n   * 输入框内容变化\n   */\n  onInputChange(e) {\n    this.setData({\n      inputText: e.detail.value\n    })\n  },\n  /**\n   * 键盘高度变化\n   */\n  onKeyboardHeightChange(e) {\n    this.setData({\n      keyboardHeight: e.detail.height\n    })\n    // 键盘弹起时滚动到底部\n    if (e.detail.height > 0) {\n      setTimeout(() => {\n        this.scrollToBottom()\n      }, 100)\n    }\n  },\n  /**\n   * 发送消息\n   */"}