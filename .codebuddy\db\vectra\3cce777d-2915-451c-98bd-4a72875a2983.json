{"chunk": 47, "numChunks": 55, "fileHash": "fCZ6nFoNZXQqhvADUzMwGWD6jOU+ajj61B8BD3CwDgs=", "filePath": "pages/name-test/name-test.js", "content": "// 姓名测试页面逻辑\nPage({\n  getNumberLevel(number) {\n    const luckyNumbers = [1, 3, 5, 6, 7, 8, 11, 13, 15, 16, 17, 18, 21, 23, 24, 25, 29, 31, 32, 33, 35, 37, 39, 41, 45, 47, 48, 52, 57, 61, 63, 65, 67, 68, 81];\n    const unluckyNumbers = [2, 4, 9, 10, 12, 14, 19, 20, 22, 26, 27, 28, 30, 34, 36, 38, 40, 42, 43, 44, 46, 49, 50, 51, 53, 54, 55, 56, 58, 59, 60, 62, 64, 66, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80];\n    \n    if (luckyNumbers.includes(number)) return 'good';\n    if (unluckyNumbers.includes(number)) return 'bad';\n    return 'normal';\n  },\n  // 获取等级文字\n  getLevelText(number) {\n    const level = this.getNumberLevel(number);\n    switch (level) {\n      case 'good': return '吉';\n      case 'bad': return '凶';\n      default: return '平';\n    }\n  },\n  // 获取三才分析"}