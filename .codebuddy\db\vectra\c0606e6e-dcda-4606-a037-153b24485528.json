{"chunk": 7, "numChunks": 13, "fileHash": "UUsAqDc9MY1pjBTLokkGc1GjjJmTwiOLz2lDZFa/4nc=", "filePath": "pages/index/index.wxss", "content": "  background: linear-gradient(135deg, #333 0%, #666 100%);\n  -webkit-background-clip: text;\n  background-clip: text;\n}\n\n.hot-desc {\n  font-size: 26rpx;\n  color: #999;\n  margin-bottom: 15rpx;\n  line-height: 1.5;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n}\n\n.hot-meta {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.hot-price {\n  font-size: 32rpx;\n  color: #ff6b6b;\n  font-weight: bold;\n  text-shadow: 0 2rpx 4rpx rgba(255, 107, 107, 0.2);\n}\n\n.hot-count {\n  font-size: 24rpx;\n  color: #999;\n  background: #f5f0ff;\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  font-weight: 500;\n}\n\n/* 热门文章 */\n.article-section {\n  margin-top: 50rpx;\n  padding-bottom: 40rpx;\n}\n\n.article-list {\n  display: flex;\n  flex-direction: column;\n  gap: 25rpx;\n}\n\n.article-item {\n  display: flex;\n  background: white;\n  border-radius: 25rpx;\n  padding: 25rpx;\n  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.article-item::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n"}