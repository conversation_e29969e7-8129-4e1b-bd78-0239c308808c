{"chunk": 1, "numChunks": 2, "fileHash": "uvJr2v7VjlYvc/uubWutyFkp31KM9o5uVQoAqVIwcF8=", "filePath": "pages/bazi/index.wxml", "content": "        <view class=\"grid-item\" wx:for=\"{{baziResult.pillars}}\" wx:key=\"index\">\n          <text class=\"pillar-name\">{{item.name}}</text>\n          <view class=\"pillar-content\">\n            <text>天干：{{item.heavenlyStem}}</text>\n            <text>地支：{{item.earthlyBranch}}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <view class=\"result-card\">\n      <view class=\"card-title\">五行分析</view>\n      <view class=\"wuxing-analysis\">\n        <view class=\"wuxing-item\" wx:for=\"{{baziResult.wuxing}}\" wx:key=\"element\">\n          <text class=\"element-name\">{{item.element}}</text>\n          <progress percent=\"{{item.percentage}}\" stroke-width=\"12\" color=\"{{item.color}}\"/>\n        </view>\n      </view>\n    </view>\n\n    <view class=\"result-card\">\n      <view class=\"card-title\">命理解读</view>\n      <view class=\"interpretation\">\n        <text>{{baziResult.interpretation}}</text>\n      </view>\n    </view>\n\n    <view class=\"result-card\">\n      <view class=\"card-title\">运势分析</view>\n      <view class=\"fortune-analysis\">\n        <view class=\"fortune-item\" wx:for=\"{{baziResult.fortune}}\" wx:key=\"aspect\">\n          <text class=\"aspect-name\">{{item.aspect}}</text>\n          <view class=\"stars\">\n            <text class=\"star\" wx:for=\"{{item.stars}}\" wx:key=\"index\">★</text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</view> \n"}