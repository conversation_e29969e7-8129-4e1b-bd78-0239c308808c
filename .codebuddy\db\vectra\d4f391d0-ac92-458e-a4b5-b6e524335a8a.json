{"chunk": 7, "numChunks": 38, "fileHash": "DpyinnhZJO6jrs0/VY0BXwrNjORlEQ1BeRywBJybhxQ=", "filePath": "pages/ai-chat/ai-chat.js", "content": "const app = getApp()\nPage({\n  async handleWxLogin() {\n    try {\n      wx.showLoading({ title: '登录中...' })\n      \n      const userInfo = await apiService.wxLogin()\n      this.setData({ \n        isLoggedIn: true,\n        userInfo \n      })\n\n      this.addMessage({\n        type: 'system',\n        content: `🎉 登录成功！欢迎 ${userInfo.nickname || '用户'}，现在可以享受完整的命理分析功能了。`\n      })\n\n      wx.hideLoading()\n      wx.showToast({\n        title: '登录成功',\n        icon: 'success'\n      })\n    } catch (error) {\n      wx.hideLoading()\n      console.error('登录失败:', error)\n      wx.showToast({\n        title: '登录失败，请重试',\n        icon: 'none'\n      })\n    }\n  },\n  /**\n   * 退出登录\n   */\n  handleLogout() {\n    wx.showModal({\n      title: '确认退出',\n      content: '退出后将无法使用个性化功能，确定要退出吗？',\n      success: (res) => {\n        if (res.confirm) {\n          apiService.logout()\n          this.setData({ \n            isLoggedIn: false,\n            userInfo: null \n          })\n          this.addMessage({\n            type: 'system',\n            content: '👋 已退出登录，感谢使用恒琦易道！'\n          })\n        }\n      }\n    })\n  },\n  /**\n   * 输入框聚焦时展开\n   */\n  onInputFocus() {\n    this.setData({\n      isInputExpanded: true\n    });\n  },\n  /**\n   * 取消输入，收起输入框\n   */\n  onCancelInput() {\n    this.setData({\n      isInputExpanded: false,\n      inputValue: '',\n      fabIcon: '💬'\n    });\n  },"}