{"chunk": 11, "numChunks": 76, "fileHash": "lPYWZE8QItgwSyEsMXHtl6y/HYBCI8BdGaV+1l8ICMA=", "filePath": "docs/md/API_INTERFACE_DOCUMENTATION.md", "content": "    }\n  ],\n  \"intent_hint\": \"string\",          // 意图提示 (可选)\n  \"analysis_type\": \"string\"         // 分析类型 (可选: bazi/yijing/fengshui/wuxing)\n}\n\nResponse:\n{\n  \"status\": \"success|error\",\n  \"message\": \"string\",\n  \"data\": {\n    \"session_id\": \"string\",         // 会话ID\n    \"message_id\": \"string\",         // 消息ID\n    \"response\": {\n      \"content\": \"string\",          // AI回复内容\n      \"type\": \"string\",             // 回复类型 (text/analysis/suggestion)\n      \"intent\": \"string\",           // 识别的意图\n      \"confidence\": \"number\",       // 置信度 (0-1)\n      \"suggestions\": [\"string\"],    // 建议问题\n      \"actions\": [                  // 可执行操作\n        {\n          \"type\": \"string\",         // 操作类型\n          \"text\": \"string\",         // 显示文本\n          \"url\": \"string\",          // 跳转链接\n          \"icon\": \"string\"          // 图标\n        }\n      ]\n    },\n    \"analysis\": {                   // 分析结果 (如果有)\n      \"type\": \"string\",             // 分析类型\n      \"result\": \"object\",           // 分析结果\n      \"score\": \"number\",            // 评分\n      \"summary\": \"string\"           // 总结\n    },\n    \"usage\": {                      // 使用统计\n      \"tokens_used\": \"number\",      // 使用的token数\n      \"points_cost\": \"number\"       // 消耗积分\n    }\n  }\n}\n```\n\n#### 2. 获取聊天历史\n```\nGET /api/ai-chat/history\nAuthorization: Bearer {token}\n"}