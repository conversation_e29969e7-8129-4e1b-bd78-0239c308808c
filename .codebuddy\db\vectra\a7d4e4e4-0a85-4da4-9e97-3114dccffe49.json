{"chunk": 9, "numChunks": 16, "fileHash": "9ZF2KXbrmWumjo5LxJnE/F14ePFZLBzZ6HxD55jx3zE=", "filePath": "pages/fengshui/fengshui.js", "content": "// pages/fengshui/fengshui.js\nPage({\n  // 建筑年份选择\n  onBuildYearChange(e) {\n    this.setData({\n      buildYear: e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 房间数量增减\n  increaseRooms() {\n    this.setData({\n      rooms: this.data.rooms + 1\n    })\n    this.checkCanSubmit()\n  },\n  decreaseRooms() {\n    if (this.data.rooms > 1) {\n      this.setData({\n        rooms: this.data.rooms - 1\n      })\n      this.checkCanSubmit()\n    }\n  },\n  // 面积输入\n  onAreaInput(e) {\n    this.setData({\n      area: e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 楼层输入\n  onCurrentFloorInput(e) {\n    this.setData({\n      currentFloor: e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  onTotalFloorInput(e) {\n        this.setData({\n      totalFloor: e.detail.value\n    })\n    this.checkCanSubmit()\n  },\n  // 周边环境选择\n  toggleSurrounding(e) {\n    const index = e.currentTarget.dataset.index\n    const surroundings = this.data.surroundings\n    surroundings[index].checked = !surroundings[index].checked\n    \n    this.setData({ surroundings })\n    this.checkCanSubmit()\n  },\n  // 特殊位置选择\n  toggleSpecialLocation(e) {\n    const index = e.currentTarget.dataset.index\n    const specialLocations = this.data.specialLocations\n    specialLocations[index].checked = !specialLocations[index].checked\n    \n    this.setData({ specialLocations })\n    this.checkCanSubmit()\n  },\n  // 检查是否可以提交"}