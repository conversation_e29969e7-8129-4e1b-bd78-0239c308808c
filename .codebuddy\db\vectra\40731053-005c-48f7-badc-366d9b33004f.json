{"chunk": 1, "numChunks": 7, "fileHash": "PQSUOKQzQ1nTJojwaszEOU93ZW43HSbpVKnnb8k/pfY=", "filePath": "pages/marriage/index.wxss", "content": ".card-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 32rpx;\n  padding-bottom: 16rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.card-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.card-icon {\n  font-size: 40rpx;\n  opacity: 0.7;\n}\n\n/* 表单项 */\n.form-item {\n  margin-bottom: 32rpx;\n}\n\n.form-item:last-child {\n  margin-bottom: 0;\n}\n\n.item-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.label {\n  font-size: 32rpx;\n  color: #333;\n  font-weight: 500;\n  margin-right: 8rpx;\n}\n\n.required {\n  color: #ff4757;\n  font-size: 28rpx;\n  font-weight: bold;\n}\n\n.optional {\n  color: #999;\n  font-size: 24rpx;\n  background: #f0f0f0;\n  padding: 4rpx 8rpx;\n  border-radius: 8rpx;\n}\n\n/* 选择器 */\n.picker-wrapper {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n  border: 2rpx solid #e8e8e8;\n  border-radius: 16rpx;\n  padding: 0 20rpx;\n  background: #fafafa;\n  transition: all 0.3s ease;\n}\n\n.picker-wrapper:active {\n  border-color: #667eea;\n  background: white;\n}\n\n.picker-text {\n  font-size: 32rpx;\n  flex: 1;\n}\n\n"}