{"chunk": 1, "numChunks": 5, "fileHash": "gwytGpkD8JZ0FXKWaXVx5+UFLQiWHNueoakBC5jWArU=", "filePath": "pages/settings/index.wxml", "content": "          <view class=\"picker-text\">\n            {{fontSizeOptions.find(item => item.value === generalSettings.fontSize).label}}\n          </view>\n        </picker>\n      </view>\n    </view>\n  </view>\n\n  <!-- 隐私设置 -->\n  <view class=\"section\">\n    <view class=\"section-title\">隐私设置</view>\n    <view class=\"setting-list\">\n      <!-- 在线状态 -->\n      <view class=\"setting-item\">\n        <view class=\"setting-left\">\n          <image class=\"setting-icon\" src=\"/assets/icons/online.png\"/>\n          <text class=\"setting-name\">显示在线状态</text>\n        </view>\n        <switch \n          checked=\"{{privacySettings.showOnline}}\"\n          bindchange=\"toggleSetting\"\n          data-type=\"privacy\"\n          data-key=\"showOnline\"\n        />\n      </view>\n\n      <!-- 允许搜索 -->\n      <view class=\"setting-item\">\n        <view class=\"setting-left\">\n          <image class=\"setting-icon\" src=\"/assets/icons/search.png\"/>\n          <text class=\"setting-name\">允许他人搜索</text>\n        </view>\n        <switch \n          checked=\"{{privacySettings.allowSearch}}\"\n          bindchange=\"toggleSetting\"\n          data-type=\"privacy\"\n          data-key=\"allowSearch\"\n        />\n      </view>\n\n      <!-- 最后在线时间 -->\n      <view class=\"setting-item\">\n        <view class=\"setting-left\">\n          <image class=\"setting-icon\" src=\"/assets/icons/last-seen.png\"/>\n          <text class=\"setting-name\">显示最后在线时间</text>\n        </view>\n        <switch \n          checked=\"{{privacySettings.showLastSeen}}\"\n          bindchange=\"toggleSetting\"\n          data-type=\"privacy\"\n          data-key=\"showLastSeen\"\n        />\n      </view>\n    </view>\n  </view>\n\n  <!-- 消息通知 -->\n  <view class=\"section\">\n"}