{"chunk": 2, "numChunks": 4, "fileHash": "DpTd8f7+cZkfi/n8agYLW+BkjecanzloeqTjzVIZsfQ=", "filePath": "pages/customer-service/customer-service.wxml", "content": "          <text class=\"question-text\">{{item.question}}</text>\n          <view class=\"expand-icon {{item.expanded ? 'expanded' : ''}}\">▼</view>\n        </view>\n        <view class=\"faq-answer {{item.expanded ? 'show' : ''}}\">\n          <text>{{item.answer}}</text>\n        </view>\n      </view>\n    </view>\n  </view>\n\n  <!-- 快速操作 -->\n  <view class=\"quick-actions\">\n    <view class=\"section-title\">快速操作</view>\n    <view class=\"action-grid\">\n      <view class=\"action-item\" bindtap=\"goToUserGuide\">\n        <view class=\"action-icon\">📖</view>\n        <text>使用指南</text>\n      </view>\n      <view class=\"action-item\" bindtap=\"goToAccountSecurity\">\n        <view class=\"action-icon\">🔒</view>\n        <text>账号安全</text>\n      </view>\n      <view class=\"action-item\" bindtap=\"goToPrivacyPolicy\">\n        <view class=\"action-icon\">📄</view>\n        <text>隐私政策</text>\n      </view>\n      <view class=\"action-item\" bindtap=\"goToTermsOfService\">\n        <view class=\"action-icon\">📋</view>\n        <text>服务条款</text>\n      </view>\n    </view>\n  </view>\n\n  <!-- 联系客服浮动按钮 -->\n  <view class=\"floating-contact\" bindtap=\"openQuickContact\">\n    <view class=\"floating-icon\">💬</view>\n    <text class=\"floating-text\">联系客服</text>\n  </view>\n\n  <!-- 快速联系弹窗 -->\n  <view class=\"modal\" wx:if=\"{{showContactModal}}\">\n"}