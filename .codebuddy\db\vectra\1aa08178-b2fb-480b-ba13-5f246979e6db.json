{"chunk": 1, "numChunks": 7, "fileHash": "Cqx4nGobpVxA67G0CO//E9qs4sv6sw0mA2CIPhYqZ4g=", "filePath": "subpages/divination/bazi/bazi.wxss", "content": "  font-size: 24rpx;\n  color: #999;\n}\n\n.form-group {\n  margin-bottom: 30rpx;\n}\n\n.form-label {\n  font-size: 28rpx;\n  color: #666;\n  display: block;\n  margin-bottom: 16rpx;\n}\n\n.form-input {\n  width: 100%;\n  height: 88rpx;\n  background: #f8f9fa;\n  border-radius: 12rpx;\n  padding: 0 24rpx;\n  font-size: 28rpx;\n  box-sizing: border-box;\n}\n\n/* 性别选择 */\n.gender-select {\n  display: flex;\n  gap: 20rpx;\n}\n\n.gender-option {\n  flex: 1;\n  height: 88rpx;\n  background: #f8f9fa;\n  border-radius: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  font-size: 28rpx;\n  color: #666;\n}\n\n.gender-option.active {\n  background: linear-gradient(135deg, #8a2be2, #9932cc);\n  color: white;\n}\n\n.gender-icon {\n  font-size: 32rpx;\n  margin-right: 10rpx;\n}\n\n/* 选择器样式 */\n.picker-display {\n  height: 88rpx;\n  background: #f8f9fa;\n  border-radius: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 24rpx;\n}\n\n.picker-value {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.picker-arrow {\n  color: #999;\n  font-size: 20rpx;\n}\n\n/* 分析按钮 */\n.analyze-btn {\n"}