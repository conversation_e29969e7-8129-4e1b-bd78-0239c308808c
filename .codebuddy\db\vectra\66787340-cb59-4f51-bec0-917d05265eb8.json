{"chunk": 53, "numChunks": 55, "fileHash": "fCZ6nFoNZXQqhvADUzMwGWD6jOU+ajj61B8BD3CwDgs=", "filePath": "pages/name-test/name-test.js", "content": "// 姓名测试页面逻辑\nPage({\n  getHealthAnalysis(number) {\n    const analyses = {\n      1: '身体健康状况良好，精力充沛',\n      2: '需要注意情绪调节，避免过度忧虑',\n      3: '活力充沛，但要注意劳逸结合',\n      4: '体质较弱，需要加强锻炼',\n      5: '精神状态良好，但要注意规律作息'\n    };\n    return analyses[number % 5 + 1] || '健康状况一般，需要注意保养';\n  },\n  // 根据等级获取分数\n  getScore(level) {\n    switch (level) {\n      case 'good': return Math.floor(Math.random() * 20) + 80; // 80-99\n      case 'bad': return Math.floor(Math.random() * 30) + 30; // 30-59\n      default: return Math.floor(Math.random() * 20) + 60; // 60-79\n    }\n  },\n  // 计算总分\n  calculateTotalScore(wuge, sancai) {\n    const scores = wuge.map(item => this.getScore(item.level));\n    const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;\n    return Math.round(average);\n  },\n  // 生成改名建议"}