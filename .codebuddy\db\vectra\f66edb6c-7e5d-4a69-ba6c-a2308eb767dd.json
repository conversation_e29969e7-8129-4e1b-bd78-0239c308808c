{"chunk": 69, "numChunks": 76, "fileHash": "lPYWZE8QItgwSyEsMXHtl6y/HYBCI8BdGaV+1l8ICMA=", "filePath": "docs/md/API_INTERFACE_DOCUMENTATION.md", "content": "  status ENUM('pending', 'completed', 'cancelled', 'expired') DEFAULT 'completed',\n  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n\n  <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,\n  INDEX idx_user_type (user_id, type),\n  INDEX idx_created_at (created_at),\n  INDEX idx_source (source),\n  INDEX idx_status (status)\n);\n```\n\n### 企业微信相关表\n\n#### 9. 企业用户表 (enterprise_users)\n```sql\nCREATE TABLE enterprise_users (\n  id VARCHAR(36) PRIMARY KEY,\n  corp_id VARCHAR(100) NOT NULL,\n  user_id VARCHAR(100) NOT NULL,\n  name VARCHAR(100) NOT NULL,\n  department JSON,\n  position VARCHAR(100),\n  mobile VARCHAR(20),\n  email VARCHAR(100),\n  avatar TEXT,\n  status TINYINT DEFAULT 1,\n  is_leader_in_dept JSON,\n  main_department INT,\n  permissions JSON,\n  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n\n  UNIQUE KEY uk_corp_user (corp_id, user_id),\n  INDEX idx_corp_id (corp_id),\n  INDEX idx_status (status)\n);\n```\n\n#### 10. 部门表 (departments)\n```sql\nCREATE TABLE departments (\n  id INT PRIMARY KEY,\n  corp_id VARCHAR(100) NOT NULL,\n  name VARCHAR(100) NOT NULL,\n  parent_id INT DEFAULT 0,\n  order_num INT DEFAULT 0,\n  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n\n  INDEX idx_corp_parent (corp_id, parent_id),\n  INDEX idx_corp_id (corp_id)\n);\n```\n\n### 知识库相关表\n\n#### 11. 知识库文档表 (knowledge_documents)\n```sql\n"}