{"chunk": 4, "numChunks": 19, "fileHash": "PEPeuJm140RAD7VBd85WlnsfcWUdoorqVSQj8ISByl0=", "filePath": "pages/ai-chat/ai-chat.wxss", "content": ".message-wrapper {\n  margin-bottom: 22rpx;\n}\n\n/* 时间分隔线 */\n.time-divider {\n  text-align: center;\n  margin: 24rpx 0;\n}\n\n.time-text {\n  font-size: 24rpx;\n  color: #9ca3af;\n  background: rgba(255, 255, 255, 0.8);\n  padding: 8rpx 16rpx;\n  border-radius: 12rpx;\n  display: inline-block;\n}\n\n/* 消息气泡 */\n.message-bubble {\n  display: flex;\n  align-items: flex-start;\n  gap: 16rpx;\n  margin-bottom: 16rpx;\n  animation: message-fade-in 0.4s ease-out;\n}\n\n.user-message {\n  flex-direction: row-reverse;\n}\n\n.ai-message {\n  flex-direction: row;\n}\n\n/* 头像样式 */\n.message-avatar {\n  width: 72rpx;\n  height: 72rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24rpx;\n  font-weight: 600;\n  flex-shrink: 0;\n  position: relative;\n}\n\n.user-avatar {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.ai-avatar {\n  background: linear-gradient(145deg, #7928CA, #9b4dca);\n  color: white;\n  box-shadow:\n    0 8rpx 20rpx rgba(123, 40, 202, 0.25),\n    0 4rpx 8rpx rgba(0, 0, 0, 0.06);\n}\n\n.avatar-glow-small {\n  position: absolute;\n"}