{"chunk": 1, "numChunks": 5, "fileHash": "kj9oZjkyC/FHxSTFwX241qsgDqE06+EB/MIRUDnQNTM=", "filePath": "pages/login/login.wxml", "content": "          <text class=\"user-nickname\">{{userInfo.nickName}}</text>\n          <text class=\"user-desc\">微信用户</text>\n        </view>\n        <view class=\"auth-status success\">已授权</view>\n      </view>\n\n      <!-- 手机号授权 -->\n      <view class=\"phone-auth-section\" wx:if=\"{{userInfo.nickName}}\">\n        <view class=\"auth-item\">\n          <view class=\"auth-info\">\n            <view class=\"auth-icon\">📱</view>\n            <view class=\"auth-content\">\n              <text class=\"auth-title\">手机号授权</text>\n              <text class=\"auth-desc\">{{phoneNumber || '用于接收重要通知'}}</text>\n            </view>\n          </view>\n          <button \n            class=\"auth-btn {{phoneNumber ? 'authorized' : ''}}\"\n            open-type=\"getPhoneNumber\"\n            bindgetphonenumber=\"onGetPhoneNumber\"\n            wx:if=\"{{!phoneNumber}}\"\n          >\n            获取手机号\n          </button>\n          <view class=\"auth-status success\" wx:else>已授权</view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 服务条款 -->\n    <view class=\"terms-section\" wx:if=\"{{userInfo.nickName}}\">\n      <view class=\"terms-content\">\n        <checkbox-group bindchange=\"onTermsChange\">\n          <label class=\"terms-checkbox\">\n            <checkbox value=\"agreed\" checked=\"{{termsAgreed}}\" />\n            <text class=\"terms-text\">\n              我已阅读并同意\n              <text class=\"link\" bindtap=\"viewUserAgreement\">《用户协议》</text>\n              和\n              <text class=\"link\" bindtap=\"viewPrivacyPolicy\">《隐私政策》</text>\n            </text>\n          </label>\n        </checkbox-group>\n      </view>\n"}