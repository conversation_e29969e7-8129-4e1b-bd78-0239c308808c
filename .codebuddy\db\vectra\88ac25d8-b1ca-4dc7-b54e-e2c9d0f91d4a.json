{"chunk": 2, "numChunks": 3, "fileHash": "uGRdgU84yQtnSxCsNHminVUo8YlmVO1T09FtgqT2HJ4=", "filePath": "pages/feedback/feedback.wxml", "content": "          bindtap=\"viewFeedbackDetail\"\n          data-id=\"{{item.id}}\"\n        >\n          <view class=\"history-content\">\n            <view class=\"history-title\">{{item.title}}</view>\n            <view class=\"history-desc\">{{item.content}}</view>\n            <view class=\"history-time\">{{item.createTime}}</view>\n          </view>\n          <view class=\"history-status {{item.status}}\">\n            {{item.statusText}}\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 提交按钮 -->\n    <view class=\"submit-section\">\n      <button \n        class=\"submit-btn\"\n        form-type=\"submit\"\n        disabled=\"{{!canSubmit}}\"\n      >\n        提交反馈\n      </button>\n      <view class=\"submit-tips\">\n        <text>提交后我们会在24小时内回复您</text>\n      </view>\n    </view>\n  </form>\n\n  <!-- 加载状态 -->\n  <view class=\"loading-overlay\" wx:if=\"{{isSubmitting}}\">\n    <view class=\"loading-content\">\n      <view class=\"loading-spinner\"></view>\n      <text class=\"loading-text\">正在提交...</text>\n    </view>\n  </view>\n\n  <!-- 成功提示 -->\n  <view class=\"success-modal\" wx:if=\"{{showSuccessModal}}\">\n    <view class=\"modal-mask\"></view>\n    <view class=\"modal-content\">\n      <view class=\"success-icon\">✅</view>\n      <view class=\"success-title\">提交成功</view>\n      <view class=\"success-desc\">感谢您的反馈，我们会尽快处理</view>\n      <button class=\"success-btn\" bindtap=\"closeSuccessModal\">确定</button>\n    </view>\n  </view>\n</view> \n"}