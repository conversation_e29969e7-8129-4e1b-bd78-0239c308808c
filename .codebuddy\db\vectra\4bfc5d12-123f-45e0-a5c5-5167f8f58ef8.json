{"chunk": 3, "numChunks": 9, "fileHash": "yDcz6SvSgZVWvKwtTExumLiXk7y0FYiP8gZ9qIHubRA=", "filePath": "pages/fengshui/fengshui.wxss", "content": "  border-radius: 12rpx;\n  transition: all 0.3s ease;\n}\n\n.counter-btn.disabled {\n  opacity: 0.5;\n  background: #f0f0f0;\n  color: #999;\n}\n\n.counter-value {\n  font-size: 32rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n/* 楼层输入 */\n.floor-inputs {\n  display: flex;\n  align-items: center;\n  gap: 20rpx;\n}\n\n.floor-input {\n  flex: 1;\n}\n\n.floor-separator {\n  font-size: 32rpx;\n  color: #999;\n  font-weight: 500;\n}\n\n/* 复选框组 */\n.checkbox-group {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n}\n\n.checkbox-item {\n  flex: 0 0 calc(33.33% - 12rpx);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 16rpx;\n  border: 2rpx solid #e8e8e8;\n  border-radius: 16rpx;\n  background: #fafafa;\n  transition: all 0.3s ease;\n}\n\n.checkbox-item.active {\n  border-color: #667eea;\n  background: rgba(102, 126, 234, 0.1);\n}\n\n.checkbox-icon {\n  font-size: 36rpx;\n  margin-bottom: 8rpx;\n}\n\n.checkbox-text {\n  font-size: 24rpx;\n  color: #666;\n  text-align: center;\n}\n\n/* 提示卡片 */\n.tips-card {\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border-radius: 20rpx;\n  padding: 24rpx;\n"}