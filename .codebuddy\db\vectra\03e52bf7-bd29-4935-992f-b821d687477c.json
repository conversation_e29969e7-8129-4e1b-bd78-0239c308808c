{"chunk": 2, "numChunks": 13, "fileHash": "UUsAqDc9MY1pjBTLokkGc1GjjJmTwiOLz2lDZFa/4nc=", "filePath": "pages/index/index.wxss", "content": "  font-size: 64rpx;\n  font-weight: bold;\n  color: #7e57c2;\n  margin-right: 20rpx;\n  font-family: 'DIN Alternate', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\n}\n\n.date-info {\n  display: flex;\n  flex-direction: column;\n  gap: 4rpx;\n}\n\n.month-year {\n  font-size: 28rpx;\n  color: #666;\n  font-weight: 500;\n}\n\n.weekday {\n  font-size: 24rpx;\n  color: #9575cd;\n  font-weight: 500;\n}\n\n.lunar-info {\n  display: flex;\n  flex-direction: column;\n  gap: 12rpx;\n}\n\n.lunar-date {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.lunar-text {\n  font-size: 32rpx;\n  color: #333;\n  font-weight: 600;\n}\n\n.ganzhi-year {\n  font-size: 24rpx;\n  color: #666;\n  background: rgba(149, 117, 205, 0.1);\n  padding: 4rpx 12rpx;\n  border-radius: 12rpx;\n}\n\n.calendar-details {\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.term-info {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n}\n\n.term-icon {\n  font-size: 24rpx;\n}\n\n.term-text {\n  font-size: 24rpx;\n  color: #9575cd;\n  font-weight: 500;\n}\n\n.yiji-info {\n  display: flex;\n  flex-direction: column;\n  gap: 12rpx;\n"}