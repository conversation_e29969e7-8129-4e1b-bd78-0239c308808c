{"chunk": 54, "numChunks": 55, "fileHash": "fCZ6nFoNZXQqhvADUzMwGWD6jOU+ajj61B8BD3CwDgs=", "filePath": "pages/name-test/name-test.js", "content": "// 姓名测试页面逻辑\nPage({\n  generateSuggestions(surname, gender) {\n    const maleNames = ['志强', '建华', '志明', '俊杰', '文博'];\n    const femaleNames = ['雅琳', '诗涵', '梦洁', '雨婷', '思怡'];\n    const names = gender === 'male' ? maleNames : femaleNames;\n    \n    return names.map((name, index) => ({\n      name: surname + name,\n      score: Math.floor(Math.random() * 10) + 85,\n      reason: '五格配置协调，三才搭配良好'\n    }));\n  },\n  // 保存到历史记录\n  saveToHistory(result) {\n    try {\n      const history = wx.getStorageSync('nameTestHistory') || [];\n      const newRecord = {\n        ...result,\n        timestamp: new Date().getTime(),\n        date: new Date().toLocaleDateString()\n      };\n      \n      history.unshift(newRecord);\n      // 只保留最近10条记录\n      if (history.length > 10) {\n        history.splice(10);\n      }\n      \n      wx.setStorageSync('nameTestHistory', history);\n    } catch (error) {\n      console.error('保存历史记录失败:', error);\n    }\n  },\n  // 保存结果\n  saveResult() {\n    wx.showToast({\n      title: '结果已保存',\n      icon: 'success'\n    });\n  },\n  // 分享结果\n  shareResult() {\n    wx.showShareMenu({\n      withShareTicket: true,\n      menus: ['shareAppMessage', 'shareTimeline']\n    });\n  },\n  // 重新测试"}