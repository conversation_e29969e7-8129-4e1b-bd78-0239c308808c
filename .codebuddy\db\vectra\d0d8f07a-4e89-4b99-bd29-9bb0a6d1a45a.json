{"chunk": 5, "numChunks": 9, "fileHash": "RJtqiMp6ufLzCDA9+EFDGWW4xytfcsd5HAKWaRu2Drc=", "filePath": "frontend-implementation/pages/index/index.js", "content": "// 首页 - 使用示例\ncreatePage({\n  // 页面需要登录\n  requireLogin: true,\n  // 分享配置\n  shareTitle: '卦里乾坤 - 专业命理分析平台',\n  sharePath: '/pages/index/index',\n  /**\n   * 页面加载\n   */\n  async pageOnLoad(options) {\n    // 检查是否是新用户\n    this.checkNewUser()\n    \n    // 加载页面数据\n    await this.loadPageData()\n  },\n  /**\n   * 页面显示\n   */\n  pageOnShow() {\n    // 刷新用户统计\n    this.refreshUserStats()\n  },\n  /**\n   * 检查新用户\n   */\n  checkNewUser() {\n    const userInfo = store.getState('user.userInfo')\n    const isFirstTime = !wx.getStorageSync('hasVisited')\n    \n    if (isFirstTime && userInfo) {\n      this.setData({ showWelcome: true })\n      wx.setStorageSync('hasVisited', true)\n    }\n  },\n  /**\n   * 加载页面数据\n   */\n  async loadPageData() {\n    try {\n      this.showLoading('加载中...')\n      \n      // 并行加载多个数据\n      const promises = [\n        this.loadUserStats(),\n        this.loadBirthInfo(),\n        this.loadRecentAnalyses(),\n        this.loadChatSessions(),\n        this.loadQuickActions()\n      ]\n      \n      await Promise.allSettled(promises)\n    } catch (error) {\n      console.error('加载页面数据失败:', error)\n      this.showError('加载失败，请稍后重试')\n    } finally {\n      this.hideLoading()\n    }\n  },\n  /**\n   * 加载用户统计\n   */"}