{"chunk": 1, "numChunks": 6, "fileHash": "m/bcrIpornnqwv0iP6r9/jZF/qprhOPA5RFcfEHrukg=", "filePath": "pages/profile/profile.wxss", "content": "  box-shadow: 0 8rpx 24rpx rgba(149, 117, 205, 0.2);\n}\n\n.user-info {\n  flex: 1;\n}\n\n.user-name {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10rpx;\n  display: flex;\n  align-items: center;\n}\n\n.edit-icon {\n  margin-left: 10rpx;\n  font-size: 24rpx;\n}\n\n.user-desc {\n  display: flex;\n  align-items: center;\n  gap: 20rpx;\n}\n\n.user-id {\n  font-size: 26rpx;\n  color: #999;\n  padding: 4rpx 16rpx;\n  background: #f5f0ff;\n  border-radius: 20rpx;\n}\n\n.user-level {\n  font-size: 26rpx;\n  color: #9575cd;\n  padding: 4rpx 16rpx;\n  background: linear-gradient(135deg, #f5f0ff 0%, #ece7f5 100%);\n  border-radius: 20rpx;\n}\n\n.login-btn {\n  margin-top: 20rpx;\n  padding: 16rpx 40rpx;\n  background: linear-gradient(135deg, #9575cd 0%, #7e57c2 100%);\n  color: white;\n  border-radius: 50rpx;\n  font-size: 30rpx;\n  font-weight: 500;\n  border: none;\n  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.3);\n}\n\n/* 用户统计 */\n.user-stats {\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n}\n\n.stat-item {\n  flex: 1;\n"}