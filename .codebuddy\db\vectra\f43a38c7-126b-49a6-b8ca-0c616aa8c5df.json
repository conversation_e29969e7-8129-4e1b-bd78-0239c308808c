{"chunk": 1, "numChunks": 19, "fileHash": "PEPeuJm140RAD7VBd85WlnsfcWUdoorqVSQj8ISByl0=", "filePath": "pages/ai-chat/ai-chat.wxss", "content": "  width: 64rpx;\n  height: 64rpx;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.action-btn:active {\n  transform: scale(0.9);\n  background: rgba(255, 255, 255, 1);\n}\n\n.action-icon {\n  font-size: 32rpx;\n}\n\n/* 聊天容器 */\n.chat-container {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20rpx);\n  border-radius: 32rpx 32rpx 0 0;\n  margin-top: 20rpx;\n  overflow: hidden;\n  width: 97%;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n/* 消息滚动区域 */\n.messages-scroll {\n  flex: 1;\n  padding: 6rpx;\n  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);\n  overflow-y: scroll;\n  -webkit-overflow-scrolling: touch;\n  width: 100%;\n  box-sizing: border-box;\n}\n\n/* 欢迎区域 */\n.welcome-section {\n  /* padding: 40rpx 0; */\n  text-align: center;\n}\n\n.welcome-header {\n  margin-bottom: 48rpx;\n}\n\n.ai-avatar-large {\n  position: relative;\n  display: inline-block;\n  margin-bottom: 24rpx;\n}\n\n.avatar-glow {\n  position: absolute;\n  top: -20rpx;\n  left: -20rpx;\n"}