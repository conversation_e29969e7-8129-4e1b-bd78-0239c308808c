{"chunk": 5, "numChunks": 7, "fileHash": "OC4bchRgffgqpj5ffRE3rPdsxwNsfIQSvzKsBv0wUBE=", "filePath": "pages/profile/index.js", "content": "const app = getApp()\nPage({\n  data: {\n    userInfo: {\n      avatarUrl: '',\n      nickName: '未登录',\n      points: 0,\n      level: '普通用户'\n    },\n    // 用户统计信息\n    statistics: {\n      orders: 0,\n      favorites: 0,\n      points: 0,\n      coupons: 0\n    },\n    // 功能列表\n    functionList: [\n      {\n        id: 'orders',\n        name: '我的订单',\n        icon: '/assets/icons/order.png',\n        url: '/pages/orders/index'\n      },\n      {\n        id: 'favorites',\n        name: '我的收藏',\n        icon: '/assets/icons/favorite.png',\n        url: '/pages/favorites/index'\n      },\n      {\n        id: 'points',\n        name: '积分中心',\n        icon: '/assets/icons/points.png',\n        url: '/pages/points/index'\n      },\n      {\n        id: 'coupons',\n        name: '优惠券',\n        icon: '/assets/icons/coupon.png',\n        url: '/pages/coupons/index'\n      },\n      {\n        id: 'history',\n        name: '浏览历史',\n        icon: '/assets/icons/history.png',\n        url: '/pages/history/index'\n      },\n      {\n        id: 'address',\n        name: '地址管理',\n        icon: '/assets/icons/address.png',\n        url: '/pages/address/index'\n      }\n    ],\n    // 设置列表"}