{"chunk": 0, "numChunks": 13, "fileHash": "UUsAqDc9MY1pjBTLokkGc1GjjJmTwiOLz2lDZFa/4nc=", "filePath": "pages/index/index.wxss", "content": "/**index.wxss**/\npage {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: var(--background-color) !important;\n}\n\n.page-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f5f0ff 0%, #e8dff5 100%);\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n.scrollarea {\n  height: 100vh;\n  overflow: hidden;\n  background: transparent;\n}\n\n.userinfo {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  color: var(--text-light);\n  width: 80%;\n}\n\n.userinfo-avatar {\n  overflow: hidden;\n  width: 128rpx;\n  height: 128rpx;\n  margin: 20rpx;\n  border-radius: 50%;\n  border: 4rpx solid var(--primary-lightest);\n  box-shadow: 0 8rpx 24rpx var(--shadow-color);\n}\n\n.usermotto {\n  margin-top: 200px;\n}\n\n/* 容器美化 */\n.container {\n  min-height: 100vh;\n  padding-bottom: calc(env(safe-area-inset-bottom) + 100rpx);\n}\n\n/* 错误提示样式 - 美化 */\n.error-tip {\n  text-align: center;\n  color: #999;\n  font-size: 28rpx;\n  padding: 40rpx;\n}\n\n/* 加载中 - 美化 */\n.loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n}\n\n.loading-icon {\n  width: 60rpx;\n  height: 60rpx;\n  animation: rotate 1.5s linear infinite;\n}\n\n@keyframes rotate {\n"}