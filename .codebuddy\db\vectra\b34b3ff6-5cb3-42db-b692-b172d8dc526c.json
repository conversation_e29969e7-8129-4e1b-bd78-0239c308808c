{"chunk": 2, "numChunks": 5, "fileHash": "gwytGpkD8JZ0FXKWaXVx5+UFLQiWHNueoakBC5jWArU=", "filePath": "pages/settings/index.wxml", "content": "    <view class=\"section-title\">消息通知</view>\n    <view class=\"setting-list\">\n      <!-- 推送通知 -->\n      <view class=\"setting-item\">\n        <view class=\"setting-left\">\n          <image class=\"setting-icon\" src=\"/assets/icons/push.png\"/>\n          <text class=\"setting-name\">推送通知</text>\n        </view>\n        <switch \n          checked=\"{{notificationSettings.pushEnabled}}\"\n          bindchange=\"toggleSetting\"\n          data-type=\"notification\"\n          data-key=\"pushEnabled\"\n        />\n      </view>\n\n      <!-- 声音 -->\n      <view class=\"setting-item\">\n        <view class=\"setting-left\">\n          <image class=\"setting-icon\" src=\"/assets/icons/sound.png\"/>\n          <text class=\"setting-name\">声音</text>\n        </view>\n        <switch \n          checked=\"{{notificationSettings.sound}}\"\n          bindchange=\"toggleSetting\"\n          data-type=\"notification\"\n          data-key=\"sound\"\n        />\n      </view>\n\n      <!-- 震动 -->\n      <view class=\"setting-item\">\n        <view class=\"setting-left\">\n          <image class=\"setting-icon\" src=\"/assets/icons/vibrate.png\"/>\n          <text class=\"setting-name\">震动</text>\n        </view>\n        <switch \n          checked=\"{{notificationSettings.vibrate}}\"\n          bindchange=\"toggleSetting\"\n          data-type=\"notification\"\n          data-key=\"vibrate\"\n        />\n      </view>\n\n      <!-- 消息预览 -->\n      <view class=\"setting-item\">\n        <view class=\"setting-left\">\n          <image class=\"setting-icon\" src=\"/assets/icons/preview.png\"/>\n          <text class=\"setting-name\">显示消息预览</text>\n        </view>\n        <switch \n          checked=\"{{notificationSettings.showPreview}}\"\n          bindchange=\"toggleSetting\"\n          data-type=\"notification\"\n          data-key=\"showPreview\"\n"}