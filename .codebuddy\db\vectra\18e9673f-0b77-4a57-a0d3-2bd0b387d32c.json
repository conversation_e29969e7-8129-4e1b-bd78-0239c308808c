{"chunk": 15, "numChunks": 18, "fileHash": "yi4gCVr62AiqkHDrZGxGnPy2D7On+5yxIebQ7IGvQ04=", "filePath": "pages/community/community.js", "content": "// pages/community/community.js\nPage({\n  },\n  /**\n   * 查看帖子详情\n   */\n  viewPost(e) {\n    const postId = e.currentTarget.dataset.id\n    wx.navigateTo({\n      url: `/pages/post-detail/post-detail?id=${postId}`\n    })\n  },\n  previewImage(e) {\n    const { urls, current } = e.currentTarget.dataset\n    wx.previewImage({\n      urls,\n      current\n    })\n  },\n  // 选择标签\n  selectTag(e) {\n    const tag = e.currentTarget.dataset.tag\n    this.setData({\n      'newPost.tag': tag\n    })\n  },\n  // 加载用户收藏"}