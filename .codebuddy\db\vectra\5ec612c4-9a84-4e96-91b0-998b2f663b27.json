{"chunk": 7, "numChunks": 18, "fileHash": "/DUygIsVyFRM4gmsdi8uiMJN8RFlyElKDKTgRRrPFCI=", "filePath": "utils/hehun/calculator.js", "content": "// 五行生克关系\nclass HehunCalculator {\n  calculateRelationScore() {\n    let score = 60 // 基础分\n    const maleElements = this.getBaziElements(this.maleBazi)\n    const femaleElements = this.getBaziElements(this.femaleBazi)\n\n    // 分析日柱关系\n    const maleRiWuxing = TIAN_GAN_WU_XING[maleElements.gan[2]]\n    const femaleRiWuxing = TIAN_GAN_WU_XING[femaleElements.gan[2]]\n\n    if (this.isHelpful(maleRiWuxing, femaleRiWuxing)) {\n      score += 20\n    }\n    if (this.isConflict(maleRiWuxing, femaleRiWuxing)) {\n      score -= 10\n    }\n\n    return Math.max(0, Math.min(100, score))\n  }\n  // 工具方法\n  getBaziElements(bazi) {\n    return {\n      gan: [bazi.year.gan, bazi.month.gan, bazi.day.gan, bazi.time.gan],\n      zhi: [bazi.year.zhi, bazi.month.zhi, bazi.day.zhi, bazi.time.zhi]\n    }\n  }"}