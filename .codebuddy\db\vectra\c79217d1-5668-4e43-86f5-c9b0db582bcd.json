{"chunk": 1, "numChunks": 9, "fileHash": "b5v2J+xqqZLB03zKt2qI4RvF0LmgPXZeserUsC/MOx0=", "filePath": "frontend-implementation/utils/wx.js", "content": "// 微信小程序API封装工具\nexport const showLoading = (options) => {\n  const defaultOptions = {\n    title: '加载中...',\n    mask: true\n  }\n  \n  wx.showLoading({\n    ...defaultOptions,\n    ...options\n  })\n}\n/**\n * 隐藏加载提示\n */\nexport const hideLoading = () => {\n  wx.hideLoading()\n}\n/**\n * 显示模态对话框\n * @param {Object} options 配置选项\n */\nexport const showModal = (options) => {\n  const defaultOptions = {\n    title: '提示',\n    content: '确定要执行此操作吗？',\n    showCancel: true,\n    cancelText: '取消',\n    confirmText: '确定',\n    cancelColor: '#000000',\n    confirmColor: '#576B95'\n  }\n  \n  return new Promise((resolve) => {\n    wx.showModal({\n      ...defaultOptions,\n      ...options,\n      success: (res) => {\n        resolve(res)\n        if (options.success) {\n          options.success(res)\n        }\n      },\n      fail: (error) => {\n        resolve({ confirm: false, cancel: true })\n        if (options.fail) {\n          options.fail(error)\n        }\n      }\n    })\n  })\n}\n/**\n * 显示操作菜单\n * @param {Object} options 配置选项\n */\nexport const showActionSheet = (options) => {\n  return new Promise((resolve, reject) => {\n    wx.showActionSheet({\n      ...options,\n      success: (res) => {\n        resolve(res)\n        if (options.success) {\n          options.success(res)\n        }\n      },\n      fail: (error) => {\n        reject(error)\n        if (options.fail) {\n          options.fail(error)\n        }\n      }\n    })\n  })\n}\n/**\n * 页面跳转\n * @param"}