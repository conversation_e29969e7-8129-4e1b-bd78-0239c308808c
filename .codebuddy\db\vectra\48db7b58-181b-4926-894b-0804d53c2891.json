{"chunk": 0, "numChunks": 6, "fileHash": "m/bcrIpornnqwv0iP6r9/jZF/qprhOPA5RFcfEHrukg=", "filePath": "pages/profile/profile.wxss", "content": "/* pages/profile/profile.wxss */\n.page-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f5f0ff 0%, #e8dff5 100%);\n  padding-bottom: calc(env(safe-area-inset-bottom) + 100rpx);\n}\n\n/* 顶部背景 */\n.profile-header {\n  position: relative;\n  height: 400rpx;\n  overflow: hidden;\n}\n\n.header-bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.header-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%);\n}\n\n/* 用户信息卡片 */\n.user-card {\n  position: absolute;\n  bottom: -40rpx;\n  left: 30rpx;\n  right: 30rpx;\n  background: white;\n  border-radius: 30rpx;\n  padding: 40rpx;\n  box-shadow: 0 10rpx 40rpx rgba(149, 117, 205, 0.15);\n}\n\n.user-main {\n  display: flex;\n  align-items: center;\n  margin-bottom: 40rpx;\n}\n\n.user-avatar {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 60rpx;\n  margin-right: 30rpx;\n  border: 6rpx solid #f5f0ff;\n"}