{"chunk": 2, "numChunks": 6, "fileHash": "8h8E6IyA2QKaLBRxCAXNE6RcQ2dvD/eV8dT/ZBdIuAw=", "filePath": "pages/marriage/marriage.wxml", "content": "          <text class=\"label\">出生日期</text>\n          <text class=\"required\">*</text>\n        </view>\n        <picker mode=\"date\" value=\"{{femaleInfo.birthDate}}\" bindchange=\"onFemaleDateChange\">\n          <view class=\"picker-wrapper\">\n            <text class=\"picker-text {{femaleInfo.birthDate ? 'selected' : 'placeholder'}}\">\n              {{femaleInfo.birthDate || '请选择出生日期'}}\n            </text>\n            <text class=\"picker-icon\">📅</text>\n          </view>\n        </picker>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">出生时间</text>\n          <text class=\"required\">*</text>\n        </view>\n        <picker mode=\"time\" value=\"{{femaleInfo.birthTime}}\" bindchange=\"onFemaleTimeChange\">\n          <view class=\"picker-wrapper\">\n            <text class=\"picker-text {{femaleInfo.birthTime ? 'selected' : 'placeholder'}}\">\n              {{femaleInfo.birthTime || '请选择出生时间'}}\n            </text>\n            <text class=\"picker-icon\">⏰</text>\n          </view>\n        </picker>\n      </view>\n    </view>\n\n    <!-- 提示信息 -->\n    <view class=\"tips-card\">\n      <view class=\"tips-header\">\n        <text class=\"tips-icon\">💡</text>\n        <text class=\"tips-title\">温馨提示</text>\n      </view>\n      <view class=\"tips-content\">\n        <text class=\"tip-item\">• 姓名和出生时间为必填项</text>\n        <text class=\"tip-item\">• 出生时间越精确，合婚结果越准确</text>\n"}