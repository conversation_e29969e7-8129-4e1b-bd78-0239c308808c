{"chunk": 4, "numChunks": 10, "fileHash": "zs0lx95Rie79XClG/B2xJnr9ZnTEU5TU8Cf7eq2kuOY=", "filePath": "frontend-implementation/pages/birth-info/index.js", "content": "// 出生信息页面\nPage({\n  initDateRanges() {\n    const currentYear = new Date().getFullYear()\n    const yearRange = []\n    for (let i = currentYear; i >= 1900; i--) {\n      yearRange.push(i)\n    }\n    \n    const monthRange = Array.from({ length: 12 }, (_, i) => i + 1)\n    const dayRange = Array.from({ length: 31 }, (_, i) => i + 1)\n    const hourRange = Array.from({ length: 24 }, (_, i) => i)\n    const minuteRange = Array.from({ length: 60 }, (_, i) => i)\n    \n    this.setData({\n      yearRange,\n      monthRange,\n      dayRange,\n      hourRange,\n      minuteRange\n    })\n  },\n  /**\n   * 加载出生信息\n   */\n  async loadBirthInfo() {\n    try {\n      showLoading({ title: '加载中...' })\n      const result = await getBirthInfo()\n      \n      if (result.status === 'success' && result.data.birth_info) {\n        this.setData({\n          formData: result.data.birth_info,\n          lunarInfo: result.data.birth_info.lunar_info,\n          baziInfo: result.data.birth_info.bazi,\n          wuxingInfo: result.data.birth_info.wuxing\n        })\n      }\n    } catch (error) {\n      console.error('加载出生信息失败:', error)\n      showToast({\n        title: '加载失败',\n        icon: 'none'\n      })\n    } finally {\n      hideLoading()\n    }\n  },\n  /**\n   * 表单输入处理\n   */\n  onInput(e) {\n    const { field } = e.currentTarget.dataset\n    const { value } = e.detail\n    \n    this.setData({\n      [`formData.${field}`]: value,\n      [`errors.${field}`]: null\n    })\n  },\n  /**\n   * 性"}