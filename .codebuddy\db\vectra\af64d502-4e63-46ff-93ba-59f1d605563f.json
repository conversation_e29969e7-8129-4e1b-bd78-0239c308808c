{"chunk": 9, "numChunks": 19, "fileHash": "PEPeuJm140RAD7VBd85WlnsfcWUdoorqVSQj8ISByl0=", "filePath": "pages/ai-chat/ai-chat.wxss", "content": "  transition: all 0.3s ease;\n}\n\n.send-btn.active {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);\n}\n\n.send-btn.disabled {\n  background: #e5e7eb;\n  color: #9ca3af;\n}\n\n.send-btn.active:active {\n  transform: scale(0.98);\n  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);\n}\n\n.btn-icon {\n  font-size: 28rpx;\n}\n\n.btn-text {\n  font-size: 30rpx;\n}\n\n/* 悬浮球 */\n.floating-ball {\n  width: 120rpx;\n  height: 120rpx;\n  position: relative;\n  cursor: pointer;\n}\n\n.ball-glow {\n  position: absolute;\n  top: -20rpx;\n  left: -20rpx;\n  right: -20rpx;\n  bottom: -20rpx;\n  background: radial-gradient(circle, rgba(121, 40, 202, 0.3) 0%, transparent 70%);\n  border-radius: 50%;\n  animation: glow-pulse 3s ease-in-out infinite;\n}\n\n.ball-content {\n  width: 120rpx;\n  height: 120rpx;\n  background: linear-gradient(145deg, #7928CA, #9b4dca);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow:\n    0 8rpx 20rpx rgba(123, 40, 202, 0.25),\n"}