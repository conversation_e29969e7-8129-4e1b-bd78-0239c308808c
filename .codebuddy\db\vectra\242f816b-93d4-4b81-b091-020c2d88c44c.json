{"chunk": 20, "numChunks": 22, "fileHash": "jr30LZOMc1P5VIpMptyaGnan9xv44m/c09rHA5aMeTY=", "filePath": "pages/index/index.js", "content": "// index.js\nPage({\n  async handleSignIn() {\n    try {\n      const result = await signInManager.signIn();\n      if (result.success) {\n        this.setData({\n          signInStatus: true\n        });\n        \n        // 构建奖励提示信息\n        let message = `签到成功！获得${result.pointsEarned}积分\\n`;\n        if (result.rewards && result.rewards.length > 1) {\n          message += '\\n获得奖励：\\n';\n          result.rewards.forEach(reward => {\n            message += `${reward.description}：+${reward.points}积分\\n`;\n          });\n        }\n        message += `\\n连续签到：${result.consecutiveDays}天`;\n        message += `\\n本月签到：${result.monthlyDays}天`;\n        \n        wx.showModal({\n          title: '签到成功',\n          content: message,\n          showCancel: false,\n          confirmText: '我知道了',\n          success: () => {\n            // 更新页面数据\n            this.checkSignIn();\n          }\n        });\n      } else {\n        wx.showToast({\n          title: result.message,\n          icon: 'none'\n        });\n      }\n    } catch (error) {\n      console.error('签到失败:', error);\n      wx.showToast({\n        title: '签到失败，请重试',\n        icon: 'none'\n      });\n    }\n  },\n  // 轮播图点击事件\n  onSwiperTap(e) {\n    const { url } = e.currentTarget.dataset\n    this.navigateTo(url)\n  },\n  // 导航项点击事件\n  onNavTap(e) {\n    const { url } = e.currentTarget.dataset\n    this.navigateTo(url)\n  },\n  // 分类项目点击\n  onCategoryTap(e) {\n    const url = e.currentTarget.dataset.url\n    this.navigateTo(url)\n"}