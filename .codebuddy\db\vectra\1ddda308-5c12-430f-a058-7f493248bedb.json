{"chunk": 3, "numChunks": 5, "fileHash": "kj9oZjkyC/FHxSTFwX241qsgDqE06+EB/MIRUDnQNTM=", "filePath": "pages/login/login.wxml", "content": "  </view>\n\n  <!-- 加载状态 -->\n  <view class=\"loading-overlay\" wx:if=\"{{isLoading}}\">\n    <view class=\"loading-content\">\n      <view class=\"loading-spinner\"></view>\n      <text class=\"loading-text\">{{loadingText}}</text>\n    </view>\n  </view>\n\n  <!-- 登录成功提示 -->\n  <view class=\"success-modal\" wx:if=\"{{showSuccessModal}}\">\n    <view class=\"modal-mask\"></view>\n    <view class=\"modal-content\">\n      <view class=\"success-animation\">\n        <view class=\"success-circle\">\n          <view class=\"success-checkmark\">✓</view>\n        </view>\n      </view>\n      <view class=\"success-title\">登录成功</view>\n      <view class=\"success-desc\">\n        欢迎来到恒琦易道，{{userInfo.nickName}}\n      </view>\n      <view class=\"success-rewards\" wx:if=\"{{loginRewards.length > 0}}\">\n        <text class=\"rewards-title\">登录奖励</text>\n        <view class=\"rewards-list\">\n          <view class=\"reward-item\" wx:for=\"{{loginRewards}}\" wx:key=\"type\">\n            <view class=\"reward-icon\">{{item.icon}}</view>\n            <text class=\"reward-text\">{{item.text}}</text>\n          </view>\n        </view>\n      </view>\n      <button class=\"enter-app-btn\" bindtap=\"enterApp\">进入应用</button>\n    </view>\n  </view>\n\n  <!-- 权限说明弹窗 -->\n  <view class=\"permission-modal\" wx:if=\"{{showPermissionModal}}\">\n    <view class=\"modal-mask\" bindtap=\"hidePermissionModal\"></view>\n    <view class=\"modal-content\">\n      <view class=\"modal-header\">\n        <text>权限说明</text>\n"}