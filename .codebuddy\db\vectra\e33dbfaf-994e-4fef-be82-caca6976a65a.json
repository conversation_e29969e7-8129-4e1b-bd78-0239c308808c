{"chunk": 6, "numChunks": 38, "fileHash": "DpyinnhZJO6jrs0/VY0BXwrNjORlEQ1BeRywBJybhxQ=", "filePath": "pages/ai-chat/ai-chat.js", "content": "const app = getApp()\nPage({\n  getWelcomeMessage() {\n    if (this.data.isLoggedIn) {\n      return '🔮 欢迎回到恒琦易道！我是你的玄学AI助手，可以为你提供：\\n\\n• 八字命理分析\\n• 易经卦象解读\\n• 风水布局建议\\n• 五行运势分析\\n• 日常占卜咨询\\n\\n请直接说出你想了解的内容，或者点击下方快捷功能开始吧！'\n    } else {\n      return '✨ 你好！我是恒琦易道的AI助手。为了提供更精准的命理分析，建议先登录获取完整功能。\\n\\n当前可以为你提供基础的玄学咨询，如需个性化分析请先登录。'\n    }\n  },\n  /**\n   * 检查登录状态\n   */\n  async checkLoginStatus() {\n    const isLoggedIn = apiService.isLoggedIn()\n    this.setData({ isLoggedIn })\n\n    if (isLoggedIn) {\n      try {\n        const userInfo = wx.getStorageSync('user_info')\n        this.setData({ userInfo })\n      } catch (error) {\n        console.warn('获取用户信息失败:', error)\n      }\n    }\n  },\n  /**\n   * 微信登录\n   */"}