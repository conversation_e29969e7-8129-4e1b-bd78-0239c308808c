{"chunk": 11, "numChunks": 12, "fileHash": "Le/FlEMr7xpFlnsEezDNSUltFPN5XSRHUvE0NpcG53I=", "filePath": "frontend-implementation/pages/ai-chat/index.js", "content": "// AI聊天页面\nPage({\n  async onMessageFeedback(e) {\n    const { messageId, type } = e.currentTarget.dataset\n    \n    try {\n      await submitMessageFeedback(messageId, {\n        feedback_type: type,\n        rating: type === 'like' ? 5 : 1\n      })\n      \n      showToast({\n        title: '反馈成功',\n        icon: 'success'\n      })\n    } catch (error) {\n      console.error('提交反馈失败:', error)\n    }\n  },\n  /**\n   * 长按消息\n   */\n  onMessageLongPress(e) {\n    const { message } = e.currentTarget.dataset\n    \n    wx.showActionSheet({\n      itemList: ['复制', '删除'],\n      success: (res) => {\n        if (res.tapIndex === 0) {\n          // 复制消息\n          wx.setClipboardData({\n            data: message.content,\n            success: () => {\n              showToast({\n                title: '已复制',\n                icon: 'success'\n              })\n            }\n          })\n        } else if (res.tapIndex === 1) {\n          // 删除消息\n          this.deleteMessage(message.id)\n        }\n      }\n    })\n  },\n  /**\n   * 删除消息\n   */\n  deleteMessage(messageId) {\n    showModal({\n      title: '确认删除',\n      content: '确定要删除这条消息吗？',\n      success: (res) => {\n        if (res.confirm) {\n          const messages = this.data.messages.filter(msg => msg.id !== messageId)\n          this.setData({ messages })\n        }\n      }\n    })\n  },\n  /**\n   * 清空聊天记录\n   */"}