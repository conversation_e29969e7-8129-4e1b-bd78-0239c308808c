{"chunk": 0, "numChunks": 9, "fileHash": "u79DhtWBcqv2jyfo/kouN6jRcunleYVqTQDoY+P3gFo=", "filePath": "pages/bazi/bazi.wxss", "content": "page {\n  background: linear-gradient(135deg, #f5f0ff 0%, #e8dff5 100%);\n}\n\n.container {\n  min-height: 100vh;\n  padding: 20rpx;\n  padding-bottom: 40rpx;\n}\n\n/* 输入区域样式 */\n.input-section {\n  background-color: var(--primary-color);\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 4rpx 12rpx var(--shadow-color);\n}\n\n.section-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n  margin: 40rpx 0 25rpx;\n  padding-left: 20rpx;\n  position: relative;\n}\n\n.section-title::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 6rpx;\n  height: 70%;\n  background: linear-gradient(180deg, #9575cd 0%, #7e57c2 100%);\n  border-radius: 3rpx;\n}\n\n.input-group {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.input-item {\n  display: flex;\n  flex-direction: column;\n  gap: 10rpx;\n}\n\n.label {\n  font-size: 28rpx;\n  color: var(--text-secondary);\n}\n\n.picker-value {\n  height: 80rpx;\n  line-height: 80rpx;\n  padding: 0 20rpx;\n  background-color: var(--primary-lightest);\n  border-radius: 8rpx;\n  font-size: 28rpx;\n  color: var(--text-primary);\n}\n\n.radio-label {\n"}