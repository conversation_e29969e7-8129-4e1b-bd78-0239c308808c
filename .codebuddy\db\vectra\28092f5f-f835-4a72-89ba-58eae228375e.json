{"chunk": 3, "numChunks": 6, "fileHash": "gjgKY958lwZRjwR5WBqAv14luMAhHXf3C7kpKqAQJe0=", "filePath": "pages/yijing/yijing.js", "content": "// yijing.js\nPage({\n  data: {\n    question: '',\n    name: '',\n    gender: '男',\n    loading: false,\n    result: null,\n    birthInfo: null\n  },\n  onLoad() {\n    // 检查是否有出生信息\n    if (!checkBirthInfo()) {\n      navigateToBirthInfo('/pages/yijing/yijing')\n      return\n    }\n    \n    // 从本地存储读取出生信息\n    this.loadBirthInfo()\n  },\n  onShow() {\n    // 每次页面显示时重新加载出生信息，确保数据是最新的\n    this.loadBirthInfo()\n  },\n  // 加载出生信息\n  loadBirthInfo() {\n    // 使用工具函数获取出生信息，确保即使某些字段缺失也能正常显示\n    const birthInfo = getBirthInfo()\n    this.setData({\n      name: birthInfo.name,\n      gender: birthInfo.gender,\n      birthInfo: birthInfo\n    })\n  },\n  // 处理问题输入\n  onQuestionInput(e) {\n    this.setData({\n      question: e.detail.value\n    })\n  },"}