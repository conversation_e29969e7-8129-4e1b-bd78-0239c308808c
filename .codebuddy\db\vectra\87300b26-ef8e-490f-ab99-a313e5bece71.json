{"chunk": 2, "numChunks": 6, "fileHash": "3s34je0qL5+ULluXZdiBSih2mwyLkPtuCPPtZ9V/C7o=", "filePath": "pages/community/community.wxml", "content": "          <view class=\"post-title\">{{item.title || ''}}</view>\n          <view class=\"post-text\">{{item.content || ''}}</view>\n          <!-- 话题标签 -->\n          <view class=\"post-tags\" wx:if=\"{{item.tags && item.tags.length > 0}}\">\n            <text class=\"tag\" wx:for=\"{{item.tags}}\" wx:key=\"*this\" wx:for-item=\"tag\">#{{tag}}</text>\n          </view>\n          <view class=\"post-images\" wx:if=\"{{item.images && item.images.length > 0}}\">\n            <image \n              wx:for=\"{{item.images}}\" \n              wx:key=\"*this\" \n              wx:for-item=\"image\"\n              src=\"{{image}}\" \n              mode=\"aspectFill\"\n              bindtap=\"previewImage\"\n              data-urls=\"{{item.images}}\"\n              data-current=\"{{image}}\"\n            ></image>\n          </view>\n        </view>\n        <view class=\"post-footer\">\n          <view class=\"post-actions\">\n            <view class=\"action-btn {{item.isLiked ? 'liked' : ''}}\" bindtap=\"toggleLike\" data-id=\"{{item.id}}\" catchtap=\"true\">\n              <icon type=\"{{item.isLiked ? 'success' : 'success_no_circle'}}\" size=\"16\"></icon>\n              <text>{{item.likes || 0}}</text>\n            </view>\n            <view class=\"action-btn\" bindtap=\"viewPost\" data-id=\"{{item.id}}\">\n              <icon type=\"info_circle\" size=\"16\"></icon>\n              <text>{{item.comments || 0}}</text>\n            </view>\n            <view class=\"action-btn {{item.isCollected ? 'collected' : ''}}\" bindtap=\"toggleCollect\" data-id=\"{{item.id}}\" catchtap=\"true\">\n              <icon type=\"{{item.isCollected ? 'success' : 'success_no_circle'}}\" size=\"16\"></icon>\n"}