{"chunk": 10, "numChunks": 11, "fileHash": "6cvegKFbV+qpODqHzHmmaj3L2MYUXg3Sv6AOEWns8A8=", "filePath": "frontend-implementation/mixins/basePage.js", "content": "// 页面基础混入\nexport const basePageMixin = {\n  formatRelativeTime(date) {\n    const now = new Date()\n    const target = new Date(date)\n    const diff = now - target\n    \n    const minute = 60 * 1000\n    const hour = 60 * minute\n    const day = 24 * hour\n    const week = 7 * day\n    const month = 30 * day\n    \n    if (diff < minute) {\n      return '刚刚'\n    } else if (diff < hour) {\n      return `${Math.floor(diff / minute)}分钟前`\n    } else if (diff < day) {\n      return `${Math.floor(diff / hour)}小时前`\n    } else if (diff < week) {\n      return `${Math.floor(diff / day)}天前`\n    } else if (diff < month) {\n      return `${Math.floor(diff / week)}周前`\n    } else {\n      return this.formatDate(date, 'YYYY-MM-DD')\n    }\n  },\n  /**\n   * 数字格式化\n   * @param {number} num 数字\n   * @param {number} precision 精度\n   */\n  formatNumber(num, precision = 0) {\n    if (typeof num !== 'number') {\n      return num\n    }\n    \n    return num.toFixed(precision).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')\n  },\n  /**\n   * 分享配置\n   */\n  onShareAppMessage() {\n    return {\n      title: this.shareTitle || '卦里乾坤 - 专业命理分析',\n      path: this.sharePath || '/pages/index/index',\n      imageUrl: this.shareImageUrl || '/images/share-default.png'\n    }\n  },\n  /**\n   * 分享到朋友圈\n   */"}