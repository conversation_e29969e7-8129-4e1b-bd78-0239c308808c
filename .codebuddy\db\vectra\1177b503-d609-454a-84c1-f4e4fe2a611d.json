{"chunk": 2, "numChunks": 6, "fileHash": "dNwun7ERRMYazRQ1UOKQO3ggF1ZH6El+O2/hG+Rh3/o=", "filePath": "pages/chat-list/chat-list.wxml", "content": "          wx:for=\"{{filteredChats}}\"\n          wx:key=\"id\"\n          bindtap=\"openChat\"\n          data-id=\"{{item.id}}\"\n          bindlongpress=\"showChatOptions\"\n          data-chat=\"{{item}}\"\n        >\n          <view class=\"chat-avatar\">\n            <image src=\"{{item.avatar || '/assets/icons/ai-avatar.png'}}\" mode=\"aspectFill\" />\n            <view class=\"chat-status {{item.status}}\"></view>\n            <view class=\"chat-type-badge\">{{item.chatTypeIcon}}</view>\n          </view>\n          <view class=\"chat-content\">\n            <view class=\"chat-header\">\n              <text class=\"chat-title\">{{item.title}}</text>\n              <text class=\"chat-time\">{{item.lastMessageTime}}</text>\n            </view>\n            <view class=\"chat-preview\">\n              <text class=\"last-message\">{{item.lastMessage}}</text>\n              <view class=\"chat-badges\">\n                <view class=\"unread-badge\" wx:if=\"{{item.unreadCount > 0}}\">{{item.unreadCount}}</view>\n                <view class=\"favorite-icon\" wx:if=\"{{item.isFavorite}}\">⭐</view>\n              </view>\n            </view>\n          </view>\n          <view class=\"chat-actions\" wx:if=\"{{item.showActions}}\">\n            <view class=\"action-btn pin\" bindtap=\"togglePin\" data-id=\"{{item.id}}\">\n              {{item.isPinned ? '取消置顶' : '置顶'}}\n            </view>\n            <view class=\"action-btn delete\" bindtap=\"deleteChat\" data-id=\"{{item.id}}\">删除</view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 空状态 -->\n      <view class=\"empty-state\" wx:if=\"{{filteredChats.length === 0 && !isLoading}}\">\n        <view class=\"empty-icon\">💭</view>\n"}