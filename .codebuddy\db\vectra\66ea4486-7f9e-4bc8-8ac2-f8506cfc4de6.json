{"chunk": 0, "numChunks": 5, "fileHash": "gwytGpkD8JZ0FXKWaXVx5+UFLQiWHNueoakBC5jWArU=", "filePath": "pages/settings/index.wxml", "content": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<view class=\"container\">\n  <!-- 通用设置 -->\n  <view class=\"section\">\n    <view class=\"section-title\">通用设置</view>\n    <view class=\"setting-list\">\n      <!-- 深色模式 -->\n      <view class=\"setting-item\">\n        <view class=\"setting-left\">\n          <image class=\"setting-icon\" src=\"/assets/icons/dark-mode.png\"/>\n          <text class=\"setting-name\">深色模式</text>\n        </view>\n        <switch \n          checked=\"{{generalSettings.darkMode}}\"\n          bindchange=\"toggleSetting\"\n          data-type=\"general\"\n          data-key=\"darkMode\"\n        />\n      </view>\n\n      <!-- 语言设置 -->\n      <view class=\"setting-item\">\n        <view class=\"setting-left\">\n          <image class=\"setting-icon\" src=\"/assets/icons/language.png\"/>\n          <text class=\"setting-name\">语言</text>\n        </view>\n        <picker \n          mode=\"selector\" \n          range=\"{{languageOptions}}\" \n          range-key=\"label\"\n          value=\"{{languageOptions.findIndex(item => item.value === generalSettings.language)}}\"\n          bindchange=\"changeLanguage\"\n        >\n          <view class=\"picker-text\">\n            {{languageOptions.find(item => item.value === generalSettings.language).label}}\n          </view>\n        </picker>\n      </view>\n\n      <!-- 字体大小 -->\n      <view class=\"setting-item\">\n        <view class=\"setting-left\">\n          <image class=\"setting-icon\" src=\"/assets/icons/font-size.png\"/>\n          <text class=\"setting-name\">字体大小</text>\n        </view>\n        <picker \n          mode=\"selector\" \n          range=\"{{fontSizeOptions}}\" \n          range-key=\"label\"\n          value=\"{{fontSizeOptions.findIndex(item => item.value === generalSettings.fontSize)}}\"\n          bindchange=\"changeFontSize\"\n        >\n"}