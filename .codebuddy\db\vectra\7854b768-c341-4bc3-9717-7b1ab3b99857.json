{"chunk": 9, "numChunks": 14, "fileHash": "3g+f4UKfyPQOeFOGstuRr+TvNVx6NqSI5ZfRnXdVunU=", "filePath": "pages/profile/profile.js", "content": "// pages/profile/profile.js\nPage({\n  logout() {\n    wx.showModal({\n      title: '提示',\n      content: '确定要退出登录吗？',\n      success: (res) => {\n        if (res.confirm) {\n          // 使用全局状态管理器退出登录\n          globalState.logout()\n\n          // 重置页面数据\n          this.setData({\n            userInfo: null,\n            hasUserInfo: false,\n            points: 0,\n            level: 1,\n            userStats: {\n              posts: 0,\n              followers: 0,\n              following: 0\n            }\n          })\n\n          wx.showToast({\n            title: '已退出登录',\n            icon: 'success'\n          })\n\n          // 返回首页\n          setTimeout(() => {\n            wx.switchTab({\n              url: '/pages/index/index'\n            })\n          }, 1500)\n        }\n      }\n    })\n  },\n  /**\n   * 页面卸载时清理监听器\n   */\n  onUnload() {\n    // 清理状态监听器\n    if (this.userInfoListener) this.userInfoListener()\n    if (this.pointsListener) this.pointsListener()\n    if (this.birthInfoListener) this.birthInfoListener()\n    if (this.signInListener) this.signInListener()\n  },"}