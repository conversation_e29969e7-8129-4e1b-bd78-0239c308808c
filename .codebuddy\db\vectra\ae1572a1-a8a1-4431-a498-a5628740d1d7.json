{"chunk": 0, "numChunks": 6, "fileHash": "8h8E6IyA2QKaLBRxCAXNE6RcQ2dvD/eV8dT/ZBdIuAw=", "filePath": "pages/marriage/marriage.wxml", "content": "<!--pages/marriage/marriage.wxml-->\n<view class=\"container\">\n  <!-- 顶部标题区域 -->\n  <view class=\"header\">\n    <view class=\"header-content\">\n      <view class=\"title\">八字合婚</view>\n      <view class=\"subtitle\">测算两人姻缘匹配度，探寻天作之合</view>\n    </view>\n    <view class=\"header-decoration\">\n      <text class=\"icon\">💕</text>\n    </view>\n  </view>\n\n  <!-- 表单区域 -->\n  <view class=\"form-container\">\n    <!-- 男方信息卡片 -->\n    <view class=\"info-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">男方信息</text>\n        <text class=\"card-icon\">👨</text>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">姓名</text>\n          <text class=\"required\">*</text>\n        </view>\n        <view class=\"input-wrapper\">\n          <input\n            class=\"input\"\n            placeholder=\"请输入男方姓名\"\n            bindinput=\"onMaleNameInput\"\n            value=\"{{maleInfo.name}}\"\n            maxlength=\"20\"\n            placeholder-class=\"input-placeholder\"\n          />\n          <text class=\"input-icon\">✏️</text>\n        </view>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">出生日期</text>\n          <text class=\"required\">*</text>\n        </view>\n        <picker mode=\"date\" value=\"{{maleInfo.birthDate}}\" bindchange=\"onMaleDateChange\">\n          <view class=\"picker-wrapper\">\n"}