{"chunk": 4, "numChunks": 5, "fileHash": "4lpNnoqGg1eEPtIgkdLgszOng5dzR4kuUtSZ8HRwytk=", "filePath": "pages/yijing/yijing.wxss", "content": "  margin: 30rpx 0;\n  padding: 20rpx 0;\n}\n\n.birth-info {\n  background-color: var(--primary-lightest);\n  border-radius: 12rpx;\n  padding: 20rpx;\n  margin-bottom: 30rpx;\n  border: 2rpx solid var(--border-color);\n}\n\n.info-title {\n  font-size: 28rpx;\n  color: var(--primary-color);\n  font-weight: bold;\n  margin-bottom: 16rpx;\n}\n\n.info-content {\n  display: flex;\n  flex-direction: column;\n  gap: 12rpx;\n}\n\n.info-item {\n  font-size: 26rpx;\n  color: var(--text-secondary);\n}\n\n.submit-btn {\n  margin-top: 40rpx;\n  background-color: var(--primary-color);\n  color: #FFFFFF;\n  font-size: 30rpx;\n  padding: 20rpx 0;\n  border-radius: 8rpx;\n  box-shadow: 0 4rpx 8rpx var(--shadow-color);\n  border: none;\n  width: 100%;\n  font-weight: bold;\n  letter-spacing: 2rpx;\n  transition: all 0.3s ease;\n}\n\n.submit-btn:active {\n  opacity: 0.8;\n  transform: translateY(2rpx);\n  box-shadow: 0 2rpx 4rpx var(--shadow-color);\n} \n"}