{"chunk": 4, "numChunks": 7, "fileHash": "RAGdlTZI62FtOLCwB83HuJtgEeQmh/OOiLgGD1MsfmA=", "filePath": "frontend-implementation/utils/request.js", "content": "// 网络请求工具\nclass RequestInterceptor {\n  clearAuthAndRedirect() {\n    removeStorageSync('token')\n    removeStorageSync('refreshToken')\n    removeStorageSync('userInfo')\n    \n    showToast({\n      title: '登录已过期，请重新登录',\n      icon: 'none'\n    })\n\n    setTimeout(() => {\n      wx.reLaunch({\n        url: '/pages/login/index'\n      })\n    }, 1500)\n  }\n  /**\n   * 发起请求\n   */\n  async request(options) {\n    // 生成请求ID用于去重\n    const requestId = this.generateRequestId(options)\n    \n    // 检查是否有相同的请求正在进行\n    if (this.requestQueue.has(requestId)) {\n      return this.requestQueue.get(requestId)\n    }\n\n    // 添加基础配置\n    const requestOptions = {\n      url: `${BASE_URL}${options.url}`,\n      method: options.method || 'GET',\n      data: options.data,\n      header: {\n        'Content-Type': 'application/json',\n        ...options.header\n      },\n      timeout: options.timeout || TIMEOUT,\n      ...options\n    }\n\n    // 添加认证头\n    this.addAuthHeader(requestOptions)\n\n    // 创建请求Promise\n    const requestPromise = new Promise((resolve, reject) => {\n      wx.request({\n        ...requestOptions,\n        success: async (response) => {\n          try {\n            const result = await this.handleResponse(response, options)\n            resolve(result)\n          } catch (error) {\n            reject(error)\n          }\n        },\n        fail: (error) => {\n          console.error('请求失败:', error)\n          reject(new Error(error.errMsg || '网络请求失败'))\n        },\n        complete: () => {\n          // 请求完成后从队列中移除\n          this.requestQueue.delete(requestId)\n        }\n      })\n    })\n\n    // 将请求添加到队列\n    this"}