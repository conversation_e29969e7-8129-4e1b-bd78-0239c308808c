{"chunk": 4, "numChunks": 6, "fileHash": "YXf85xx1jxmB9as0Va5OH90PaK79Xnt82sfEgjKj2bE=", "filePath": "pages/post-detail/post-detail.wxss", "content": "  flex-direction: column;\n  gap: 25rpx;\n}\n\n.comment-item {\n  display: flex;\n  background: white;\n  padding: 25rpx;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n.comment-avatar {\n  width: 70rpx;\n  height: 70rpx;\n  border-radius: 50%;\n  margin-right: 20rpx;\n  flex-shrink: 0;\n}\n\n.comment-content {\n  flex: 1;\n}\n\n.comment-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15rpx;\n}\n\n.comment-author {\n  font-size: 30rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n.comment-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.comment-text {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n  margin-bottom: 15rpx;\n}\n\n.comment-actions {\n  display: flex;\n  gap: 30rpx;\n}\n\n.action-btn {\n  font-size: 26rpx;\n  color: #999;\n  padding: 8rpx 16rpx;\n  transition: all 0.3s ease;\n}\n\n.action-btn:active {\n  color: #9575cd;\n}\n\n.load-more {\n  text-align: center;\n  padding: 30rpx;\n  font-size: 28rpx;\n  color: #999;\n}\n\n/* 底部评论输入 */\n.comment-input-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  display: flex;\n  padding: 20rpx 30rpx;\n"}