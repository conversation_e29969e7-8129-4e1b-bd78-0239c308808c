{"chunk": 1, "numChunks": 6, "fileHash": "8h8E6IyA2QKaLBRxCAXNE6RcQ2dvD/eV8dT/ZBdIuAw=", "filePath": "pages/marriage/marriage.wxml", "content": "            <text class=\"picker-text {{maleInfo.birthDate ? 'selected' : 'placeholder'}}\">\n              {{maleInfo.birthDate || '请选择出生日期'}}\n            </text>\n            <text class=\"picker-icon\">📅</text>\n          </view>\n        </picker>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">出生时间</text>\n          <text class=\"required\">*</text>\n        </view>\n        <picker mode=\"time\" value=\"{{maleInfo.birthTime}}\" bindchange=\"onMaleTimeChange\">\n          <view class=\"picker-wrapper\">\n            <text class=\"picker-text {{maleInfo.birthTime ? 'selected' : 'placeholder'}}\">\n              {{maleInfo.birthTime || '请选择出生时间'}}\n            </text>\n            <text class=\"picker-icon\">⏰</text>\n          </view>\n        </picker>\n      </view>\n    </view>\n\n    <!-- 女方信息卡片 -->\n    <view class=\"info-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">女方信息</text>\n        <text class=\"card-icon\">👩</text>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">姓名</text>\n          <text class=\"required\">*</text>\n        </view>\n        <view class=\"input-wrapper\">\n          <input\n            class=\"input\"\n            placeholder=\"请输入女方姓名\"\n            bindinput=\"onFemaleNameInput\"\n            value=\"{{femaleInfo.name}}\"\n            maxlength=\"20\"\n            placeholder-class=\"input-placeholder\"\n          />\n          <text class=\"input-icon\">✏️</text>\n        </view>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n"}