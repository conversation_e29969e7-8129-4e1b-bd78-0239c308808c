{"chunk": 1, "numChunks": 6, "fileHash": "VToXv7ATbmi0am+Amh5z0qVhRJK7IjhEVx3t5LgFVgM=", "filePath": "pages/marriage/index.wxml", "content": "        <picker mode=\"time\" value=\"{{male.time}}\" bindchange=\"bindMaleTimeChange\">\n          <view class=\"picker-wrapper\">\n            <text class=\"picker-text {{male.time ? 'selected' : 'placeholder'}}\">\n              {{male.time || '请选择出生时间'}}\n            </text>\n            <text class=\"picker-icon\">⏰</text>\n          </view>\n        </picker>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">出生地点</text>\n          <text class=\"optional\">选填</text>\n        </view>\n        <view class=\"picker-wrapper\" bindtap=\"chooseMaleLocation\">\n          <text class=\"picker-text {{male.location ? 'selected' : 'placeholder'}}\">\n            {{male.location || '点击选择出生地点'}}\n          </text>\n          <text class=\"picker-icon\">📍</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 女方信息卡片 -->\n    <view class=\"info-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">女方信息</text>\n        <text class=\"card-icon\">👩</text>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">出生日期</text>\n          <text class=\"required\">*</text>\n        </view>\n        <picker mode=\"date\" value=\"{{female.date}}\" bindchange=\"bindFemaleDateChange\">\n          <view class=\"picker-wrapper\">\n            <text class=\"picker-text {{female.date ? 'selected' : 'placeholder'}}\">\n              {{female.date || '请选择出生日期'}}\n            </text>\n            <text class=\"picker-icon\">📅</text>\n          </view>\n        </picker>\n      </view>\n\n"}