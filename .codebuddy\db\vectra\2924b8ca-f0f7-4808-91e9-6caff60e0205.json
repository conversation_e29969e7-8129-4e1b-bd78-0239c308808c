{"chunk": 6, "numChunks": 11, "fileHash": "iGKX2KeXQCBpUHYCjVhl7Obd6FTaj6FiHotsH10wGkE=", "filePath": "frontend-implementation/pages/analysis/index.js", "content": "// 命理分析页面\nPage({\n  },\n  onLoad(options) {\n    const { type } = options\n    \n    if (!type || !this.data.analysisTypes[type]) {\n      showToast({\n        title: '分析类型错误',\n        icon: 'none'\n      })\n      wx.navigateBack()\n      return\n    }\n    \n    this.setData({\n      analysisType: type,\n      analysisConfig: this.data.analysisTypes[type]\n    })\n    \n    // 检查是否需要出生信息\n    if (this.data.analysisConfig.requiresBirthInfo) {\n      this.checkBirthInfo()\n    }\n  },\n  /**\n   * 检查出生信息\n   */\n  async checkBirthInfo() {\n    try {\n      this.setData({ loading: true })\n      \n      const result = await getBirthInfo()\n      \n      if (result.status === 'success' && result.data.birth_info) {\n        this.setData({\n          birthInfo: result.data.birth_info,\n          hasBirthInfo: true\n        })\n      } else {\n        this.setData({ hasBirthInfo: false })\n        this.showBirthInfoRequired()\n      }\n    } catch (error) {\n      console.error('获取出生信息失败:', error)\n      this.setData({ hasBirthInfo: false })\n      this.showBirthInfoRequired()\n    } finally {\n      this.setData({ loading: false })\n    }\n  },\n  /**\n   * 显示需要出生信息提示\n   */\n  showBirthInfoRequired() {\n    showModal({\n      title: '需要出生信息',\n      content: '此分析需要您的出生信息，是否前往完善？',\n      confirmText: '去完善',\n      success: (res) => {\n        if (res.confirm) {\n          wx.navigateTo({\n            url: '/pages/birth-info/index'\n          })\n        } else {\n          wx.navigateBack()\n        }\n      }\n    })\n  },"}