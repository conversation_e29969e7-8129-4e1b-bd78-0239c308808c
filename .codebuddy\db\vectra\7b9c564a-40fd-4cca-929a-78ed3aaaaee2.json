{"chunk": 0, "numChunks": 3, "fileHash": "+UnDZ2Oz2YO43xAn0K/rzwUpkljb+ipIs7Av95FRNE8=", "filePath": "pages/community/index.wxml", "content": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<view class=\"container\">\n  <!-- 分类导航 -->\n  <scroll-view class=\"category-nav\" scroll-x enable-flex>\n    <view \n      class=\"category-item {{currentCategory === item.id ? 'active' : ''}}\"\n      wx:for=\"{{categories}}\"\n      wx:key=\"id\"\n      bindtap=\"switchCategory\"\n      data-category=\"{{item.id}}\"\n    >\n      {{item.name}}\n    </view>\n  </scroll-view>\n\n  <!-- 帖子列表 -->\n  <scroll-view class=\"post-list\" scroll-y enable-flex>\n    <view class=\"post-item\" wx:for=\"{{posts}}\" wx:key=\"_id\">\n      <!-- 用户信息 -->\n      <view class=\"post-header\">\n        <image class=\"user-avatar\" src=\"{{item.author.avatarUrl}}\" mode=\"aspectFill\"/>\n        <view class=\"user-info\">\n          <text class=\"username\">{{item.author.nickName}}</text>\n          <text class=\"post-time\">{{item.createTime}}</text>\n        </view>\n      </view>\n\n      <!-- 帖子内容 -->\n      <view class=\"post-content\" bindtap=\"handleComment\" data-post-id=\"{{item._id}}\">\n        <text class=\"content-text\">{{item.content}}</text>\n        <!-- 图片列表 -->\n        <view class=\"image-list\" wx:if=\"{{item.images && item.images.length > 0}}\">\n          <image \n            class=\"post-image {{item.images.length === 1 ? 'single' : ''}}\" \n            wx:for=\"{{item.images}}\" \n            wx:key=\"*this\"\n            wx:for-item=\"image\"\n            src=\"{{image}}\"\n            mode=\"aspectFill\"\n          />\n        </view>\n      </view>\n\n      <!-- 互动栏 -->\n      <view class=\"interaction-bar\">\n        <view \n"}