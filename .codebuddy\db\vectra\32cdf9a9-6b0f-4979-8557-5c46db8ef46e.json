{"chunk": 1, "numChunks": 6, "fileHash": "IwGBwJ25Blubhn4hCt83o/e9MDZEM6l3SvRIm1A0vrE=", "filePath": "pages/fengshui/fengshui.wxml", "content": "        <picker bindchange=\"onHouseTypeChange\" value=\"{{houseTypeIndex}}\" range=\"{{houseTypes}}\">\n          <view class=\"picker-wrapper\">\n            <text class=\"picker-text {{houseTypes[houseTypeIndex] ? 'selected' : 'placeholder'}}\">\n              {{houseTypes[houseTypeIndex] || '请选择房屋类型'}}\n            </text>\n            <text class=\"picker-icon\">🏢</text>\n          </view>\n        </picker>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">建筑年份</text>\n          <text class=\"required\">*</text>\n        </view>\n        <picker mode=\"date\" fields=\"year\" value=\"{{buildYear}}\" bindchange=\"onBuildYearChange\">\n          <view class=\"picker-wrapper\">\n            <text class=\"picker-text {{buildYear ? 'selected' : 'placeholder'}}\">\n              {{buildYear || '请选择建筑年份'}}\n            </text>\n            <text class=\"picker-icon\">📅</text>\n          </view>\n        </picker>\n      </view>\n    </view>\n\n    <!-- 户型信息卡片 -->\n    <view class=\"info-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">户型信息</text>\n        <text class=\"card-icon\">📐</text>\n  </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">房间数量</text>\n          <text class=\"required\">*</text>\n        </view>\n        <view class=\"room-counter\">\n          <view class=\"counter-btn {{rooms <= 1 ? 'disabled' : ''}}\" bindtap=\"decreaseRooms\">-</view>\n          <text class=\"counter-value\">{{rooms}}间</text>\n"}