{"chunk": 3, "numChunks": 10, "fileHash": "lg9EuzAhq20Uogzg9r07DedW1yISmqoEvsYKCcdN8GI=", "filePath": "utils/fengshui/calculator.js", "content": "// 八卦方位数据\nclass FengshuiCalculator {\n  constructor() {\n    this.baguaData = BAGUA_DATA;\n    this.wuxingRelations = WUXING_RELATIONS;\n    this.roomSuggestions = ROOM_SUGGESTIONS;\n  }\n  async calculate(params) {\n    const { direction, houseType, buildYear, area } = params;\n\n    try {\n      // 计算八卦方位\n      const bagua = this.calculateBagua(direction);\n\n      // 计算吉凶方位\n      const positions = this.calculatePositions(direction, houseType);\n\n      // 生成布局建议\n      const layout = this.generateLayoutAdvice(positions.auspicious, area);\n\n      // 生成化解方案\n      const solutions = this.generateSolutions(positions.inauspicious);\n\n      return {\n        bagua,\n        positions,\n        layout,\n        solutions\n      };\n    } catch (error) {\n      console.error('风水计算错误:', error);\n      throw new Error('风水计算失败');\n    }\n  }\n  calculateBagua(direction) {\n    const bagua = [];\n    const mainDirection = this.getMainDirection(direction);\n    \n    for (const [name, data] of Object.entries(this.baguaData)) {\n      bagua.push({\n        name,\n        direction: data.direction,\n        element: data.element,\n        isActive: data.direction === mainDirection\n      });\n    }\n\n    return bagua;\n  }\n  getMainDirection(direction) {\n    // 从\"坐X朝Y\"格式中提取主要朝向\n    const match = direction.match(/朝(.*)/);\n    return match ? match[1] : '';\n  }"}