{"chunk": 44, "numChunks": 55, "fileHash": "fCZ6nFoNZXQqhvADUzMwGWD6jOU+ajj61B8BD3CwDgs=", "filePath": "pages/name-test/name-test.js", "content": "// 姓名测试页面逻辑\nPage({\n  calculateWuge(surnameStrokes, givenNameStrokes) {\n    const surname = Array.isArray(surnameStrokes) ? surnameStrokes : [surnameStrokes];\n    const givenName = Array.isArray(givenNameStrokes) ? givenNameStrokes : [givenNameStrokes];\n    // 天格：姓氏笔画数+1（单姓）或姓氏两字笔画数之和（复姓）\n    const tiange = surname.reduce((sum, stroke) => sum + stroke, 0) + (surname.length === 1 ? 1 : 0);\n    // 人格：姓氏最后一字 + 名字第一字\n    const renge = surname[surname.length - 1] + givenName[0];\n    // 地格：名字笔画数之和（如果单名则+1）\n    const dige = givenName.reduce((sum, stroke) => sum + stroke, 0) + (givenName.length === 1 ? 1 : 0);\n    // 外格：天格 + 地格 - 人格\n    const waige = tiange + dige - renge;\n    // 总格：所有笔画数之和\n    const zongge = surname.reduce((sum, stroke) => sum + stroke, 0) + givenName.reduce((sum, stroke) => sum + stroke, 0);"}