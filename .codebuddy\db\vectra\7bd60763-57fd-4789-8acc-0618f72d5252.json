{"chunk": 0, "numChunks": 7, "fileHash": "YmBtAc19/HOz7eUBhWxGsON9KNrEblPnxcHiHsXvXv8=", "filePath": "pages/community/community.wxss", "content": "/* pages/community/community.wxss */\n.container {\n  padding: 0;\n  background: #f5f5f5;\n  min-height: 100vh;\n  position: relative;\n  padding-bottom: 120rpx;\n}\n\n/* 搜索栏 */\n.search-bar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: #fff;\n  padding: 20rpx 30rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n}\n\n.search-input {\n  display: flex;\n  align-items: center;\n  background: #f5f5f5;\n  border-radius: 32rpx;\n  padding: 16rpx 24rpx;\n}\n\n.search-input icon {\n  margin-right: 16rpx;\n  color: #999;\n}\n\n.search-input input {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n\n/* 分类标签 */\n.category-tabs {\n  position: fixed;\n  top: 120rpx;\n  left: 0;\n  right: 0;\n  z-index: 99;\n  background: #fff;\n  display: flex;\n  padding: 20rpx 30rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n  white-space: nowrap;\n  overflow-x: auto;\n}\n\n.category-tab {\n  display: inline-block;\n  padding: 12rpx 32rpx;\n  margin-right: 20rpx;\n  font-size: 28rpx;\n  color: #666;\n  background: #f5f5f5;\n  border-radius: 28rpx;\n"}