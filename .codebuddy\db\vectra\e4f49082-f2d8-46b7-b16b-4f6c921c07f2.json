{"chunk": 1, "numChunks": 5, "fileHash": "yLT5DZWgzA1pMyoKDto/o26wX3DjjCYshSeMtgr/cn4=", "filePath": "pages/xingming/index.wxml", "content": "    <view class=\"form-item\">\n      <text class=\"label\">出生日期</text>\n      <picker mode=\"date\" value=\"{{birthDate}}\" bindchange=\"onBirthDateChange\">\n        <view class=\"picker {{birthDate ? '' : 'placeholder'}}\">\n          {{birthDate || '请选择出生日期'}}\n        </view>\n      </picker>\n    </view>\n\n    <!-- 出生时间 -->\n    <view class=\"form-item\">\n      <text class=\"label\">出生时间</text>\n      <picker mode=\"time\" value=\"{{birthTime}}\" bindchange=\"onBirthTimeChange\">\n        <view class=\"picker {{birthTime ? '' : 'placeholder'}}\">\n          {{birthTime || '请选择出生时间'}}\n        </view>\n      </picker>\n    </view>\n  </view>\n\n  <!-- 提交按钮 -->\n  <view class=\"submit-section\">\n    <button class=\"submit-btn {{canSubmit ? '' : 'disabled'}}\" bindtap=\"onAnalyze\" disabled=\"{{!canSubmit}}\">\n      <text>开始测算</text>\n      <text class=\"price\" wx:if=\"{{price > 0}}\">￥{{price}}</text>\n    </button>\n  </view>\n\n  <!-- 分析结果 -->\n  <view class=\"result-section\" wx:if=\"{{showResult}}\">\n    <!-- 姓名评分 -->\n    <view class=\"analysis-card\">\n      <view class=\"card-title\">\n        <image class=\"title-icon\" src=\"/assets/icons/score.png\"/>\n        <text>姓名评分</text>\n      </view>\n      <view class=\"score-display\">\n        <view class=\"score\">{{totalScore}}</view>\n        <view class=\"max-score\">/100</view>\n      </view>\n      <view class=\"score-desc\">{{scoreDesc}}</view>\n    </view>\n\n    <!-- 五格数理 -->\n"}