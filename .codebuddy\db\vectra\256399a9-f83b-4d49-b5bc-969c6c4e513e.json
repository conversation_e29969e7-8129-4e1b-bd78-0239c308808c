{"chunk": 5, "numChunks": 19, "fileHash": "PEPeuJm140RAD7VBd85WlnsfcWUdoorqVSQj8ISByl0=", "filePath": "pages/ai-chat/ai-chat.wxss", "content": "  top: -4rpx;\n  left: -4rpx;\n  right: -4rpx;\n  bottom: -4rpx;\n  background: radial-gradient(circle, rgba(121, 40, 202, 0.3) 0%, transparent 70%);\n  border-radius: 50%;\n  animation: glow-pulse 2s ease-in-out infinite;\n}\n\n.avatar-text {\n  position: relative;\n  z-index: 1;\n}\n\n.avatar-icon {\n  width: 40rpx;\n  height: 40rpx;\n  position: relative;\n  z-index: 1;\n}\n\n/* 消息内容 */\n.message-content {\n  max-width: 70%;\n  position: relative;\n}\n\n.user-message .message-content {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 20rpx 24rpx;\n  border-radius: 24rpx 24rpx 8rpx 24rpx;\n  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);\n}\n\n.ai-message .message-content {\n  background: white;\n  color: #1f2937;\n  padding: 20rpx 24rpx;\n  border-radius: 24rpx 24rpx 24rpx 8rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);\n  border: 1rpx solid rgba(0, 0, 0, 0.05);\n}\n\n.message-text {\n  font-size: 30rpx;\n  line-height: 1.6;\n  word-wrap: break-word;\n  white-space: pre-wrap;\n}\n\n/* 消息操作按钮 */\n"}