{"chunk": 13, "numChunks": 38, "fileHash": "DpyinnhZJO6jrs0/VY0BXwrNjORlEQ1BeRywBJybhxQ=", "filePath": "pages/ai-chat/ai-chat.js", "content": "const app = getApp()\nPage({\n  async onQuickActionTap(e) {\n    const { id } = e.currentTarget.dataset\n    const action = this.data.quickActions.find(item => item.id === id)\n    if (!action) return\n    // 检查是否需要出生信息\n    if (action.requiresBirthInfo && !globalState.hasBirthInfo()) {\n      this.addMessage({\n        type: 'ai',\n        content: '该功能需要您的出生信息才能提供准确分析。请先完善您的出生信息。',\n        timestamp: this.formatTime(new Date()),\n        actions: [{\n          type: 'goto_birth_info',\n          text: '去填写出生信息'\n        }]\n      })\n      return\n    }"}