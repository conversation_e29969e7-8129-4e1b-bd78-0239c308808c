{"chunk": 6, "numChunks": 12, "fileHash": "5X5f8yfnxquoBxYzBGhnUPxPUBZeZKQywMAReox6cBk=", "filePath": "pages/bazi/index.js", "content": "const app = getApp()\nPage({\n  updateYunshiAnalysis(yunshi) {\n    const { currentTab } = this.data\n    this.yunshiData = yunshi\n\n    this.setData({\n      yunshiDetails: yunshi[currentTab].details\n    })\n  },\n  // 绘制图表\n  drawCharts() {\n    const { wuxingAnalysis } = this.data\n    \n    // 绘制五行图\n    this.wuxingChart.draw('wuxingCanvas', wuxingAnalysis)\n    \n    // 绘制运势图\n    this.updateYunshiChart()\n  },\n  // 更新运势图表\n  updateYunshiChart() {\n    const { currentTab } = this.data\n    const data = this.yunshiData[currentTab].data\n    this.yunshiChart.draw('yunshiCanvas', data)\n  },\n  // 分享功能\n  onShareAppMessage() {\n    return {\n      title: `${this.data.name}的八字分析结果`,\n      path: '/pages/bazi/index',\n      imageUrl: '/assets/images/share-cover.jpg'\n    }\n  },\n  // 页面卸载\n  onUnload() {\n    // 清理图表实例\n    if (this.wuxingChart) {\n      this.wuxingChart.dispose()\n    }\n    if (this.yunshiChart) {\n      this.yunshiChart.dispose()\n    }\n  },"}