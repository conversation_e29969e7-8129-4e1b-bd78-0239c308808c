{"chunk": 0, "numChunks": 22, "fileHash": "jr30LZOMc1P5VIpMptyaGnan9xv44m/c09rHA5aMeTY=", "filePath": "pages/index/index.js", "content": "// index.js\nconst app = getApp()\nconst defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'\nconst { signInManager, recentUsedManager, pointsManager } = require('../../utils/userState');\nconst { getTodaySolarTerm } = require('../../utils/solarTerms');\nconst { getTodayInfo } = require('../../utils/lunar');\nconst globalState = require('../../utils/global-state');\nconst navigationManager = require('../../utils/navigation');\nconst errorHandler = require('../../utils/error-handler');"}