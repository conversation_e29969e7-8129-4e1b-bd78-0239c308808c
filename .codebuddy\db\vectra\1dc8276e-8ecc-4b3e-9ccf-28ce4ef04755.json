{"chunk": 6, "numChunks": 13, "fileHash": "RdYUonhiW47SLv0/ZKSqV2JJEqqe4g6tc2PZPdqonRM=", "filePath": "pages/post-detail/post-detail.js", "content": "const app = getApp()\nPage({\n  async loadComments(postId, isLoadMore = false) {\n    if (this.data.isLoadingComments) return\n    \n    this.setData({ isLoadingComments: true })\n    \n    try {\n      const db = wx.cloud.database()\n      const { commentPageNum, commentPageSize } = this.data\n      \n      const commentsResult = await db.collection('comments')\n        .where({\n          postId: postId\n        })\n        .orderBy('createTime', 'desc')\n        .skip((commentPageNum - 1) * commentPageSize)\n        .limit(commentPageSize)\n        .get()\n      \n      // 格式化时间\n      const newComments = commentsResult.data.map(comment => {\n        comment.createTime = new Date(comment.createTime).toLocaleString()\n        return comment\n      })\n      \n      // 获取评论总数\n      const countResult = await db.collection('comments')\n        .where({\n          postId: postId\n        })\n        .count()\n      \n      const hasMoreComments = this.data.comments.length + newComments.length < countResult.total\n      \n      this.setData({\n        comments: isLoadMore ? [...this.data.comments, ...newComments] : newComments,\n        hasMoreComments,\n        commentPageNum: isLoadMore ? this.data.commentPageNum + 1 : 1,\n        isLoadingComments: false\n      })\n    } catch (error) {\n      console.error('获取评论列表失败:', error)\n      this.setData({ isLoadingComments: false })\n      wx.showToast({\n        title: '获取评论失败',\n        icon: 'none'\n      })\n    }\n  },\n  /**\n   * 加载更多评论\n   */\n  loadMoreComments() {\n    if (this.data.hasMoreComments && !this.data.isLoadingComments) {\n      this.loadComments(this.data.post.id, true)\n    }\n  },\n  /**\n   * 预览图片\n   */"}