{"chunk": 6, "numChunks": 15, "fileHash": "ydfcj+4tZs2HvARUdw6TErn3fg1suYaeqU0s+F1D6wI=", "filePath": "pages/hehun/index.js", "content": "const app = getApp()\nPage({\n  drawWuxingChart(data) {\n    this.wuxingChart.draw('wuxingCanvas', data)\n  },\n  // 分享功能\n  onShareAppMessage() {\n    const { male, female, matchScore } = this.data\n    return {\n      title: `${male.name}和${female.name}的合婚测算结果：${matchScore}分`,\n      path: '/pages/hehun/index',\n      imageUrl: '/assets/images/share-cover.jpg'\n    }\n  },\n  // 页面卸载\n  onUnload() {\n    // 清理图表实例\n    if (this.wuxingChart) {\n      this.wuxingChart.dispose()\n    }\n  },\n  onMaleBirthDateChange(e) {\n    this.setData({\n      maleBirthDate: e.detail.value\n    });\n  },\n  onMaleBirthTimeChange(e) {\n    this.setData({\n      maleBirthTime: e.detail.value\n    });\n  },\n  onFemaleBirthDateChange(e) {\n    this.setData({\n      femaleBirthDate: e.detail.value\n    });\n  },\n  onFemaleBirthTimeChange(e) {\n    this.setData({\n      femaleBirthTime: e.detail.value\n    });\n  },"}