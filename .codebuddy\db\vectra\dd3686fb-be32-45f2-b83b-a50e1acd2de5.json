{"chunk": 3, "numChunks": 4, "fileHash": "0WJlPsLtY6dByumgky6y0geRpquXpBvPySRi7WFItpI=", "filePath": "pages/bazi/bazi.wxml", "content": "        <text class=\"interpretation-content\">{{baziResult.interpretation.health}}</text>\n      </view>\n    </view>\n\n    <!-- 大运流年 -->\n    <view class=\"section-title\">大运流年</view>\n    <view class=\"fortune\">\n      <scroll-view class=\"fortune-scroll\" scroll-x=\"true\">\n        <view class=\"fortune-item\" wx:for=\"{{baziResult.fortune}}\" wx:key=\"age\">\n          <text class=\"fortune-age\">{{item.age}}岁</text>\n          <text class=\"fortune-year\">{{item.year}}年</text>\n          <text class=\"fortune-pillar\">{{item.pillar}}</text>\n          <text class=\"fortune-shishen\">{{item.shishen}}</text>\n          <text class=\"fortune-luck {{item.luck === '吉' ? 'ji' : item.luck === '凶' ? 'xiong' : 'ping'}}\">{{item.luck}}</text>\n        </view>\n      </scroll-view>\n    </view>\n  </block>\n\n  <!-- 空状态提示 -->\n  <view class=\"empty-tip\" wx:if=\"{{!birthInfo && !loading && !error}}\">\n    <text class=\"tip-text\">请先输入出生信息\\n点击右上角\"信息\"按钮开始</text>\n  </view>\n</view> \n"}