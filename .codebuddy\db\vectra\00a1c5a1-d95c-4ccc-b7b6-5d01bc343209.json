{"chunk": 3, "numChunks": 5, "fileHash": "SwGl0Osw+Il/Fk4wMhJRhmGve8PVm+UDhwwtYP7bA2g=", "filePath": "pages/community/index.js", "content": "const app = getApp()\nPage({\n  data: {\n    // 帖子列表\n    posts: [],\n    // 当前选中的分类\n    currentCategory: 'all',\n    // 分类列表\n    categories: [\n      { id: 'all', name: '全部' },\n      { id: 'bazi', name: '八字' },\n      { id: 'fengshu<PERSON>', name: '风水' },\n      { id: 'marriage', name: '姻缘' },\n      { id: 'yijing', name: '易经' }\n    ],\n    // 加载状态\n    loading: false,\n    // 是否还有更多数据\n    hasMore: true,\n    // 当前页码\n    page: 1,\n    // 每页数量\n    pageSize: 10\n  },\n  onLoad() {\n    this.loadPosts()\n  },\n  onShow() {\n    if (typeof this.getTabBar === 'function' && this.getTabBar()) {\n      this.getTabBar().setData({\n        selected: 2  // 社区的 tabBar 索引\n      })\n    }\n  },\n  // 切换分类\n  switchCategory(e) {\n    const { category } = e.currentTarget.dataset\n    this.setData({\n      currentCategory: category,\n      posts: [],\n      page: 1,\n      hasMore: true\n    }, () => {\n      this.loadPosts()\n    })\n  },\n  // 加载帖子列表"}