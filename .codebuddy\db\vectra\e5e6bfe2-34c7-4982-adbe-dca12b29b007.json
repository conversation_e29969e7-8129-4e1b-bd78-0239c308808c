{"chunk": 71, "numChunks": 76, "fileHash": "lPYWZE8QItgwSyEsMXHtl6y/HYBCI8BdGaV+1l8ICMA=", "filePath": "docs/md/API_INTERFACE_DOCUMENTATION.md", "content": "  reviewed_by <PERSON><PERSON><PERSON><PERSON>(36),\n  reviewed_at DATETIME,\n  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n\n  INDEX idx_category (category),\n  INDEX idx_is_active (is_active),\n  INDEX idx_difficulty (difficulty),\n  INDEX idx_usage_count (usage_count),\n  INDEX idx_success_rate (success_rate),\n  FULLTEXT idx_qa_search (question, answer)\n);\n```\n\n#### 13. 知识库搜索日志表 (knowledge_search_logs)\n```sql\nCREATE TABLE knowledge_search_logs (\n  id VARCHAR(36) PRIMARY KEY,\n  user_id VARCHAR(36),\n  session_id VARCHAR(36),\n  query TEXT NOT NULL,\n  search_type ENUM('semantic', 'keyword', 'hybrid') DEFAULT 'hybrid',\n  results_count INT DEFAULT 0,\n  top_result_id VARCHAR(36),\n  top_result_type <PERSON>NUM('document', 'qa_pair'),\n  click_through BOOLEAN DEFAULT FALSE,\n  user_feedback ENUM('helpful', 'not_helpful', 'irrelevant'),\n  response_time INT,\n  search_filters JSON,\n  results_data JSON,\n  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n\n  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,\n  INDEX idx_user_id (user_id),\n  INDEX idx_session_id (session_id),\n  INDEX idx_search_type (search_type),\n  INDEX idx_created_at (created_at),\n  INDEX idx_user_feedback (user_feedback),\n  FULLTEXT idx_query_search (query)\n);\n```\n\n#### 14. 知识库用户收藏表 (knowledge_bookmarks)\n```sql\nCREATE TABLE knowledge_bookmarks (\n  id VARCHAR(36) PRIMARY KEY,\n  user_id VARCHAR(36) NOT NULL,\n  content_id VARCHAR(36) NOT NULL,\n  content_type ENUM('document', 'qa_pair') NOT NULL,\n  notes TEXT,\n  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n"}