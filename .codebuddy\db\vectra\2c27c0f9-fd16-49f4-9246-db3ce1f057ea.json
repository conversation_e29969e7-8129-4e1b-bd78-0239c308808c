{"chunk": 0, "numChunks": 4, "fileHash": "DpTd8f7+cZkfi/n8agYLW+BkjecanzloeqTjzVIZsfQ=", "filePath": "pages/customer-service/customer-service.wxml", "content": "<!--客服页面-->\n<view class=\"page-container\">\n  <view class=\"header\">\n    <image class=\"header-bg\" src=\"/assets/images/customer-service-bg.png\" mode=\"aspectFill\" />\n    <view class=\"header-content\">\n      <view class=\"title\">客服中心</view>\n      <view class=\"subtitle\">7×24小时为您服务</view>\n    </view>\n  </view>\n\n  <!-- 联系方式 -->\n  <view class=\"contact-section\">\n    <view class=\"section-title\">联系我们</view>\n    <view class=\"contact-grid\">\n      <view class=\"contact-item\" bindtap=\"contactWechat\">\n        <view class=\"contact-icon wechat\">💬</view>\n        <view class=\"contact-info\">\n          <text class=\"contact-title\">微信客服</text>\n          <text class=\"contact-desc\">在线咨询</text>\n        </view>\n        <view class=\"contact-status online\">在线</view>\n      </view>\n      \n      <view class=\"contact-item\" bindtap=\"contactPhone\">\n        <view class=\"contact-icon phone\">📞</view>\n        <view class=\"contact-info\">\n          <text class=\"contact-title\">电话客服</text>\n          <text class=\"contact-desc\">************</text>\n        </view>\n        <view class=\"contact-arrow\">></view>\n      </view>\n      \n      <view class=\"contact-item\" bindtap=\"contactEmail\">\n        <view class=\"contact-icon email\">📧</view>\n        <view class=\"contact-info\">\n          <text class=\"contact-title\">邮件客服</text>\n          <text class=\"contact-desc\"><EMAIL></text>\n        </view>\n        <view class=\"contact-arrow\">></view>\n      </view>\n      \n"}