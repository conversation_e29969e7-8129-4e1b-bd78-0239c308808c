{"chunk": 0, "numChunks": 6, "fileHash": "VToXv7ATbmi0am+Amh5z0qVhRJK7IjhEVx3t5LgFVgM=", "filePath": "pages/marriage/index.wxml", "content": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<view class=\"container\">\n  <!-- 顶部标题区域 -->\n  <view class=\"header\" wx:if=\"{{!showResult}}\">\n    <view class=\"header-content\">\n      <view class=\"title\">八字合婚</view>\n      <view class=\"subtitle\">测算两人姻缘匹配度，探寻天作之合</view>\n    </view>\n    <view class=\"header-decoration\">\n      <text class=\"icon\">💕</text>\n    </view>\n  </view>\n\n  <!-- 表单区域 -->\n  <view class=\"form-container\" wx:if=\"{{!showResult}}\">\n    <!-- 男方信息卡片 -->\n    <view class=\"info-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">男方信息</text>\n        <text class=\"card-icon\">👨</text>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">出生日期</text>\n          <text class=\"required\">*</text>\n        </view>\n        <picker mode=\"date\" value=\"{{male.date}}\" bindchange=\"bindMaleDateChange\">\n          <view class=\"picker-wrapper\">\n            <text class=\"picker-text {{male.date ? 'selected' : 'placeholder'}}\">\n              {{male.date || '请选择出生日期'}}\n            </text>\n            <text class=\"picker-icon\">📅</text>\n          </view>\n        </picker>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"item-header\">\n          <text class=\"label\">出生时间</text>\n          <text class=\"required\">*</text>\n        </view>\n"}