{"chunk": 10, "numChunks": 11, "fileHash": "i8jYYz/gAcuprKem1BBbiBaSat5ZFs+bjwiR2/ZQErE=", "filePath": "pages/birth-info/birth-info.js", "content": "const globalState = require('../../utils/global-state')\nPage({\n  saveInfo() {\n    try {\n      // 构建出生信息对象\n      const birthInfo = {\n        name: name.trim(),\n        gender,\n        zodiacIndex,\n        numberIndex,\n        zodiac: zodiacIndex !== null ? zodiacList[zodiacIndex] : null,\n        number: numberIndex !== null ? numberList[numberIndex] : null,\n        selectedDateTime,\n        dateTimeArray,\n        dateTime: dateTime.map((arr, index) => arr[dateTimeArray[index]]).join(''),\n        timestamp: new Date().getTime()\n      };\n      console.log('保存的出生信息:', birthInfo);\n      // 使用全局状态管理器保存\n      globalState.updateBirthInfo(birthInfo);\n      wx.showToast({\n        title: '保存成功',\n        icon: 'success',\n        duration: 1500\n      });\n      // 获取目标页面路径\n      const targetPage = wx.getStorageSync('targetPage');"}