{"chunk": 0, "numChunks": 6, "fileHash": "YXf85xx1jxmB9as0Va5OH90PaK79Xnt82sfEgjKj2bE=", "filePath": "pages/post-detail/post-detail.wxss", "content": "/* pages/post-detail/post-detail.wxss */\n.page-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f5f0ff 0%, #e8dff5 100%);\n  display: flex;\n  flex-direction: column;\n}\n\n.content-scroll {\n  flex: 1;\n  padding-bottom: 120rpx;\n}\n\n/* 文章头图 */\n.article-header {\n  position: relative;\n  height: 400rpx;\n  overflow: hidden;\n}\n\n.cover-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.header-mask {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.6) 100%);\n}\n\n.header-info {\n  position: absolute;\n  bottom: 30rpx;\n  left: 30rpx;\n  right: 30rpx;\n  color: white;\n}\n\n.article-category {\n  display: inline-block;\n  padding: 8rpx 20rpx;\n  background: rgba(149, 117, 205, 0.8);\n  border-radius: 20rpx;\n  font-size: 24rpx;\n  margin-bottom: 15rpx;\n}\n\n.article-title {\n  display: block;\n  font-size: 42rpx;\n  font-weight: bold;\n  line-height: 1.4;\n  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);\n}\n\n/* 作者信息 */\n.author-section {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 30rpx;\n  background: white;\n"}