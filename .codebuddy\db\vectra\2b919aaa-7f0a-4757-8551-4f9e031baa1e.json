{"chunk": 11, "numChunks": 12, "fileHash": "a4LdV5V9/sM95i4esh3ydW49XOc5FHg6OAvfDYJ8/so=", "filePath": "pages/name/index.js", "content": "const { NameCalculator } = require('../../utils/name/calculator');\nPage({\n  },\n  validateInput() {\n    if (!this.data.surname.trim()) {\n      wx.showToast({\n        title: '请输入姓氏',\n        icon: 'none'\n      });\n      return false;\n    }\n    if (!this.data.givenName.trim()) {\n      wx.showToast({\n        title: '请输入名字',\n        icon: 'none'\n      });\n      return false;\n    }\n    if (!this.data.birthDate) {\n      wx.showToast({\n        title: '请选择出生日期',\n        icon: 'none'\n      });\n      return false;\n    }\n    return true;\n  },\n  getStars(value) {\n    // 将0-100的分数转换为1-5颗星\n    const starCount = Math.ceil(value / 20);\n    return new Array(starCount).fill('★');\n  },"}